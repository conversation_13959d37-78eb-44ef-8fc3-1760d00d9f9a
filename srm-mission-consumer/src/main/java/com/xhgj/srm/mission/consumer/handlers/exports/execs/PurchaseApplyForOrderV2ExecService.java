package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.exportfiled.ExportFiledSelectDTO;
import com.xhgj.srm.common.enums.OrderGoodsStateV2Enum;
import com.xhgj.srm.common.enums.PurchaseApplicationTypeEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.PurchaseApplyItem;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.PermissionTypeRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyItemRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.service.ShareSupplierOrderService;
import com.xhgj.srm.service.ShareSupplierOrderToFormService;
import com.xhgj.srm.service.ShareUserToGroupService;
import com.xhgj.srm.v2.dao.PurchaseApplyForOrderV2Dao;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *PurchaseApplyForOrderV2ExecService
 */
@Slf4j
@Component
public class PurchaseApplyForOrderV2ExecService implements ExecService {
  @Resource
  private PermissionTypeRepository permissionTypeRepository;
  @Resource
  private ExportUtil ex;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private ShareUserToGroupService shareUserToGroupService;
  @Resource
  private PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  private PurchaseApplyForOrderV2Dao purchaseApplyForOrderV2Dao;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  private ShareSupplierOrderToFormService supplierOrderToFormService;
  @Resource
  private ShareSupplierOrderService supplierOrderService;
  @Resource
  private PurchaseApplyItemRepository purchaseApplyItemRepository;
  @Resource
  private PurchaseApplyRecordRepository purchaseApplyRecordRepository;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;

  @Override
  @SneakyThrows
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    JSONObject jsonObject = JSON.parseObject(dispatchParam.getParams());
    String exportField = jsonObject.getString("exportField");
    List<ExportFiledSelectDTO> exportFiledSelectDTOS = new ArrayList<>();
    List<String> infoList = new ArrayList<>();
    infoList.add("采购申请单号");
    List<String> productInfoList = new ArrayList<>();
    productInfoList.add("物料编码");
    //组件信息
    List<String> componentInfoList = ListUtil.toList("采购申请单号", "采购申请类型", "物料编码", "物料序号");
    List<String> titleList = new ArrayList<>();
    if(StrUtil.isNotBlank(exportField)){
      exportFiledSelectDTOS =
          JSONObject.parseObject(exportField, new TypeReference<List<ExportFiledSelectDTO>>() {});
    }
    for (int i = 0; i < exportFiledSelectDTOS.size(); i++) {
      ExportFiledSelectDTO exportFiledSelectDTO = exportFiledSelectDTOS.get(i);
      List<String> fieldNameList = CollUtil.emptyIfNull(exportFiledSelectDTO.getChildList()).stream()
          .filter(exportFiledSelectDTO1 -> BooleanUtil.isTrue(exportFiledSelectDTO1.getSelect()))
          .map(ExportFiledSelectDTO::getName).collect(Collectors.toList());
      if (i==0) {
        infoList.addAll(fieldNameList);
      }
      if (i==1) {
        productInfoList.addAll(fieldNameList);
      }
      if (i==2) {
        componentInfoList.addAll(fieldNameList);
      }
    }
    titleList.addAll(infoList);
    titleList.addAll(productInfoList);
//    titleList.addAll(componentInfoList);
    XSSFWorkbook book = new XSSFWorkbook();
    CellStyle baseStyle = ex.getBaseStyle(book);
    CellStyle titleStyle = ex.getTitleStyle(book);
    titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
    titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    // 写入国内供应商
    List<Integer> title =titleList.stream().map(name->30).collect(Collectors.toList());
    List<Integer> componentTitle = componentInfoList.stream().map(name->30).collect(Collectors.toList());
    List<String> titles = new ArrayList<>(titleList);
    List<String> componentTitles = new ArrayList<>(componentInfoList);
    Sheet sheet = ex.createSheet(book, "采购申请订单(产业)", title);
    Row rowTitle = sheet.createRow(0);
    ex.createTitle(titles, titleStyle, rowTitle);
    Sheet componentSheet = ex.createSheet(book, "组件清单", componentTitle);
    Row componentRowTitle = componentSheet.createRow(0);
    ex.createTitle(componentTitles, titleStyle, componentRowTitle);


    List<PurchaseApplyForOrderV2> purchaseApplyForOrders = (List<PurchaseApplyForOrderV2>) collection;
    int index = 1;
    int componentIndex = 1;
    int count = 0;
    // 每3000条处理一次
    for (int j = 0; j < purchaseApplyForOrders.size(); j += 3000) {
      // 分割3000
      List<PurchaseApplyForOrderV2> splitList = purchaseApplyForOrders.subList(j, Math.min(j + 3000, purchaseApplyForOrders.size()));
      // 获取erpCodes
      List<String> erpCodes = splitList.stream().map(PurchaseApplyForOrderV2::getPurchaseDepartment)
          .filter(Objects::nonNull).distinct().collect(Collectors.toList());
      erpCodes.add("-1");
      List<Group> groupList =
          groupRepository.findAllByErpCodeInAndState(erpCodes, Constants.STATE_OK);
      // 去重groupList
      Map<String, Group> groupMap = groupList.stream()
          .collect(Collectors.toMap(Group::getErpCode, Function.identity(), (key1, key2) -> key2));
      // 获取orderNos
      List<String> applyForNo = splitList.stream().map(PurchaseApplyForOrderV2::getApplyForOrderNo)
          .filter(Objects::nonNull).distinct().collect(Collectors.toList());
      Map<String, String> purchaseOrderNoMap = getPurchaseOrderNoMap(applyForNo);
      //采购申请订单
      for (PurchaseApplyForOrderV2 purchaseApplyForOrder : splitList) {
        Row row = sheet.createRow(index);
        int rowNum = index + 1;
        for (int i = 0; i < titleList.size(); i++) {
          String filedName = titleList.get(i);
          if ("采购申请单号".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getApplyForOrderNo(), baseStyle);
          }
          if ("物料编码".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getProductCode(), baseStyle);
          }
          //后续接入BootDictDetailService
          if ("采购申请类型".equals(filedName)) {
            PurchaseApplicationTypeEnum purchaseApplicationTypeEnum =
                PurchaseApplicationTypeEnum.fromCode(purchaseApplyForOrder.getApplyForType());
            ex.createCell(row, i, purchaseApplicationTypeEnum == null ? StrUtil.EMPTY
                : purchaseApplicationTypeEnum.getName(), baseStyle);
          }
          if ("订货状态".equals(filedName)) {
            //订货状态
            OrderGoodsStateV2Enum orderGoodsStateV2Enum =
                OrderGoodsStateV2Enum.fromKey(purchaseApplyForOrder.getOrderGoodsState());
            ex.createCell(row, i, orderGoodsStateV2Enum!=null?orderGoodsStateV2Enum.getValue():StrUtil.EMPTY, baseStyle);
          }
          if ("取消状态".equals(filedName)) {
            //取消状态
            ex.createCell(row, i, Constants.YES_OR_NO.get(
                StrUtil.blankToDefault(purchaseApplyForOrder.getCancellationState(),
                    com.xhiot.boot.core.common.constants.Constants.NO)), baseStyle);
          }
          if ("创建日期".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getCreateTime() == null ? StrUtil.EMPTY
                    : DateUtils.formatTimeStampToNormalDateTime(purchaseApplyForOrder.getCreateTime()),
                baseStyle);
          }
          if ("客户订单号".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getCustomerOrderNumber(), baseStyle);
          }
          if ("销售订单号".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSaleOrderNo(), baseStyle);
          }
          if ("跟单员".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getFollowUpPersonName(), baseStyle);
          }
          if ("业务员".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSalesman(), baseStyle);
          }
          if ("销售组织".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSalesOrganization(), baseStyle);
          }
          if ("采购组织".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getPurchasingOrganization(), baseStyle);
          }
          if ("项目编码".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getProjectNo(), baseStyle);
          }
          if ("项目名称".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getProjectName(), baseStyle);
          }
          if ("关联采购订单".equals(filedName)) {
            String orderNos = purchaseOrderNoMap.get(purchaseApplyForOrder.getApplyForOrderNo());
            ex.createCell(row, i, orderNos, baseStyle);
          }
          if ("售达方".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSoldToParty(), baseStyle);
          }
          if ("收件人".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getConsignee(), baseStyle);
          }
          if ("联系方式".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getContactInformation(), baseStyle);
          }
          if ("收件地址".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getDeliveryAddress(), baseStyle);
          }
          if ("发货方式".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getDeliveryType(), baseStyle);
          }
          if ("申请单备注".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getApplicationFormRemarks(), baseStyle);
          }
          if ("修改记录".equals(filedName)) {
            String haveModifyRecord = purchaseApplyRecordRepository.existsByPurchaseApplyIdAndState(
                purchaseApplyForOrder.getId(), Constants.STATE_OK) ? "是" : "否";
            ex.createCell(row, i, haveModifyRecord, baseStyle);
          }
          if ("组件信息".equals(filedName)) {
            String haveComponentManifest = purchaseApplyItemRepository.existsByPurchaseApplyIdAndState(
                purchaseApplyForOrder.getId(), Constants.STATE_OK) ? "是" : "否";
            ex.createCell(row, i, haveComponentManifest, baseStyle);
          }
          if ("科目分配类别".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getAssignmentCategory(), baseStyle);
          }
          if ("总账科目".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getLedgerSubject(), baseStyle);
          }
          if ("成本中心".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getCostCenter(), baseStyle);
          }
          if ("订单".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getOrder(), baseStyle);
          }
          if ("交货日期".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getDeliverTime() == null ? StrUtil.EMPTY
                : DateUtils.formatTimeStampToNormalDateTime(purchaseApplyForOrder.getDeliverTime()), baseStyle);
          }
          if ("物料组".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getItemGroup(), baseStyle);
          }
          if ("固定的供应商".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getFixedVendorName(), baseStyle);
          }
          //物料明细
          if ("物料序号".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSerialNumber(), baseStyle);
          }
          if ("资产卡片".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getProfileCard(), baseStyle);
          }
          if ("物料名称".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getProductName(), baseStyle);
          }
          if ("品牌".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getBrand(), baseStyle);
          }
          if ("型号".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getModel(), baseStyle);
          }
          if ("规格".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getSpecification(), baseStyle);
          }
          if ("单位".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getUnitName(), baseStyle);
          }
          if ("描述".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getMaterialDescription(), baseStyle);
          }
          if ("申请数量".equals(filedName)) {
            ex.createCell(row, i, BigDecimalUtil.formatForStandard(purchaseApplyForOrder.getApplyForNumber()).toPlainString(), baseStyle);
          }
          if ("已订货数量".equals(filedName)) {
            ex.createCell(row, i,
                Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber()).orElse(
                        BigDecimal.ZERO)
                    .toPlainString(), baseStyle);
          }
          if ("销售单价".equals(filedName)) {
            ex.createCell(row, i,Optional.ofNullable(purchaseApplyForOrder.getSalesUnitPrice()).orElse(BigDecimal.ZERO)
                .toPlainString(), baseStyle);
          }
          if ("销售需求数量".equals(filedName)) {
            ex.createCell(row, i,Optional.ofNullable(purchaseApplyForOrder.getSalesDemandQuantity()).orElse(BigDecimal.ZERO)
                .toPlainString(), baseStyle);
          }
          if ("仓库".equals(filedName)) {
            String warehouseName = inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                purchaseApplyForOrder.getFactoryCode(), purchaseApplyForOrder.getWarehouse(),
                Constants.STATE_OK).map(InventoryLocation::getWarehouseName).orElse(StrUtil.EMPTY);
            ex.createCell(row, i, warehouseName, baseStyle);
          }
          if ("MPM参考结算价".equals(filedName)) {
            ex.createCell(row, i,purchaseApplyForOrder.getMpmReferenceSettlementPrice() == null ? StrUtil.EMPTY
                : purchaseApplyForOrder.getMpmReferenceSettlementPrice().stripTrailingZeros()
                    .toPlainString(), baseStyle);
          }
          if ("计划需求日期".equals(filedName)) {
            if (purchaseApplyForOrder.getPlanDemandDate() == null) {
              ex.createCell(row, i, StrUtil.EMPTY, baseStyle);
            } else {
              ex.createCell(row, i,
                  DateUtil.format(DateTime.of(purchaseApplyForOrder.getPlanDemandDate()), "yyyy-MM-dd"),
                  baseStyle);
            }
          }
          if ("采购员".equals(filedName)) {
            ex.createCell(row, i, purchaseApplyForOrder.getPurchaseManMix(), baseStyle);
          }
          if ("采购部门".equals(filedName)) {
            String purchaseDepartment = purchaseApplyForOrder.getPurchaseDepartment();
            if (StrUtil.isNotBlank(purchaseDepartment)) {
              Group group = groupMap.get(purchaseDepartment);
              ex.createCell(row, i, group != null ? group.getName() : StrUtil.EMPTY, baseStyle);
            } else {
              ex.createCell(row, i, StrUtil.EMPTY, baseStyle);
            }
          }
        }
        index++;
        count++;
        fcMissionService.createMissionDetail(
            dispatchParam.getMissionId(), "第【" + rowNum + "】行", StrUtil.EMPTY);
      }

      //组件清单
      for (PurchaseApplyForOrderV2 purchaseApplyForOrder : splitList) {
        //一个采购申请单对应多个组件清单明细
        List<PurchaseApplyItem> purchaseApplyItemList =
            purchaseApplyItemRepository.findAllByPurchaseApplyIdAndState(
                purchaseApplyForOrder.getId(), Constants.STATE_OK);
        for (PurchaseApplyItem purchaseApplyItem : purchaseApplyItemList) {
          Row row = componentSheet.createRow(componentIndex);
          for (int i = 0; i < componentInfoList.size(); i++) {
            String filedName = componentInfoList.get(i);
            //采购申请单号
            if ("采购申请单号".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyForOrder.getApplyForOrderNo(), baseStyle);
            }
            //后续接入BootDictDetailService
            if ("采购申请类型".equals(filedName)) {
              PurchaseApplicationTypeEnum purchaseApplicationTypeEnum =
                  PurchaseApplicationTypeEnum.fromCode(purchaseApplyForOrder.getApplyForType());
              ex.createCell(row, i, purchaseApplicationTypeEnum == null ? StrUtil.EMPTY
                  : purchaseApplicationTypeEnum.getName(), baseStyle);
            }
            if ("物料编码".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyForOrder.getProductCode(), baseStyle);
            }
            if ("物料序号".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyForOrder.getSerialNumber(), baseStyle);
            }
            if ("项目编号".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getProjectNo(), baseStyle);
            }
            if ("组件物料编码".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentProductCode(), baseStyle);
            }
            if ("物料名称".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentProductName(), baseStyle);
            }
            if ("品牌".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentBrand(), baseStyle);
            }
            if ("单位".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentUnit(), baseStyle);
            }
            if ("需求数量".equals(filedName)) {
              ex.createCell(row, i, BigDecimalUtil.formatForStandard(purchaseApplyItem.getComponentQuantity()).toPlainString(), baseStyle);
            }
            if ("已使用数量".equals(filedName)) {
              ex.createCell(row, i,
                  BigDecimalUtil.formatForStandard(purchaseApplyItem.getUsedQuantity()).toPlainString(),
                  baseStyle);
            }
            if ("剩余数量".equals(filedName)) {
              ex.createCell(row, i,
                  BigDecimalUtil.formatForStandard(NumberUtil.sub(purchaseApplyItem.getComponentQuantity(),purchaseApplyItem.getUsedQuantity())).toPlainString(), baseStyle);
            }
            if ("规格".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentSpecification(), baseStyle);
            }
            if ("型号".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getComponentModel(), baseStyle);
            }
            if ("MRP类型".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getMrpType(), baseStyle);
            }
            if ("行项目类别".equals(filedName)) {
              ex.createCell(row, i, purchaseApplyItem.getLineItemCategory(), baseStyle);
            }
          }
          componentIndex++;
        }
      }

    }
    String now = String.valueOf(System.currentTimeMillis());
    String fileNewName = "采购申请单（产业）导出" + now + ".xlsx";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    book.write(outputStream);
    return ExportMissionCompleteResult.builder()
        .successCount(count)
        .fileName(fileNewName)
        .bytes(outputStream.toByteArray())
        .build();
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    Map<String, Object> queryMap =
        JSON.parseObject(dispatchParam.getParams(), new TypeReference<Map<String, Object>>() {});
    String userId = Convert.toStr(queryMap.get("userId"));
    List<String> purchaseDepartmentCodeList = Convert.toList(String.class, queryMap.get(
        "purchaseDepartmentCode"));
    List<String> ids = Convert.toList(String.class, queryMap.get("ids"));

        // 判断导出权限
        String permissionCode = Optional.ofNullable(
                permissionTypeRepository.getPermissionTypeByUserIdAndType(
                    userId,
                    Constants.USER_PERMISSION_EXPORT_APPLY_FOR_ORDER))
            .map(PermissionType::getPermissionCode).orElse(StrUtil.EMPTY);
    List<String> depteCurrUserList = new ArrayList<>();
    // 仅允许导出自己部门的数据
    if (Constants.EXPORT_DEPT_TYPE.equals(permissionCode)) {
      depteCurrUserList.addAll(shareUserToGroupService.getDeptErpCodeListByUser(userId));
      purchaseDepartmentCodeList.addAll(depteCurrUserList);
      queryMap.put("purchaseDepartmentCode",purchaseDepartmentCodeList);
    }
    List<PurchaseApplyForOrderV2> purchaseApplyForOrders = new ArrayList<>();
    if (CollUtil.isNotEmpty(ids)) {
      purchaseApplyForOrders = purchaseApplyForOrderV2Repository.findAllByIdIn(ids).stream().filter(
          order -> !Constants.EXPORT_DEPT_TYPE.equals(permissionCode) || depteCurrUserList.contains(
              order.getPurchaseDepartment())).collect(Collectors.toList());
    } else {
      Page<PurchaseApplyForOrderV2> page = purchaseApplyForOrderV2Dao.findPage(queryMap);
      purchaseApplyForOrders = page.getContent();
    }
    return purchaseApplyForOrders;
  }

  private Map<String, String> getPurchaseOrderNoMap(List<String> applyOrderNos) {
    Map<String, String> res = new HashMap<>();
    List<PurchaseApplyForOrderV2> purchaseApplyForOrders =
        purchaseApplyForOrderV2Repository.findAllByApplyForOrderNoInAndState(applyOrderNos,
            Constants.STATE_OK);
    List<String> purchaseApplyForOrderIds =
        purchaseApplyForOrders.stream().map(PurchaseApplyForOrderV2::getId).collect(Collectors.toList());
    purchaseApplyForOrderIds.add("-1");

    List<SupplierOrderDetail> orderDetails =
        supplierOrderDetailRepository.findAllByPurchaseApplyForOrderIdInAndState(
            purchaseApplyForOrderIds,Constants.STATE_OK);
    if (CollUtil.isEmpty(orderDetails)) {
      return new HashMap<>();
    }
    Map<String, List<SupplierOrderDetail>> id2SupplierOrderDetails =
        orderDetails.stream().collect(Collectors.groupingBy(SupplierOrderDetail::getId));

    for (PurchaseApplyForOrderV2 purchaseApplyForOrder : purchaseApplyForOrders) {
      List<SupplierOrderDetail> supplierOrderDetails =
          id2SupplierOrderDetails.get(purchaseApplyForOrder.getId());
      if (CollUtil.isEmpty(supplierOrderDetails)) {
        res.put(purchaseApplyForOrder.getApplyForOrderNo(), null);
        continue;
      }
      String orderNo = supplierOrderDetails.stream().map(supplierOrderDetail -> {
        String orderToFormId = supplierOrderDetail.getOrderToFormId();
        if (StrUtil.isNotBlank(orderToFormId)) {
          SupplierOrderToForm supplierOrderToForm = supplierOrderToFormService.get(orderToFormId,
              () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
          SupplierOrder supplierOrder =
              supplierOrderService.get(supplierOrderToForm.getSupplierOrderId(),
                  () -> CheckException.noFindException(SupplierOrder.class,
                      supplierOrderToForm.getSupplierOrderId()));
          String result = supplierOrder.getCode();
          if (StrUtil.isNotBlank(supplierOrder.getPurchaseMan())) {
            result = result + "(" + supplierOrder.getPurchaseMan() + ")";
          }
          return result;
        }
        return null;
      }).filter(Objects::nonNull).collect(Collectors.joining("、"));
      res.put(purchaseApplyForOrder.getApplyForOrderNo(), orderNo);
    }
    return res;
  }

}
