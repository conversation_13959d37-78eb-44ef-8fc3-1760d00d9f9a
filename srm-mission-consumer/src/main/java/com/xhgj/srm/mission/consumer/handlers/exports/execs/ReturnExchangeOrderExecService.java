package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.FileDTOExt;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.jpa.dao.ReturnExchangeOrderDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.service.ShareGroupService;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhiot.boot.core.common.util.DateUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/12 9:24
 * @Description: 退换单导出类
 */
@Service
public class ReturnExchangeOrderExecService implements ExecService {

  @Autowired
  private UserDao userDao;
  @Autowired
  private SharePermissionTypeService sharePermissionTypeService;
  @Autowired
  private UserToGroupDao userToGroupDao;
  @Autowired
  private ShareGroupService shareGroupService;
  @Resource
  private ReturnExchangeOrderDao returnExchangeOrderDao;
  @Autowired
  private ExportUtil ex;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private FileRepository fileRepository;
  private final String baseUrl;
  public ReturnExchangeOrderExecService(SrmConfig srmConfig) {
    this.baseUrl = srmConfig.getUploadUrl();
  }

  @SneakyThrows
  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    try (SXSSFWorkbook book = new SXSSFWorkbook()) {
      CellStyle baseStyle = ex.getBaseStyle(book);
      CellStyle titleStyle = ex.getTitleStyle(book);
      titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
      titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
      List<String> titles = new ArrayList<>(Constants_Excel.EXPORT_RETURN_EXCHANGE_LIST_TITLE_LIST);
      List<Integer> titleSize = new ArrayList<>();
      for (String t : Constants_Excel.EXPORT_RETURN_EXCHANGE_LIST_TITLE_LIST) {
        titleSize.add(20);
      }
      Sheet sheet = ex.createSheet(book, "退换货订单", titleSize);
      Row rowTitle = sheet.createRow(0);
      ex.createTitle(titles, titleStyle, rowTitle);
      List<ReturnExchangeListVO> vos = (List<ReturnExchangeListVO>) collection;
      int index = 1;
      long count = 0;
      for (int i = 0; i < vos.size(); i++) {
        int col = 0;
        int rowNum = index + 1;
        Row row = sheet.createRow(index);
        ReturnExchangeListVO vo = vos.get(i);
        // 退换货订单号
        ex.createCell(row, col++, vo.getCode(), baseStyle);
        ex.createCell(row, col++, vo.getOrderStateValue(), baseStyle);
        ex.createCell(row, col++, vo.getProjectTypeValue(), baseStyle);
        ex.createCell(row, col++, vo.getReturnReason(), baseStyle);
        ex.createCell(row, col++, DateUtils.formatTimeStampToNormalDateTime(vo.getCreateTime()),
            baseStyle);
        ex.createCell(row, col++, vo.getSupplierName(), baseStyle);
        ex.createCell(row, col++, vo.getPurchaseMan(), baseStyle);
        ex.createCell(row, col++, vo.getPurchaseDept(), baseStyle);
        ex.createCell(row, col++, vo.getProductCode(), baseStyle);
        ex.createCell(row, col++, vo.getBrand(), baseStyle);
        ex.createCell(row, col++, vo.getProductName(), baseStyle);
        ex.createCell(row, col++, vo.getDescription(), baseStyle);
        ex.createCell(row, col++, vo.getManuCode(), baseStyle);
        ex.createCell(row, col++, vo.getUnit(), baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getNum()).stripTrailingZeros().toPlainString(), baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getPrice()).stripTrailingZeros().toPlainString(), baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getTaxRate()).stripTrailingZeros().toPlainString(), baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getTotalPrice()).stripTrailingZeros().toPlainString(),
            baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getReturnedNum()).stripTrailingZeros().toPlainString(),
            baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getCancelReturnNum()).stripTrailingZeros().toPlainString(),
            baseStyle);
        ex.createCell(row, col++,
            NumberUtil.null2Zero(vo.getRemainReturnNum()).stripTrailingZeros().toPlainString(),
            baseStyle);
        ex.createCell(row, col++, vo.getWarehouseName(), baseStyle);
        ex.createCell(row, col++, vo.getMark(), baseStyle);
        ex.createCell(row, col++, vo.getOriginalOrderNo(), baseStyle);
        ex.createCell(row, col++, vo.getBatchNo(), baseStyle);
        ex.createCell(row, col++, Constants.BOOLEAN_TO_TEXT_MAP.get(vo.getNeedRedInvoice()),
            baseStyle);
        ex.createCell(row, col++, vo.getSupplierOpenInvoiceStateValue(), baseStyle);
        ex.createCell(row, col++, Constants.BOOLEAN_TO_TEXT_MAP.get(vo.getLoss()), baseStyle);
        String files = CollUtil.emptyIfNull(vo.getAnnexDTOs()).stream().map(FileDTOExt::getFullUrl)
            .collect(Collectors.joining("\n"));
        ex.createCell(row, col++, files, baseStyle);
        index++;
        count++;
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(), "第【" + rowNum + "】行",
            StrUtil.EMPTY);
      }
      String now = String.valueOf(System.currentTimeMillis());
      String fileNewName = "【后台】退换货订单导出" + now + ".xlsx";
      // 创建临时文件
      File tempFile = File.createTempFile("excel_", ".xlsx");
      try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
        // 将 XSSFWorkbook 内容写入临时文件
        book.write(fileOutputStream);
        book.dispose();
        book.close();
      }
      return ExportMissionCompleteResult.builder().successCount(count).fileName(fileNewName)
          .tempFile(tempFile).build();
    }
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
//    String createManId = dispatchParam.getUserId();
    String params = dispatchParam.getParams();
//    User user = Optional.ofNullable(userDao.get(createManId))
//        .orElseThrow(() -> CheckException.noFindException(User.class, createManId));
    Map<String, Object> queryMap =
        JSON.parseObject(params, new TypeReference<Map<String, Object>>() {});
    // 判断导出权限
//    String permissionCode =
//        StrUtil.blankToDefault(sharePermissionTypeService.getUserPermissionCodeByUserIdAndType(user.getId(),
//            Constants.USER_PERMISSION_EXPORT_RETURN_EXCHANGE), Constants.EXPORT_OWM_PURCHASE_TYPE);
//    if (Constants.NOT_EXPORT_IMPORT_KEY.equals(permissionCode)) {
//      throw new CheckException("导出失败，无导出权限");
//    }
    // 供应商查询
//    boolean selectSupplier = StrUtil.isNotBlank(Convert.toStr(queryMap.get("supplierName")));
//    List<String> curUserDeptList = new ArrayList<>();
//    String curUserCode = StrUtil.EMPTY;
//    if ((!selectSupplier) && Constants.EXPORT_DEPT_TYPE.equals(permissionCode)) {
//      // 采购部门符合用户所在部门
//      curUserDeptList.addAll(
//          CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(createManId)).stream()
//              .map(UserToGroup::getDeptId).distinct().map(shareGroupService::get)
//              .filter(ObjectUtil::isNotNull).map(Group::getErpCode).collect(Collectors.toList()));
//    } else if ((!selectSupplier) && Constants.EXPORT_OWM_PURCHASE_TYPE.equals(permissionCode)) {
//      // 采购员为当前用户
//      curUserCode = user.getCode();
//    }
//    queryMap.put("curUserDeptList", curUserDeptList);
//    queryMap.put("curUserCode", curUserCode);
    List<ReturnExchangeListVO> content = returnExchangeOrderDao.getPage(queryMap).getContent();
    // 分批次处理文件附件
    int batchSize = 1000;
    int size = content.size();
    for (int i = 0; i < size; i += batchSize) {
      int end = Math.min(i + batchSize, size);
      List<ReturnExchangeListVO> subList = content.subList(i, end);
      List<String> supplierOrderIds =
          subList.stream().map(ReturnExchangeListVO::getSupplierOrderId).collect(Collectors.toList());
      List<com.xhgj.srm.jpa.entity.File> attachments =
          fileRepository.findByRelationIdInAndRelationTypeAndState(supplierOrderIds,
              Constants_FileRelationType.ORDER_ANNEX, Constants.STATE_OK);
      Map<String, List<com.xhgj.srm.jpa.entity.File>> attachmentMap =
          attachments.stream().collect(Collectors.groupingBy(com.xhgj.srm.jpa.entity.File::getRelationId));
      subList.forEach(vo -> {
        List<com.xhgj.srm.jpa.entity.File> findAttachments = attachmentMap.get(vo.getSupplierOrderId());
        findAttachments = CollUtil.emptyIfNull(findAttachments);
        List<FileDTOExt> fileDTOExts = findAttachments.parallelStream()
            .map(item -> new FileDTOExt(item.getId(), item.getDescription(), item.getUrl(), baseUrl))
            .collect(Collectors.toList());
        vo.setAnnexDTOs(fileDTOExts);
      });
    }
    return content;
  }
}
