package com.xhgj.srm.mission.consumer.handlers.dataProcess;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.OmsOrderReceiptDto;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.enums.order.OrderReceiptType;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.repository.OrderNeedPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderReceiptRecordRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/4/16 13:43
 * @Description: 履约回款记录-需付账款回款比例数据处理
 * 查询客户回款记录回款比例为0%的数据，批量调用履约回款记录接口，将接口返参的比例赋值客户回款记录以及对应的需付款数据
 */
@Slf4j
@Component
public class OrderReceiptProcess implements  DataProcess{
  @Resource
  private OrderReceiptRecordRepository orderReceiptRecordRepository;
  @Resource
  private OrderNeedPaymentRepository orderNeedPaymentRepository;
  @Resource
  private OMSService omsService;
  @Resource
  private OrderRepository orderRepository;
  @Override
  public void process(MissionDispatchParam dispatchParam) {
    // 按创建顺序处理
    List<OrderReceiptRecord> receiptRecords =
        orderReceiptRecordRepository.findAllByOrderByCreateTimeAsc();
    if (CollUtil.isEmpty(receiptRecords)) {
      return;
    }
    for (OrderReceiptRecord receiptRecord : receiptRecords) {
      try {
        Order order = orderRepository.findById(receiptRecord.getOrderId()).orElseThrow(
            () -> CheckException.noFindException(Order.class, receiptRecord.getOrderId()));
        String orderNo = StrUtil.trim(order.getOrderNo());
        String type = StrUtil.trim(order.getType());
        String supplierOrderId = StrUtil.trim(order.getSupplierOrderId());
        List<PaymentStatus> orderCustomerReturnList =
            omsService.getOrderCustomerReturnStatus(orderNo, type, supplierOrderId);
        if (CollUtil.isEmpty(orderCustomerReturnList)) {
          log.info("订单回款状态查询结果为空，订单号：{}", orderNo);
          continue;
        }
        PaymentStatus orderCustomerReturn = orderCustomerReturnList.get(0);
        if (ObjectUtil.isEmpty(orderCustomerReturn)) {
          continue;
        }
        // 1. 赋值回款记录比例
        OmsOrderReceiptDto receiptDto = orderCustomerReturn.getPaymentRecordDTOList().stream()
            .filter(dto -> StrUtil.equals(dto.getId(), receiptRecord.getOmsUniqueId())).findFirst()
            .orElseThrow(() -> new CheckException("未找到对应记录的履约回款记录"));
        receiptRecord.setPaymentRatio(receiptDto.getPaymentRatio());
        orderReceiptRecordRepository.saveAndFlush(receiptRecord);
        // 2. 判断后赋值需付账款比例
        OrderNeedPayment orderNeedPayment =
            orderNeedPaymentRepository.findFirstByOrderReceiptIdAndState(receiptRecord.getId(),
                Constants.STATE_OK);
        if (orderNeedPayment == null) {
          continue;
        }
        orderNeedPayment.setShowRate(receiptDto.getPaymentRatio());
        orderNeedPaymentRepository.saveAndFlush(orderNeedPayment);
        Boolean backToBack = order.getBackToBack();
        if (BooleanUtil.isTrue(orderNeedPayment.getOffset()) || Boolean.FALSE.equals(backToBack)) {
          // 冲销的回款记录或非背靠背直接跳过
          continue;
        }
        switch (OrderReceiptType.getByCode(receiptRecord.getPaymentType())) {
          case NORMAL:
            orderNeedPayment.setRate(receiptDto.getPaymentRatio());
            break;
          case MULTI_MODAL:
            if (receiptRecord.getPaymentAmount().compareTo(BigDecimal.ZERO) < 0) {
              orderNeedPayment.setRate(receiptDto.getPaymentRatio().multiply(new BigDecimal("-1")));
            } else {
              orderNeedPayment.setRate(receiptDto.getPaymentRatio());
            }
            break;
        }
        orderNeedPaymentRepository.saveAndFlush(orderNeedPayment);
      } catch (Exception e) {
        log.error("回款记录：{}处理失败", receiptRecord.getId(), e);
      }
    }
  }
}
