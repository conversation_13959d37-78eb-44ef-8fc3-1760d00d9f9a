package com.xhgj.srm.mission.consumer.event.application;

import com.xhgj.srm.mission.common.MissionSourceEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 任务完成事件
 *
 * <AUTHOR>
 * @since 2024/8/13 13:26
 */
@Getter
public class MissionCompleteEvent extends ApplicationEvent {

  private static final long serialVersionUID = -5606410300454061431L;

  /** 任务 id */
  private final String missionId;

  /** 任务来源：前台/后台 */
  private final MissionSourceEnum missionSource;

  /** 任务名 */
  private final String missionName;

  /** 发起人 id */
  private final String userId;

  /** 是否执行成功 */
  private final boolean success;

  /**
   * Create a new ApplicationEvent.
   *
   * @param source the object on which the event initially occurred (never {@code null})
   */
  public MissionCompleteEvent(
      Object source,
      String missionId,
      MissionSourceEnum missionSource,
      String missionName,
      String userId,
      boolean success) {
    super(source);
    this.missionId = missionId;
    this.missionSource = missionSource;
    this.missionName = missionName;
    this.userId = userId;
    this.success = success;
  }

  @Override
  public String toString() {
    return "MissionCompleteEvent{"
        + "source="
        + source
        + ", success="
        + success
        + ", userId='"
        + userId
        + '\''
        + ", missionName='"
        + missionName
        + '\''
        + ", missionSource='"
        + missionSource
        + '\''
        + ", missionId='"
        + missionId
        + '\''
        + '}';
  }
}
