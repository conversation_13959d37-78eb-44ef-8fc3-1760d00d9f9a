<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xhgj</groupId>
    <artifactId>srm-boot</artifactId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>srm-api-portal</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>


    <dependency>
      <groupId>com.xhiot.xhiot-boot.framework</groupId>
      <artifactId>framework-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-log4j2</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-request</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-service</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-api-portal-provider</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--    需要发送 MQ 消息，TODO 许海川：上线时需要注意环境配置-->
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-mq</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-open</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-v2</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>

  </dependencies>
  <build>
    <!-- java -Dloader.path=./lib -jar srm-api-manage.jar -->
    <finalName>srm-api-portal</finalName>
    <plugins>
      <!--<plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>-->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <layout>ZIP</layout>
          <includes>
            <include>
              <groupId>nothing</groupId>
              <artifactId>nothing</artifactId>
            </include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>