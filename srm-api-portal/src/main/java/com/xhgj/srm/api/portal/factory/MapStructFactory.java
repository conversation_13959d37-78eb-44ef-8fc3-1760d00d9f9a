package com.xhgj.srm.api.portal.factory;/**
 * @since 2024/12/5 17:13
 */
import com.xhgj.srm.api.portal.dto.InventoryAddParam;
import com.xhgj.srm.api.portal.dto.purchase.order.PurchaseApplyForOrderAddParam;
import com.xhgj.srm.dto.entryregistration.EntryRegistrationDetail;
import com.xhgj.srm.jpa.entity.Inventory;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDetailDTO;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2AddForm;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 *<AUTHOR>
 *@date 2024/12/5 17:13:45
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {
  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * supplierOrderDetail to supplierOrderDetail
   * @param supplierOrderDetail
   * @return
   */
  SupplierOrderDetail toSupplierOrderDetail(SupplierOrderDetail supplierOrderDetail);

  /**
   * supplier to supplier
   * @param supplier
   * @return
   */
  Supplier toSupplier(Supplier supplier);

  /**
   * EntryRegistrationDetailDTO to EntryRegistrationDetail
   * @param entryRegistrationDetailDTO
   * @return
   */
  EntryRegistrationDetail toEntryRegistrationDetail(EntryRegistrationDetailDTO  entryRegistrationDetailDTO);


  Inventory toInventory(InventoryAddParam inventoryAddParam);

  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateInventoryEntity(InventoryAddParam source, @MappingTarget Inventory target);

  /**
   * PurchaseApplyForOrderV2AddForm to PurchaseApplyForOrderAddParam
   * @param source
   * @return
   */
  PurchaseApplyForOrderAddParam toPurchaseApplyForOrderAddParam(PurchaseApplyForOrderV2AddForm source);

  /**
   * PurchaseApplyForOrderV2 to PurchaseApplyForOrder
   * @return
   */
  PurchaseApplyForOrderV2 toPurchaseApplyForOrderV2(PurchaseApplyForOrder source);
}
