package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.portal.dto.product.MPMPriceAudit;
import com.xhgj.srm.api.portal.dto.product.MPMSendPricePassParam;
import com.xhgj.srm.api.portal.dto.product.MPMSendPriceRejectParam;
import com.xhgj.srm.api.portal.service.ProductService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.NoticeCenterType;
import com.xhgj.srm.jpa.entity.NoticeCenter;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.repository.NoticeCenterRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

  @Resource
  SupplierRepository supplierRepository;
  @Resource
  NoticeCenterRepository noticeCenterRepository;

  private static final String PRICE_AUDIT_CONTENT = "{}， {}，{}";


  @Override
  public void sendProductPricePass(MPMSendPricePassParam param) {
    try {
      Supplier supplier =
          supplierRepository.getFirstByMdmCodeAndState(param.getSupplierCode(), Constants.STATE_OK);
      if (supplier != null) {
        log.info("国网物料调价通过发送站内消息{}", JSON.toJSONString(param));
        saveNoticeCenter(NoticeCenterType.PRODUCT_PRICE_PASS,
            param.getContent(), null,
            supplier.getId());
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public Boolean sendProductPriceReject(MPMSendPriceRejectParam param) {
    try {
      Supplier supplier =
          supplierRepository.getFirstByMdmCodeAndState(param.getSupplierCode(), Constants.STATE_OK);
      if(supplier != null){
        saveNoticeCenter(NoticeCenterType.PRODUCT_PRICE_REJECT,
            StrUtil.format(Constants_order.PRODUCT_PRICE_REJECT, param.getCode()),
            null, supplier.getId());
      }
      return true;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public void priceAudit(MPMPriceAudit form) {
    Supplier supplier =
        supplierRepository.getFirstByMdmCodeAndState(form.getSupplierCode(), Constants.STATE_OK);
    log.info("MPM调价审核{}", JSON.toJSONString(form));
    String result = form.getSuccess() ? "审核通过" : "审核驳回";
    if (supplier != null) {
      if (form.getSuccess()) {
        saveNoticeCenter(NoticeCenterType.MPM_PRICE_AUDIT_RESULT,
            StrUtil.format(PRICE_AUDIT_CONTENT, form.getCode(), form.getName(),result),
            null, supplier.getId());
      } else {
        if (StrUtil.isNotBlank(form.getReason())) {
          result = StrUtil.format("审核驳回，原因：{}", form.getReason());
        }
        saveNoticeCenter(NoticeCenterType.MPM_PRICE_AUDIT_RESULT,
            StrUtil.format(PRICE_AUDIT_CONTENT, form.getCode(), form.getName(), result),
            null, supplier.getId());
      }
    }
  }

  /**
   * 保存消息中心
   *
   * @param type 消息类型
   * @param supplierOrderId 订单id
   * @param supplierId 供应商id
   */
  private void saveNoticeCenter(NoticeCenterType type, String content, String supplierOrderId,
      String supplierId) {
    log.info("新增前台消息通知内容：" + "type:" + type + "content:" + content + "supplierOrderId:"
        + supplierOrderId + "supplierId:" + supplierId);
    noticeCenterSave(type, content, supplierOrderId, supplierId);
  }

  public void noticeCenterSave(NoticeCenterType type, String content, String supplierOrderId, String supplierId) {
    NoticeCenter noticeCenter = new NoticeCenter();
    noticeCenter.setContent(content);
    noticeCenter.setType(type.getKey());
    noticeCenter.setSupplierId(supplierId);
    noticeCenter.setSupplierOrderId(supplierOrderId);
    noticeCenter.setIsRead(false);
    noticeCenter.setState(Constants.STATE_OK);
    noticeCenter.setCreateTime(System.currentTimeMillis());
    noticeCenterRepository.save(noticeCenter);
  }

}
