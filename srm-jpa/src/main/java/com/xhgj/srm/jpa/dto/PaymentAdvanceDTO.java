package com.xhgj.srm.jpa.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
  *@ClassName PaymentAdvanceDTO
  *<AUTHOR>
  *@Date 2023/12/28 17:17
*/
@Data
public class PaymentAdvanceDTO {
  /**
   * 最大可预付金额
   */
  @ApiModelProperty("最大可预付金额")
  private String maxAdvancePrice;

  /**
   * 预付账期
   */
  @ApiModelProperty("预付账期")
  private String advancePeriod;

  /**
   * 付款方式
   */
  @ApiModelProperty("付款方式")
  private String payType;

  /**
   * 开户行
   */
  @ApiModelProperty("开户行")
  private String bank;

  /**
   * 银行账号
   */
  @ApiModelProperty("银行账号")
  private String bankAccount;

  /**
   * 账户名称
   */
  @ApiModelProperty("账户名称")
  private String accountName;

  /**
   * 采购订单号
   */
  @ApiModelProperty("采购订单号")
  private String supplierOrderNo;

  @ApiModelProperty("联行号")
  private String bankCode;

  @ApiModelProperty("货币码")
  private String moneyCode;

  @ApiModelProperty("银行账号备注")
  private String bankNumRemark;

}
