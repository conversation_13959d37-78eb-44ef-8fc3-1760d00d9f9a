package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.Query;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/11/27 16:28
 */
public interface SupplierOrderRepository extends BootBaseRepository<SupplierOrder,String> {


  /**
   * 根据 erpId 获得供应商订单
   * @param erpId erpId 必传
   * @param state 数据状态 必传
   */
  SupplierOrder getFirstByErpIdAndState(String erpId,String state);

  /**
   * 根据订单类型获得订单的数量
   * @param orderState 发货单类型 必传
   * @param state 数据状态 必传
   */
  Long countAllByOrderStateAndState(String orderState,String state);

  /**
   * 根据订单类型获得订单
   * @param orderState 发货单类型 必传
   * @param state 数据状态 必传
   */
  List<SupplierOrder> getAllByOrderStateAndState(String orderState,String state);

  /**
   * 根据订单类型获得订单的数量
   * @param SupplierOrderId 供应商id 必传
   * @param orderState 发货单类型 必传
   * @param state 数据状态 必传
   */
  Long countBySupplierIdAndOrderStateAndState(String SupplierOrderId,String orderState,String state);

  /**
   * 根据订单类型获得订单的数量
   * @param SupplierOrderId 供应商id 必传
   * @param orderState 发货单类型 必传
   * @param state 数据状态 必传
   * @param supplierOpenInvoiceState 供应商开票状态
   */
  Long countBySupplierIdAndOrderStateAndStateAndSupplierOpenInvoiceStateAndOrderTypeNotIn(String SupplierOrderId,
      String orderState,String state, String supplierOpenInvoiceState,String orderType);

  Long countBySupplierIdAndOrderStateAndStateAndSupplierOpenInvoiceStateAndScpAndOrderTypeNotIn(String SupplierOrderId,
      String orderState,String state, String supplierOpenInvoiceState,Boolean scp,
      String orderType);

  /**
   * 根据订单确认状态获得订单
   * @param orderConfirmState 是否待确认 必传
   * @param state 数据状态 必传
   */
  List<SupplierOrder> getAllByOrderConfirmStateAndState(Boolean orderConfirmState,String state);


  /**
   * 根据供应商开票单查询
   * @param orderInvoiceRelationId 关联数据id
   * @param state 数据状态
   * @return
   */
  List<SupplierOrder> findAllByOrderInvoiceRelationIdAndState(String orderInvoiceRelationId, String state);

  List<SupplierOrder> findAllBySupplierOpenInvoiceStateAndState(String supplierOpenInvoiceState,
      String state);

  List<SupplierOrder> findAllByOrderStateAndState(String orderState, String state);

  SupplierOrder findFirstByCodeAndState(String code,String state);

  SupplierOrder findFirstByCodeAndStateIn(String code,List<String> states);

  List<SupplierOrder> findAllByCodeInAndStateIn(List<String> codes, List<String> states);

  List<SupplierOrder> findAllByTotalStockInputQtyInAndOrderStateInAndState(
      List<BigDecimal> totalStockInputQty, List<String> orderState, String state);

  List<SupplierOrder> findAllByIdIn(Set<String> ids);

  Long countBySupplierIdAndOrderStateAndStateAndSupplierOpenInvoiceState(String SupplierOrderId,
      String orderState,String state, String supplierOpenInvoiceState);

  /**
   * 根据订单号查询
   * @param codes
   * @param state
   * @return
   */
  List<SupplierOrder> findAllByCodeInAndState(List<String> codes, String state);

  /**
   * 根据订单号查询
   * @param codes
   * @return
   */
  List<SupplierOrder> findAllByCodeIn(List<String> codes);

  Optional<SupplierOrder> findFirstByConsignmentToOwnedCodeAndState(String consignmentToOwnedCode,
      String state);

  Optional<SupplierOrder> findFirstBySupplierIdAndStateOrderByCreateTimeDesc(String supplierId, String state);

  @Query(value = "select so.* from t_supplier_order so where so.c_code in ?1 and so.c_state = ?2 "
      + "order by so.c_order_create_time asc limit 1",nativeQuery = true)
  SupplierOrder findNewestByCodes(List<String> codes,String state);

  /**
   * 根据订单号查询
   * @param of
   * @return
   */
  @Query(
      value = "select so from SupplierOrder so where so.state = '1' "
      + "and (so.soldToParty is null or so.salesman is null) "
  )
  Page<SupplierOrder> getOldData(PageRequest of);
}
