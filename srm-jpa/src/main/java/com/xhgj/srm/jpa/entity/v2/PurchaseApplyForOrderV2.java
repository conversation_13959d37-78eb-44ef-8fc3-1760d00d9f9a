package com.xhgj.srm.jpa.entity.v2;

import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.enums.OrderGoodsStateV2Enum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.jpa.annotations.ShardingTable;
import com.xhgj.srm.jpa.entity.BasePurchaseApplyForOrder;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.Data;
import org.hibernate.annotations.DynamicUpdate;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 采购申请单实体类
 */
@Entity
@Table(name = "t_purchase_apply_for_order")
@Data
@DynamicUpdate
@ShardingTable(
    logicTable = "t_purchase_apply_for_order",
    actualDataNodes = "ds0.t_purchase_apply_for_order,ds0.t_purchase_apply_for_order_v2"
)
public class PurchaseApplyForOrderV2 extends BasePurchaseApplyForOrder {

  /**
   * 规格
   */
  @Column(name = "c_specification")
  private String specification;

  /**
   * 资料卡片:编码+名称
   */
  @Column(name = "c_profile_card")
  private String profileCard;

  /**
   * 资料卡片编码
   */
  @Column(name = "c_profile_card_code")
  private String profileCardCode;

  /**
   * 资料卡片名称
   */
  @Column(name = "c_profile_card_name")
  private String profileCardName;

  /**
   * 科目分配类别:编码+名称
   */
  @Column(name = "c_assignment_category")
  private String assignmentCategory;

  /**
   * 科目分配类别编码
   */
  @Column(name = "c_assignment_category_code")
  private String assignmentCategoryCode;

  /**
   * 科目分配类别名称
   */
  @Column(name = "c_assignment_category_name")
  private String assignmentCategoryName;

  /**
   * 项目类别:编码+名称
   */
  @Column(name = "c_project_category")
  private String projectCategory;

  /**
   * 项目类别编码
   */
  @Column(name = "c_project_category_code")
  private String projectCategoryCode;

  /**
   * 项目类别名称
   */
  @Column(name = "c_project_category_name")
  private String projectCategoryName;

  /**
   * 总账科目:编码+名称
   */
  @Column(name = "c_ledger_subject")
  private String ledgerSubject;

  /**
   * 总账科目编码
   */
  @Column(name = "c_ledger_subject_code")
  private String ledgerSubjectCode;

  /**
   * 总账科目名称
   */
  @Column(name = "c_ledger_subject_name")
  private String ledgerSubjectName;

  /**
   * 成本中心:编码+名称
   */
  @Column(name = "c_cost_center")
  private String costCenter;

  /**
   * 成本中心编码
   */
  @Column(name = "c_cost_center_code")
  private String costCenterCode;

  /**
   * 成本中心名称
   */
  @Column(name = "c_cost_center_name")
  private String costCenterName;

  /**
   * 订单:编码+名称
   */
  @Column(name = "c_order")
  private String order;

  /**
   * 订单编码
   */
  @Column(name = "c_order_code")
  private String orderCode;

  /**
   * 订单名称
   */
  @Column(name = "c_order_name")
  private String orderName;

  /**
   * 物料组:编码+名称
   */
  @Column(name = "c_item_group")
  private String itemGroup;

  /**
   * 物料组编码
   */
  @Column(name = "c_item_group_code")
  private String itemGroupCode;

  /**
   * 物料组名称
   */
  @Column(name = "c_item_group_name")
  private String itemGroupName;

  /**
   * 交货日期
   */
  @Column(name = "c_deliver_time")
  private Long deliverTime;

  /**
   * 固定的供应商
   */
  @Column(name = "c_fixed_vendor")
  private String fixedVendor;

  /**
   * 固定的供应商名称
   */
  @Column(name = "c_fixed_vendor_name")
  private String fixedVendorName;

  /**
   * 采购信息记录：标准/寄售
   */
  @Column(name = "c_procurement_record")
  private String procurementRecord;

  /**
   * 采购员工号
   */
  @Column(name = "c_purchase_man_number")
  private String purchaseManNumber;

  /**
   * 计算列拼接工号 + 采购员
   */
  @Column(name = "c_purchase_man_mix", insertable = false, updatable = false)
  private String purchaseManMix;

  /**
   * index
   */
  @Transient
  private Integer index;

  /**
   * 刷新V2订货状态
   * todo 锁定时候在更新会覆盖锁定问题
   */
  public void updateOrderGoodsState() {
    BigDecimal orderGoodsNumber =
        Optional.ofNullable(this.getOrderGoodsNumber())
            .orElse(BigDecimal.ZERO);
    BigDecimal applyForNumber = Optional.ofNullable(this.getApplyForNumber())
        .orElse(BigDecimal.ZERO);
    // 更新orderGoodsState -- 如果是取消状态则直接完成
    if (getCancellationState().equals(SimpleBooleanEnum.YES.getKey())) {
      this.setOrderGoodsState(OrderGoodsStateV2Enum.ORDER_COMPLETE.getKey());
    } else if (NumberUtil.isLess(orderGoodsNumber, applyForNumber)) {
      this.setOrderGoodsState(OrderGoodsStateV2Enum.CAN_ORDER.getKey());
    } else {
      this.setOrderGoodsState(OrderGoodsStateV2Enum.ORDER_COMPLETE.getKey());
    }
  }

  /**
   * 刷新V2订货状态 校验锁定
   */
  public void updateOrderGoodsStateCheckLock() {
    if (OrderGoodsStateV2Enum.LOCK.getKey().equals(this.getOrderGoodsState())) {
      throw new CheckException("采购申请单锁定状态不能修改");
    }
    this.updateOrderGoodsState();
  }
}