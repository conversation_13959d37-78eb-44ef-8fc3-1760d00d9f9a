package com.xhgj.srm.jpa.repository;



import com.xhgj.srm.jpa.entity.OrderOpenInvoice;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Collection;
import java.util.List;

/**
 * 落地商订单开票信息(OrderOpenInvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-03 10:46:03
 */
public interface OrderOpenInvoiceRepository extends BootBaseRepository<OrderOpenInvoice,String> {

  /**
   * 通过开票申请 id 获得开票信息
   * @param orderInvoiceId 开票申请 id 必传
   * @return
   */
  List<OrderOpenInvoice> getByOrderInvoiceIdOrderByCreateTimeDesc(String orderInvoiceId);

  /**
   * 通过开票申请 id 获得开票信息
   * @param orderInvoiceIds
   * @return
   */
  List<OrderOpenInvoice> findAllByOrderInvoiceIdIn(Collection<String> orderInvoiceIds);
}


