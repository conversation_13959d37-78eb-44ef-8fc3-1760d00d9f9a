package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.dto.SearchSupplierOrderDetailDTO;
import com.xhgj.srm.common.dto.SearchSupplierOrderDetailForm;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.purchase.order.PurchaseOrderProductFilterTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderRefuseState;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.dto.OrderProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.ProductDetailInfoDTO;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.unified.dto.UnifiedChildForm;
import com.xhgj.srm.unified.dto.UnifiedChildForm.UnifiedFilter;
import com.xhgj.srm.unified.dto.UnifiedForm;
import com.xhgj.srm.unified.dto.UnifiedFormForQueryMap;
import com.xhgj.srm.unified.enmus.UnifiedCombinationLogicEnums;
import com.xhgj.srm.unified.factory.UnifiedSqlFactory;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2022/11/28 18:59
 */
@Repository
public class SupplierOrderDetailDaoImpl extends AbstractExtDao<SupplierOrderDetail>
    implements SupplierOrderDetailDao {

  @Override
  public List<String> getErpIdListByOrderToFormId(String orderToFormId) {
    Assert.notEmpty(orderToFormId);
    String hql = "select erpId from SupplierOrderDetail where orderToFormId = ? and state = ? ";
    return getHqlObjList(hql, orderToFormId, Constants.STATE_OK);
  }

  @Override
  public Page<SupplierOrderDetail> findPurchaseOrderDetailPageRef(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append("select distinct sod.*,so.c_create_time orderTime from t_supplier_order_detail sod ");
    sql.append(" left join t_supplier_order_to_form sotf ");
    sql.append(" on sod.order_to_form_id = sotf.id ");
    sql.append(" left join t_supplier_order so ");
    sql.append(" on so.id = sotf.supplier_order_id ");
    sql.append(" left join t_supplier_order_product sop ");
    sql.append(" on sop.id = sod.order_product_id ");
    UnifiedFormForQueryMap unifiedForm = UnifiedSqlFactory.buildUnifiedForm(queryMap);
    if (StrUtil.isNotEmpty(unifiedForm.getValueStr("purchaseApplyCode"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("salesman"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("followUpPersonName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("applyForType"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("businessCompanyName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("makeManName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("isWorryOrder"))
    ) {
      sql.append(" left join t_purchase_apply_for_order pafo ");
      sql.append(" on sod.purchase_apply_for_order_id = pafo.id ");
    }
    Object[] params = buildWhereQuery(sql, queryMap);
    sql.append(" order by orderTime desc ");
    return findPageSql(sql.toString(), params, Convert.toInt(queryMap.get("pageNo")), Convert.toInt(queryMap.get("pageSize")));
  }

  @Override
  public PurchaseOrderProductStatistics getPagePurchaseOrderStatistics2(
      Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append("select ")
        .append("COALESCE(SUM(subQuery.cancelQty), 0), ")
        .append("COALESCE(SUM(subQuery.returnQty), 0), ")
        .append("COALESCE(SUM(subQuery.price), 0), ")
        // 计算总价 num * price，并在子查询中保留2位小数
        .append("COALESCE(SUM(subQuery.totalPrice), 0), ")
        .append("COALESCE(SUM(subQuery.num), 0) ")
        .append("from ( ")
        .append("   select ")
        .append("   sod.c_cancel_qty as cancelQty, ")
        .append("   sod.c_return_qty as returnQty, ")
        .append("   sod.c_price as price, ")
        // 在子查询中计算 num * price 并保留2位小数
        .append("   ROUND(COALESCE(sod.c_num, 0) * COALESCE(sod.c_price, 0), 2) as totalPrice, ")
        .append("   sod.c_num as num ")
        .append("   from t_supplier_order_detail sod ")
        .append("   left join t_supplier_order_to_form sotf ")
        .append("   on sod.order_to_form_id = sotf.id ")
        .append("   left join t_supplier_order so ")
        .append("   on so.id = sotf.supplier_order_id ")
        .append("   left join t_supplier_order_product sop ")
        .append("   on sop.id = sod.order_product_id ");

    UnifiedFormForQueryMap unifiedForm = UnifiedSqlFactory.buildUnifiedForm(queryMap);
    if (StrUtil.isNotEmpty(unifiedForm.getValueStr("purchaseApplyCode"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("salesman"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("followUpPersonName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("applyForType"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("businessCompanyName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("makeManName"))
        || StrUtil.isNotEmpty(unifiedForm.getValueStr("isWorryOrder"))
    ) {
      sql.append(" left join t_purchase_apply_for_order pafo ");
      sql.append(" on sod.purchase_apply_for_order_id = pafo.id ");
    }

    // 添加查询条件
    Object[] params = buildWhereQuery(sql, queryMap);

    // 在子查询结束时关闭子查询括号
    sql.append(") subQuery");
    Object[] sqlObj = (Object[]) getUniqueSqlObj(sql.toString(), params);
    PurchaseOrderProductStatistics purchaseOrderProductStatistics =
        new PurchaseOrderProductStatistics();
    purchaseOrderProductStatistics.setCancelQty(Convert.toBigDecimal(sqlObj[0]));
    purchaseOrderProductStatistics.setReturnQty(Convert.toBigDecimal(sqlObj[1]));
    purchaseOrderProductStatistics.setPrice(Convert.toBigDecimal(sqlObj[2]));
    purchaseOrderProductStatistics.setTotalPrice(Convert.toBigDecimal(sqlObj[3]));
    purchaseOrderProductStatistics.setNum(Convert.toBigDecimal(sqlObj[4]));
    return purchaseOrderProductStatistics;
  }

  private Object[] buildWhereQuery(StringBuilder sql, Map<String, Object> queryMap) {
    sql.append(" where so.c_state = ? and sod.c_state = ? and sotf.c_type = ? ");
    Object[] params = new Object[] {Constants.STATE_OK,Constants.STATE_OK,SupplierOrderFormType.DETAILED.getType()};
    // 查询权限筛选
    MergeUserPermission mergeUserPermission = Convert.convert(MergeUserPermission.class, queryMap.get(
        "searchPermission"));
    if (mergeUserPermission != null) {
      StringBuilder permissionSql = new StringBuilder();
      // 查询用户的所有组织
      if (Boolean.TRUE.equals(mergeUserPermission.getIncludeAllUserGroups())) {
        if (CollUtil.isNotEmpty(mergeUserPermission.getCurrentGroupErpCodes())) {
          permissionSql.append(" and so.c_group_code in ( ");
          for (int i = 0; i < mergeUserPermission.getCurrentGroupErpCodes().size(); i++) {
            permissionSql.append(" ? ");
            if (i < mergeUserPermission.getCurrentGroupErpCodes().size() - 1) {
              permissionSql.append(" , ");
            }
            params = ObjectUtils.addObjectToArray(params,
                mergeUserPermission.getCurrentGroupErpCodes().get(i));
          }
          permissionSql.append(" ) ");
        }
      }
      // 所在组织 -> 查询所在组织
      else if (StrUtil.isNotBlank(mergeUserPermission.getCurrentGroupErpCode())) {
        permissionSql.append(" and so.c_group_code = ?  ");
        params = ObjectUtils.addObjectToArray(params, mergeUserPermission.getCurrentGroupErpCode());
      } else if (StrUtil.isNotBlank(mergeUserPermission.getCurrentUserId())) {
        // 为采购员 -> 当前用户采购员
        permissionSql.append(" and so.c_purchase_man  = ? ");
        params = ObjectUtils.addObjectToArray(params, mergeUserPermission.getCurrentUserNameWithCode());
        permissionSql.append(" and so.c_group_code = ? ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("userGroup"));
      }else {
        permissionSql.append(" and so.c_group_code = ? ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("userGroup"));
      }
      if (permissionSql.length() > 0) {
        sql.append("AND ( 1 = 1 ");
        sql.append(permissionSql);
        sql.append(" ) ");
      }
    }
    if(StrUtil.isNotEmpty(Convert.toStr(queryMap.get("orderCode")))) {
      sql.append(" and so.c_code like ? ");
      params = ObjectUtils.addObjectToArray(params, StrUtil.wrap(Convert.toStr(queryMap.get("orderCode")), "%"));
    }
    if (Boolean.TRUE.equals(Convert.toBool(queryMap.get("counteractState")))) {
      sql.append(" and (select count(*) from t_supplier_order_to_form tsotf where supplier_order_id = so.id ");
      sql.append(" and c_type in(?,?) and c_state = '1' and c_status in (?,?))>0 ");
      params = ObjectUtils.addObjectToArray(params, SupplierOrderFormType.WAREHOUSING.getType());
      params = ObjectUtils.addObjectToArray(params, SupplierOrderFormType.RETURN.getType());
      params = ObjectUtils.addObjectToArray(params, SupplierOrderFormStatus.REVERSAL.getStatus());
      params = ObjectUtils.addObjectToArray(params, SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    }
    if (queryMap.get("orderState") != null) {
      if (SupplierOrderState.NOT_REVIEWED.getOrderState().equals(queryMap.get("orderState"))) {
        params = HqlUtil.appendFieldIn(sql, params, "so.c_order_state", ListUtil.of("3", "4", "5", "6"));
      } else if (SupplierOrderState.REVIEWED.getOrderState().equals(queryMap.get("orderState"))) {
        params = HqlUtil.appendFieldIn(sql, params, "so.c_order_state",
            ListUtil.of(SupplierOrderState.WAIT.getOrderState(), SupplierOrderState.IN_PROGRESS.getOrderState()));
      } else {
        sql.append(" and so.c_order_state = ? ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("orderState"));
      }
    }
    if (queryMap.get("pending") != null || queryMap.get("staging") != null || queryMap.get("unaudited") != null
        || queryMap.get("reject") != null) {
      List<String> inList = new ArrayList<>();
      if (Boolean.TRUE.equals(queryMap.get("pending"))) {
        inList.add(SupplierOrderState.PENDING.getOrderState());
      }
      if (Boolean.TRUE.equals(queryMap.get("staging"))) {
        inList.add(SupplierOrderState.STAGING.getOrderState());
      }
      if (Boolean.TRUE.equals(queryMap.get("unaudited"))) {
        inList.add(SupplierOrderState.UNAUDITED.getOrderState());
      }
      if (Boolean.TRUE.equals(queryMap.get("reject"))) {
        inList.add(SupplierOrderState.REJECT.getOrderState());
      }
      params = HqlUtil.appendFieldIn(sql, params, "so.c_order_state", inList);
    }
    if (queryMap.get("startCreateTime") != null) {
      sql.append(" and so.c_order_create_time >= ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("startCreateTime"));
    }
    if (queryMap.get("endCreateTime") != null) {
      sql.append(" and so.c_order_create_time <= ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("endCreateTime"));
    }
    if(queryMap.get("directShipment") != null) {
      sql.append(" and so.c_direct_shipment = ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("directShipment"));
    }
    if (queryMap.get("confirmState") != null || queryMap.get("cancelState") != null
        || queryMap.get("returnState") != null || queryMap.get("refuseState") != null) {
      List<String> orList = new ArrayList<>();
      sql.append(" and (");
      if (queryMap.get("confirmState") != null) {
        orList.add("( so.c_order_confirm_state = ? and so.c_order_state = ? ) ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("confirmState"));
        params = ObjectUtils.addObjectToArray(params, SupplierOrderState.WAIT.getOrderState());
      }
      if (queryMap.get("cancelState") != null) {
        orList.add(" so.c_order_cancel_state = ? ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("cancelState"));
      }
      if (queryMap.get("returnState") != null) {
        orList.add(" so.c_order_return_state = ? ");
        params = ObjectUtils.addObjectToArray(params, queryMap.get("returnState"));
      }
      if (queryMap.get("refuseState") != null && Boolean.TRUE.equals(queryMap.get("refuseState"))) {
        orList.add(" so.c_refuse_state = ? ");
        params = ObjectUtils.addObjectToArray(params, SupplierOrderRefuseState.REFUSE.getKey());
      }
      sql.append(CollUtil.join(orList, " or ")).append(")");
    }
    if (queryMap.get("shipWaitStock") != null) {
      sql.append(" and so.c_order_ship_wait_stock_state = ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("shipWaitStock"));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseGroupName"))) ) {
      sql.append(" and so.c_group_name like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("purchaseGroupName") + "%");
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("receiveMan")))) {
      sql.append(" and so.c_receive_man = ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("receiveMan"));
    }
    String supplierOrderFormType = Convert.toStr(queryMap.get("supplierOrderFormType"));
    if (StrUtil.isNotBlank(supplierOrderFormType)) {
      sql.append(" and sotf.c_type = ? ");
      params = ObjectUtils.addObjectToArray(params, supplierOrderFormType);
    }
    String supplierOrderFormStatus = Convert.toStr(queryMap.get("supplierOrderFormStatus"));
    if (StrUtil.isNotBlank(supplierOrderFormStatus)) {
      sql.append(" and sotf.c_status = ? ");
      params = ObjectUtils.addObjectToArray(params, supplierOrderFormStatus);
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("salesOrderNo")))) {
      sql.append(" and so.c_sale_order_no like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("salesOrderNo") + "%");
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("largeTicketProjectName")))) {
      sql.append(" and so.c_project_name like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("largeTicketProjectName") + "%");
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("largeTicketProjectNumbers")))) {
      sql.append(" and so.c_project_no like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("largeTicketProjectNumbers") + "%");
    }
    if (queryMap.get("selfState") != null) {
      if (Boolean.TRUE.equals(queryMap.get("selfState"))) {
        sql.append(" and so.c_self_state = ? ");
        params = ObjectUtils.addObjectToArray(params, Constants.STATE_OK);
      } else {
        sql.append(" and so.c_self_state = ? ");
        params = ObjectUtils.addObjectToArray(params, Constants.STATE_NO);
      }
    }
    if (queryMap.get("freeState") != null) {
      if (Boolean.TRUE.equals(queryMap.get("freeState"))) {
        sql.append(" and sod.c_free_state = ? ");
        params = ObjectUtils.addObjectToArray(params, Constants.STATE_OK);
      } else {
        sql.append(" and sod.c_free_state = ? ");
        params = ObjectUtils.addObjectToArray(params, Constants.STATE_NO);
      }
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseMan")))) {
      params = SQLUtils.addLikeConditions(sql, params, "so.c_purchase_man",
          Convert.toStr(queryMap.get("purchaseMan")));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseDept")))) {
      params = SQLUtils.addLikeConditions(sql, params, "so.c_purchase_dept",
          Convert.toStr(queryMap.get("purchaseDept")));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseApplyCode")))) {
      sql.append(" and pafo.c_apply_for_order_no like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("purchaseApplyCode") + "%");
    }
    if (StrUtil.isEmpty(Convert.toStr(queryMap.get("allPrePay"))) && queryMap.get("prePay") != null) {
      if (Boolean.TRUE.equals(queryMap.get("prePay"))) {
        sql.append(
            " and (SELECT COUNT(*) FROM `t_payment_apply_record` WHERE `c_supplier_order_no` "
                + "like CONCAT('%',so.c_code,'%') and c_state = ? and c_apply_type= ? )>0 ");
      } else {
        sql.append(" and (SELECT COUNT(*) FROM `t_payment_apply_record` WHERE "
            + "`c_supplier_order_no` like CONCAT('%',so.c_code,'%') and c_state = ? and "
            + "c_apply_type= ? )=0 ");
      }
      params = ObjectUtils.addObjectToArray(params, Constants.STATE_OK);
      params = ObjectUtils.addObjectToArray(params, PaymentApplyTypeEnums.ADVANCE.getKey());
    }
    if (StrUtil.isEmpty(Convert.toStr(queryMap.get("allUploadContract"))) && queryMap.get("uploadContract") != null) {
      if (Boolean.TRUE.equals(queryMap.get("uploadContract"))) {
        sql.append(" and (select COUNT(*) from t_file where c_relationId = so.id and "
            + "c_relationType = ? )>0 ");
      } else {
        sql.append(
            "and (select COUNT(*) from t_file where c_relationId = so.id and c_relationType = ? )=0");
      }
      params = ObjectUtils.addObjectToArray(params, Constants_FileRelationType.ORDER_CONTRACT);
    }
    if (StrUtil.isEmpty(Convert.toStr(queryMap.get("allScp"))) && queryMap.get("scp") != null) {
      sql.append(" and so.c_scp = ? ");
      params = ObjectUtils.addObjectToArray(params, queryMap.get("scp"));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("brand")))) {
      params = SQLUtils.addLikeConditions(sql, params, "sop.c_brand", Convert.toStr(queryMap.get("brand")));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("name")))) {
      sql.append(" and sop.c_name like ? ");
      params = ObjectUtils.addObjectToArray(params, "%" + queryMap.get("name") + "%");
    }
    if (StrUtil.isNotBlank(Convert.toStr(queryMap.get("manuCode")))) {
      params = SQLUtils.addLikeConditions(sql, params, "sop.c_manu_code", Convert.toStr(queryMap.get("manuCode")));
    }
    if (StrUtil.isNotBlank(Convert.toStr(queryMap.get("unit")))) {
      sql.append(" and sop.c_unit = ? ");
      params = ObjectUtils.addObjectToArray(params,  Convert.toStr(queryMap.get("unit")) );
    }
    params = SQLUtils.addLogicalOperators((LogicalOperatorsEnums) queryMap.get("orderNumOperators"),
        (BigDecimal) queryMap.get("orderNum"), sql, params, "sod.c_num");
    params = SQLUtils.addLogicalOperators((LogicalOperatorsEnums) queryMap.get("productPriceOperators"),
        (BigDecimal) queryMap.get("productPrice"), sql, params, "sod.c_price");
    if (queryMap.get("decimalRate") != null) {
      sql.append(" and sod.c_tax_rate = ? ");
      params = ObjectUtils.addObjectToArray(params,  queryMap.get("decimalRate") );
    }
    if (queryMap.get("taxFreeCbPriceOperators") != null && queryMap.get("taxFreeCbPrice") != null) {
      sql.append("and (").append("(sod.c_price/(1+sod.c_tax_rate))").append(" ")
          .append(((LogicalOperatorsEnums) queryMap.get("taxFreeCbPriceOperators")).getSymbol()).append(" ? )");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, queryMap.get("taxFreeCbPrice"));
    }
    if (queryMap.get("totalPriceOperators") != null && queryMap.get("totalPrice") != null) {
      sql.append("and (").append("(sod.c_price*sod.c_num)").append(" ")
          .append(((LogicalOperatorsEnums) queryMap.get("totalPriceOperators")).getSymbol()).append(" ? )");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, queryMap.get("totalPrice"));
    }
    if (queryMap.get("deliverTimeStart") != null) {
      sql.append(" and sod.c_deliver_time >= ? ");
      params = ObjectUtils.addObjectToArray(params,  queryMap.get("deliverTimeStart"));
    }
    if (queryMap.get("deliverTimeEnd") != null) {
      sql.append(" and sod.c_deliver_time <= ? ");
      params = ObjectUtils.addObjectToArray(params,  queryMap.get("deliverTimeEnd"));
    }
    params = SQLUtils.addLogicalOperators((LogicalOperatorsEnums) queryMap.get("shipQtyOperators"),
        (BigDecimal) queryMap.get("shipQty"), sql, params, "sod.c_ship_qty");
    params = SQLUtils.addLogicalOperators((LogicalOperatorsEnums) queryMap.get("stockInputQtyOperators"),
        (BigDecimal) queryMap.get("stockInputQty"), sql, params, "sod.c_stock_input_qty");
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("warehouse")))) {
      sql.append(" and sod.c_warehouse = ? ");
      params = ObjectUtils.addObjectToArray(params,  Convert.toStr(queryMap.get("warehouse")));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("warehouseName")))) {
      params = SQLUtils.addLikeConditions(sql, params, "sod.c_warehouse_name", Convert.toStr(queryMap.get("warehouseName")));
    }
    if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("description")))) {
      sql.append(" and sod.c_description like ? ");
      params = ObjectUtils.addObjectToArray(params,  StrUtil.wrap(Convert.toStr(queryMap.get("description")),"%"));
    }
    if (StrUtil.isNotBlank(Convert.toStr(queryMap.get("isWorryOrder")))) {
      sql.append(" and pafo.c_is_worry_order = ? ");
      params = ObjectUtils.addObjectToArray(params, Convert.toStr(queryMap.get("isWorryOrder")));
    }
    // loss
    if (queryMap.get("loss") != null) {
      if (Boolean.TRUE.equals(queryMap.get("loss"))) {
        sql.append(" and so.c_loss = ? ");
        params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, queryMap.get("loss"));
      }
      if (Boolean.FALSE.equals(queryMap.get("loss"))) {
        sql.append(" and ( so.c_loss = ? or so.c_loss is null ) ");
        params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, queryMap.get("loss"));
      }
    }
    if (queryMap.containsKey("selectSupplier") && queryMap.containsKey("permissionCode")) {
      Boolean selectSupplier = (Boolean) queryMap.get("selectSupplier");
      String permissionCode = Convert.toStr(queryMap.get("permissionCode"));
      if (!Boolean.TRUE.equals(selectSupplier) && StrUtil.equals(permissionCode, Constants.EXPORT_DEPT_TYPE)) {
        List<String> depteCurrUserList = (List<String>) queryMap.get("finalDepteCurrUserList");
        if (CollUtil.isNotEmpty(depteCurrUserList)) {
          params = HqlUtil.appendFieldIn(sql, params, "so.c_purchase_dept_code", depteCurrUserList);
        }
      }
    }
    if (CollUtil.isNotEmpty((List<String>) queryMap.get("exportPurchaseOrderIds"))) {
      params = HqlUtil.appendFieldIn(sql, params, "sod.id", (List<String>) queryMap.get(
          "exportPurchaseOrderIds"));
    }
    params = SQLUtils.addLogicalOperators((LogicalOperatorsEnums) queryMap.get("remainQtyOperators"),
        (BigDecimal) queryMap.get("remainQty"), sql, params, "sod.c_remain_qty");
    if (queryMap.get("purchaseDeliverTimeStart") != null) {
      sql.append(" and sod.c_purchase_deliver_time >= ? ");
      params = ObjectUtils.addObjectToArray(params,  queryMap.get("purchaseDeliverTimeStart"));
    }
    if (queryMap.get("purchaseDeliverTimeEnd") != null) {
      sql.append(" and sod.c_purchase_deliver_time <= ? ");
      params = ObjectUtils.addObjectToArray(params,  queryMap.get("purchaseDeliverTimeEnd"));
    }
    if (BooleanUtil.isTrue((Boolean) queryMap.get("isHistoricalOrder"))) {
      sql.append(" and so.c_order_type = ? ");
      params = ObjectUtils.addObjectToArray(params, PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    } else {
      sql.append(" and so.c_order_type != ? ");
      params = ObjectUtils.addObjectToArray(params, PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    }
   /* // 是否急单
    if (BooleanUtil.isTrue((Boolean) queryMap.get("isWorryOrder"))) {
      sql.append("and pafo.c_is_worry_order = ? ");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, BooleanUtil.isTrue((Boolean) queryMap.get("isWorryOrder")));
    }
    // 采购申请类型
    if (BooleanUtil.isTrue((Boolean) queryMap.get("applyForType"))) {
      sql.append("and pafo.c_apply_for_type = ? ");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, BooleanUtil.isTrue((Boolean) queryMap.get("applyForType")));
    }
    // 是否直发
    if (BooleanUtil.isTrue((Boolean) queryMap.get("directShipment"))) {
      sql.append("and pafo.c_direct_shipment = ? ");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params,BooleanUtil.isTrue((Boolean) queryMap.get("directShipment")));
    }*/
   /* // 采购部门名称
    if (BooleanUtil.isTrue((Boolean) queryMap.get("purchaseDepartment"))) {
      sql.append("and o.c_direct_shipment = ? ");
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params,BooleanUtil.isTrue((Boolean) queryMap.get("purchaseDepartment")));
    }*/

    // 表头筛选及多选覆盖 查询参数
    UnifiedSqlFactory tableHeadFactory = this.buildTableHead(queryMap);
    String tableHeadSql = tableHeadFactory.generateUnifiedSql();
    sql.append(tableHeadSql);
    // 使用 Stream API 合并 params 和 unifiedSqlParam
    params = Stream.concat(Stream.of(params), tableHeadFactory.getParams().stream())
        .toArray(Object[]::new);
    // 统一查询方案
    UnifiedSqlFactory querySchemeFactory = this.buildQueryScheme(queryMap);
    String querySchemeSql = querySchemeFactory.generateUnifiedSql();
    sql.append(querySchemeSql);
    // 使用 Stream API 合并 params 和 unifiedSqlParam
    params = Stream.concat(Stream.of(params), querySchemeFactory.getParams().stream())
        .toArray(Object[]::new);
    return params;
  }

  private UnifiedSqlFactory buildQueryScheme(Map<String, Object> queryMap) {
    UnifiedSqlFactory querySchemeFactory = new UnifiedSqlFactory(queryMap);
    // orderCode
    querySchemeFactory.addLike("orderCode", "so.c_code");
    // orderState
    querySchemeFactory.addComplex("orderState", (tempFactory, tempValue, operator) -> {
      SupplierOrderState orderState = (SupplierOrderState) tempValue;
      List<Object> tempParams = tempFactory.getParams();
      if (orderState.getOrderState().equals(SupplierOrderState.NOT_REVIEWED.getOrderState())) {
        tempFactory.appendSql(" so.c_order_state in (?,?,?,?) ");
        tempParams.addAll(ListUtil.of("3", "4", "5", "6"));
      } else if (orderState.getOrderState().equals(SupplierOrderState.REVIEWED.getOrderState())) {
        tempFactory.appendSql(" so.c_order_state in (?,?) ");
        tempParams.addAll(ListUtil.of(SupplierOrderState.WAIT.getOrderState(),
            SupplierOrderState.IN_PROGRESS.getOrderState()));
      } else {
        tempFactory.appendSql(" and so.c_order_state = ? ");
        tempParams.add(orderState.getOrderState());
      }
    });
    // startCreateTime
    querySchemeFactory.addNative("startCreateTime", "so.c_order_create_time");
    // endCreateTime
    querySchemeFactory.addNative("endCreateTime", "so.c_order_create_time");
    // supplierName
    querySchemeFactory.addLike("supplierName", "so.c_supplier_name");
    // productCode
    querySchemeFactory.addLike("productCode", "sop.c_code");
    // supplierOpenInvoiceState
    querySchemeFactory.addNative("supplierOpenInvoiceState", "so.c_supplier_open_invoice_state");
    // salesOrderNo
    querySchemeFactory.addLike("salesOrderNo", "so.c_sale_order_no");
    // largeTicketProjectName
    querySchemeFactory.addLike("largeTicketProjectName", "so.c_project_name");
    // largeTicketProjectNumbers
    querySchemeFactory.addLike("largeTicketProjectNumbers", "so.c_project_no");
    // orderType
    querySchemeFactory.addNative("orderType", "so.c_order_type");
    // purchaseMan
    querySchemeFactory.addLike("purchaseMan", "so.c_purchase_man");
    // purchaseDept
    querySchemeFactory.addLike("purchaseDept", "so.c_purchase_dept");
    // purchaseApplyCode
    querySchemeFactory.addLike("purchaseApplyCode", "pafo.c_apply_for_order_no");
    // prePay
    querySchemeFactory.addComplex("prePay", (tempFactory, tempValue, operator) -> {
      Boolean bool = (Boolean) tempValue;
      List<Object> tempParams = tempFactory.getParams();
      if (Boolean.TRUE.equals(bool)) {
        tempFactory.appendSql(
            " and (SELECT COUNT(*) FROM `t_payment_apply_record` WHERE `c_supplier_order_no` "
                + "like CONCAT('%',so.c_code,'%') and c_state = ? and c_apply_type= ? )>0 ");
        tempParams.add(Constants.STATE_OK);
        tempParams.add(PaymentApplyTypeEnums.ADVANCE.getKey());
      } else {
        tempFactory.appendSql(" and (SELECT COUNT(*) FROM `t_payment_apply_record` WHERE "
            + "`c_supplier_order_no` like CONCAT('%',so.c_code,'%') and c_state = ? and "
            + "c_apply_type= ? )=0 ");
        tempParams.add(Constants.STATE_OK);
        tempParams.add(PaymentApplyTypeEnums.ADVANCE.getKey());
      }
    });
    // uploadContract
    querySchemeFactory.addComplex("uploadContract", (tempFactory, tempValue, operator) -> {
      Boolean bool = (Boolean) tempValue;
      List<Object> tempParams = tempFactory.getParams();
      if (Boolean.TRUE.equals(bool)) {
        tempFactory.appendSql(" and (select COUNT(*) from t_file where c_relationId = so.id and "
            + "c_relationType = ? )>0 ");
        tempParams.add(Constants_FileRelationType.ORDER_CONTRACT);
      } else {
        tempFactory.appendSql(
            "and (select COUNT(*) from t_file where c_relationId = so.id and c_relationType = ? )=0");
        tempParams.add(Constants_FileRelationType.ORDER_CONTRACT);
      }
    });
    // scp
    querySchemeFactory.addNative("scp", "so.c_scp");
    // brand
    querySchemeFactory.addLike("brand", "sop.c_brand");
    // name
    querySchemeFactory.addLike("name", "sop.c_name");
    // manuCode
    querySchemeFactory.addLike("manuCode", "sop.c_manu_code");
    // unit
    querySchemeFactory.addNative("unit", "sop.c_unit");
    // orderNum
    querySchemeFactory.addNative("orderNum", "sod.c_num");
    // productPrice
    querySchemeFactory.addNative("productPrice", "sod.c_price");
    // decimalRate
    querySchemeFactory.addNative("decimalRate", "sod.c_tax_rate");
    // taxFreeCbPrice
    querySchemeFactory.addNative("taxFreeCbPrice", "(sod.c_price/(1+sod.c_tax_rate))");
    // totalPrice
    querySchemeFactory.addNative("totalPrice", "(sod.c_price*sod.c_num)");
    // deliverTimeStart
    querySchemeFactory.addNative("deliverTimeStart", "sod.c_deliver_time");
    // deliverTimeEnd
    querySchemeFactory.addNative("deliverTimeEnd", "sod.c_deliver_time");
    // shipQty
    querySchemeFactory.addNative("shipQty", "sod.c_ship_qty");
    // stockInputQty
    querySchemeFactory.addNative("stockInputQty", "sod.c_stock_input_qty");
    // warehouseName
    querySchemeFactory.addLike("warehouseName", "sod.c_warehouse_name");
    // salesMan
    querySchemeFactory.addComplex("salesman", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_salesman like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // followUpPersonName
    querySchemeFactory.addComplex("followUpPersonName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql("  and sop.c_follow_up_person_name like ?  ");
      tempParams.add("%" + tempValue + "%");
    });
    // description
    querySchemeFactory.addLike("description", "sod.c_description");
    // applyForType
    querySchemeFactory.addNative("applyForType", "pafo.c_apply_for_type");
    // businessCompanyName
    querySchemeFactory.addComplex("businessCompanyName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_business_company_name like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // makeManName
    querySchemeFactory.addComplex("makeManName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_make_man_name like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // isWorryOrder
    querySchemeFactory.addNative("isWorryOrder", "pafo.c_is_worry_order");
    // loss
    querySchemeFactory.addComplex("loss", (tempFactory, tempValue, operator) -> {
      Boolean loss = (Boolean) tempValue;
      List<Object> temParams = tempFactory.getParams();
      if (Boolean.TRUE.equals(loss)) {
        tempFactory.appendSql(" and so.c_loss = ? ");
        temParams.add(loss);
      }
      if (Boolean.FALSE.equals(loss)) {
        tempFactory.appendSql(" and ( so.c_loss = ? or so.c_loss is null ) ");
        temParams.add(loss);
      }
    });
    // remainQty
    querySchemeFactory.addNative("remainQty", "sod.c_remain_qty");
    // purchaseDeliverTimeStart
    querySchemeFactory.addNative("purchaseDeliverTimeStart", "sod.c_purchase_deliver_time");
    // purchaseDeliverTimeEnd
    querySchemeFactory.addNative("purchaseDeliverTimeEnd", "sod.c_purchase_deliver_time");
    return querySchemeFactory;
  }

  /**
   * 表头筛选及多选覆盖（todo 未来计划对buildWhere中所有字段进行支持）
   * @param oldQueryMap
   * @return
   */
  private UnifiedSqlFactory buildTableHead(Map<String, Object> oldQueryMap) {
    Map<String, Object> queryMap = new HashMap<>(oldQueryMap);
    UnifiedForm unifiedForm = new UnifiedForm();
    UnifiedChildForm unifiedChildForm = new UnifiedChildForm(UnifiedCombinationLogicEnums.AND);
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("orderStates", queryMap, SupplierOrderState.class));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("orderType", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("applyForType", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("supplierOpenInvoiceState", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("salesman", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("followUpPersonName", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("businessCompanyName", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("makeManName", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("productCode", queryMap));
    unifiedChildForm.getFilters().add(UnifiedFilter.appendTextContains("supplierName", queryMap));
    // 过滤掉null项
    unifiedChildForm.getFilters().removeIf(Objects::isNull);
    unifiedForm.getChildForms().add(unifiedChildForm);
    queryMap.put("unifiedForm", unifiedForm);
    UnifiedSqlFactory tableHeadFactory = new UnifiedSqlFactory(queryMap);
    // orderState
    tableHeadFactory.addComplex("orderStates", (tempFactory, tempValue, operator) -> {
      SupplierOrderState orderState = (SupplierOrderState) tempValue;
      List<Object> tempParams = tempFactory.getParams();
      if (orderState.getOrderState().equals(SupplierOrderState.NOT_REVIEWED.getOrderState())) {
        tempFactory.appendSql(" so.c_order_state in (?,?,?,?) ");
        tempParams.addAll(ListUtil.of("3", "4", "5", "6"));
      }else if (orderState.getOrderState().equals(SupplierOrderState.REVIEWED.getOrderState())){
        tempFactory.appendSql(" so.c_order_state in (?,?) ");
        tempParams.addAll(ListUtil.of(SupplierOrderState.WAIT.getOrderState(),SupplierOrderState.IN_PROGRESS.getOrderState()));
      } else {
        tempFactory.appendSql(" and so.c_order_state = ? ");
        tempParams.add(orderState.getOrderState());
      }
    });
    // orderType
    tableHeadFactory.addNative("orderType", "so.c_order_type");
    // applyForType
    tableHeadFactory.addNative("applyForType", "pafo.c_apply_for_type");
    // supplierOpenInvoiceState
    tableHeadFactory.addNative("supplierOpenInvoiceState", "so.c_supplier_open_invoice_state");
    // salesman
    tableHeadFactory.addComplex("salesman", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_salesman like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // followUpPersonName
    tableHeadFactory.addComplex("followUpPersonName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql("  and sop.c_follow_up_person_name like ?  ");
      tempParams.add("%" + tempValue + "%");
    });
    // businessCompanyName
    tableHeadFactory.addComplex("businessCompanyName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_business_company_name like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // makeManName
    tableHeadFactory.addComplex("makeManName", (tempFactory, tempValue, operator) -> {
      List<Object> tempParams = tempFactory.getParams();
      tempFactory.appendSql(" and sop.c_make_man_name like ? ");
      tempParams.add("%" + tempValue + "%");
    });
    // productCode
    tableHeadFactory.addLike("productCode", "sop.c_code");
    // supplierName
    tableHeadFactory.addLike("supplierName", "so.c_supplier_name");
    return tableHeadFactory;
  }


  @Override
  public List<String> findAllIds() {
    StringBuilder sql =
        new StringBuilder(
            "select sod.id from t_supplier_order_detail sod "+
            "left join t_supplier_order_to_form sotf "
                + "on sod.order_to_form_id = sotf.id "
                + "left join t_supplier_order so "
                + "on sotf.supplier_order_id = so.id "
        );
    sql.append("where so.c_state = ? and sod.c_state = ? and sotf.c_state=? and sotf.c_type = ? ");
    Object[] params = new Object[] {Constants.STATE_OK,Constants.STATE_OK,Constants.STATE_OK,
        SupplierOrderFormType.DETAILED.getType()};
    sql.append(" order by sod.c_create_time desc ");
    return getSqlObjList(sql.toString(), params);
  }

  @Override
  public List<SupplierOrderDetail> getByWarehousingFormId(String warehousingFormId) {
    Assert.notBlank(warehousingFormId);
    String sql = "select * from t_supplier_order_detail t1 "
        + "left join t_supplier_order_to_form t2 "
        + "on t1.order_to_form_id = t2.id "
        + "where (t2.c_sap_reversal_no is null or t2.c_sap_reversal_no='') "
        + "and t2.c_type = ? "
        + "and t1.c_in_warehouse_id = ? and t1.c_state = ? and t2.c_state = ? ";
    Object[] params = new Object[] {SupplierOrderFormType.RETURN.getType(),warehousingFormId,
        Constants.STATE_OK,Constants.STATE_OK};
    return getSqlList(sql,params);
  }

  @Override
  public BigDecimal getPurchaseOrderShipQty(String purchaseId) {
    StringBuilder sql =
        new StringBuilder(
            "select IFNULL(SUM(od.c_ship_qty) , 0) "
                + "FROM t_supplier_order_detail od "
                + "LEFT JOIN t_supplier_order_to_form sof "
                + "ON od.order_to_form_id = sof.id  "
                + "Where od.c_state = ? and sof.supplier_order_id = ? ");
    Object[] params = new Object[] {Constants.STATE_OK, purchaseId};
    return BigDecimalUtil.formatForStandard((BigDecimal) getFirstSqlObj(sql.toString(), params));
  }

  @Override
  public BigDecimal getTotalCancel(String type, String supplierOrderId) {
    String sql = "select IFNULL(sum(td.c_cancel_qty),0) from t_supplier_order_to_form t left join "
        + "t_supplier_order_detail "
        + "td on t.id = td.order_to_form_id where td.c_state = ? and t.c_type = ? "
        + "and t.supplier_order_id = ? ";

    Object[] params = new Object[] {Constants.STATE_OK, type, supplierOrderId};
    return BigDecimalUtil.formatForStandard((BigDecimal) getFirstSqlObj(sql, params));
  }

  @Override
  public BigDecimal getTotalReturn (String type, String supplierOrderId) {
    StringBuilder sql = new StringBuilder(
        "select IFNULL(sum(td.c_return_qty),0) from t_supplier_order_to_form t left join "
            + "t_supplier_order_detail "
            + "td on t.id = td.order_to_form_id where td.c_state = ? and t.c_type = ? "
            + "and t.supplier_order_id = ? and t.c_status <> ? and t.c_status <> ? and t.c_status"
            + " <> ? ");
    sql.append(" and (t.c_review_status is null or t.c_review_status = ? ) ");
    Object[] params = new Object[] {Constants.STATE_OK, type, supplierOrderId,
        SupplierOrderFormStatus.REVOKE.getKey(),SupplierOrderFormStatus.REVERSAL.getKey(),
        SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getKey(), SupplierOrderFormReviewStatus.NORMAL};
    return BigDecimalUtil.formatForStandard((BigDecimal) getFirstSqlObj(sql.toString(), params));
  }

  @Override
  public List<ProductDetailInfoDTO> productDetailInfo(String userGroup, String supplierId,
      List<String> orderIds,
      List<String> orderToFormIds,String orderNo,String productVoucher,String model, Boolean historyOrder) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList();
    sql.append("SELECT od.id,o.c_code orderNo,op.c_code productCode,op.c_name productName, ");
    sql.append("op.c_manu_code manuCode,odBase.c_price price,od.c_invoicable_num invoicableNum, ");
    sql.append("odBase.c_price/(1 + odBase.c_tax_rate) ratePrice,odBase.c_tax_rate taxRate, ");
    sql.append("od.c_price*od.c_invoicable_num includingTaxPrice,otf.c_product_voucher productVoucher, ");
    sql.append("o.c_account_period accountPeriod,o.c_supplier_name supplierName,o.c_invoice_type invoiceType, ");
    sql.append("op.c_specification specification,op.c_model model ");
    sql.append("FROM t_supplier_order_detail od ");
    sql.append("LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id ");
    sql.append("LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id ");
    sql.append("LEFT JOIN t_supplier_order_product op ON od.order_product_id = op.id ");
    sql.append("LEFT JOIN t_supplier_order_detail odBase ON od.detailed_id = odBase.id ");
    sql.append("WHERE o.c_state = ? and otf.c_type = ? AND od.c_invoicable_num > 0 ");
    params.add(Constants.STATE_OK);
    params.add(SupplierOrderFormType.WAREHOUSING.getType());
    sql.append(" and (otf.c_status != ? and otf.c_status != ? or otf.c_status is null) ");
    params.add(SupplierOrderFormStatus.REVERSAL.getStatus());
    params.add(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    sql.append(" and (otf.c_review_status is null or otf.c_review_status = ? ) ");
    params.add(SupplierOrderFormReviewStatus.NORMAL.getCode());
    if (StrUtil.isNotEmpty(supplierId)) {
      sql.append(" AND o.supplier_id = ? ");
      params.add(supplierId);
    }
    if (StrUtil.isNotEmpty(orderNo)) {
      sql.append(" AND o.c_code like ? ");
      params.add("%" + orderNo + "%");
    }
    if (StrUtil.isNotEmpty(productVoucher)) {
      sql.append(" AND otf.c_product_voucher like ? ");
      params.add("%" + productVoucher + "%");
    }
    if (StrUtil.isNotEmpty(model)) {
      sql.append(" AND op.c_manu_code like ? ");
      params.add("%" + model + "%");
    }

    if (CollUtil.isNotEmpty(orderIds)) {
      sql.append(" and o.id in ( ");
      for (String orderId : orderIds) {
        sql.append(" ?,");
        params.add(orderId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }
    if (CollUtil.isNotEmpty(orderToFormIds)) {
      sql.append(" and otf.id in ( ");
      for (String orderToformId : orderToFormIds) {
        sql.append(" ?,");
        params.add(orderToformId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }
    if (StrUtil.isNotBlank(userGroup)) {
      sql.append(" and o.c_group_code = ? ");
      params.add(userGroup);
    }
    // historyOrder
    if (BooleanUtil.isTrue(historyOrder)) {
      sql.append(" and o.c_order_type = ? ");
      params.add(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    } else {
      sql.append(" and o.c_order_type != ? ");
      params.add(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    }
    sql.append("order by o.c_code asc");
    return getSqlTypedObjList(sql.toString(), ProductDetailInfoDTO.class,params.toArray());
  }

  @Override
  public List<ProductDetailInfoDTO> getLinkProductDetail(List<String> orderIdList,
      List<String> orderToFormIdList) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("SELECT od.id,o.c_code orderNo,op.c_code productCode,op.c_name productName,op.c_manu_code manuCode, ");
    sql.append("odBase.c_price price, od.c_invoicable_num invoicableNum,odBase.c_price/(1 + odBase.c_tax_rate) ratePrice,odBase.c_tax_rate taxRate, ");
    sql.append("od.c_price*od.c_invoicable_num includingTaxPrice,otf.c_product_voucher productVoucher, ");
    sql.append("o.c_account_period accountPeriod,o.c_supplier_name supplierName,o.c_invoice_type invoiceType, ");
    sql.append("op.c_specification as specification, op.c_model as model ");
    sql.append("FROM t_supplier_order_detail od ");
    sql.append("LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id ");
    sql.append("LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id ");
    sql.append("LEFT JOIN t_supplier_order_product op ON od.order_product_id = op.id ");
    sql.append("LEFT JOIN t_supplier_order_detail odBase ON od.detailed_id = odBase.id ");
    sql.append("WHERE o.c_state = ? and otf.c_type = ? ");
    params.add(Constants.STATE_OK);
    params.add(SupplierOrderFormType.WAREHOUSING.getType());
    sql.append("and (otf.c_status != ? and otf.c_status != ? or otf.c_status is null)  ");
    params.add(SupplierOrderFormStatus.REVERSAL.getStatus());
    params.add(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    sql.append(" and (otf.c_review_status is null or otf.c_review_status = ? ) ");
    params.add(SupplierOrderFormReviewStatus.NORMAL.getCode());
    //关联订单/入库单，明细里需要过滤掉入库数量为0的物料行
    sql.append(" and (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) !=0 ");
    if (CollUtil.isNotEmpty(orderIdList)) {
      sql.append(" and o.id in ( ");
      for (String orderId : orderIdList) {
        sql.append(" ?,");
        params.add(orderId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }
    if (CollUtil.isNotEmpty(orderToFormIdList)) {
      sql.append(" and otf.id in ( ");
      for (String orderToformId : orderToFormIdList) {
        sql.append(" ?,");
        params.add(orderToformId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }


    sql.append("order by o.c_code asc");
    return getSqlTypedObjList(sql.toString(), ProductDetailInfoDTO.class, params.toArray());
  }

  @Override
  public List<ProductDetailInfoDTO> getReturnedMaterialsList(String supplierId,
      String productName, String productCode, String invoiceNumber, Boolean historyOrder) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("SELECT distinct od.id,o.c_code orderNo,op.c_code productCode,op.c_name productName,op");
    sql.append(".c_manu_code manuCode,odBase.c_price price, ");
    sql.append("COALESCE(od.c_stock_output_qty, 0) - COALESCE(od.c_invoiced_num, 0) invoicableNum,");
    sql.append("odBase.c_price/(1 + odBase.c_tax_rate) ratePrice,odBase.c_tax_rate taxRate,");
    sql.append("od.c_price * COALESCE(od.c_stock_output_qty, 0) - COALESCE(od.c_invoiced_num, ");
    sql.append("0) includingTaxPrice,otf.c_product_voucher ");
    sql.append("productVoucher,o.c_account_period accountPeriod,o.c_supplier_name ");
    sql.append("supplierName,o.c_invoice_type invoiceType, ");
    sql.append("od.c_in_warehouse_id inWarehouseId, ");
    sql.append("op.c_specification specification,op.c_model model");
    sql.append("FROM t_supplier_order_detail od ");
    sql.append("LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id ");
    sql.append("LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id ");
    sql.append("LEFT JOIN t_supplier_order_product op ON od.order_product_id = op.id ");
    sql.append("LEFT JOIN t_supplier_order_detail odBase ON od.detailed_id = odBase.id ");
    sql.append("LEFT JOIN t_supplier_invoice_to_detail itd on itd.c_detail_id = odBase.id ");
    sql.append("left join t_supplier_order_to_form otf2 on od.c_in_warehouse_id = otf2.id ");
    sql.append("left join t_supplier_order_detail od2 on otf2.id = od2.order_to_form_id ");
    sql.append("LEFT JOIN t_supplier_invoice_to_detail itd2 ON itd2.c_detail_id = od2.id ");
    sql.append("WHERE o.c_state in(?,?) and otf.c_type = ? AND ");
    sql.append("COALESCE(od.c_stock_output_qty, 0) - COALESCE(od.c_invoiced_num, 0) > 0 and (otf.c_status != ? and otf.c_status != ? and otf");
    sql.append(".c_need_red_ticket = ? and otf.c_product_voucher is not null) ");
    sql.append("and (otf.c_review_status is null or otf.c_review_status = ? ) ");

    params.add(Constants.STATE_OK);
    params.add(Constants.STATE_LOCKED);
    params.add(SupplierOrderFormType.RETURN.getType());
    params.add(SupplierOrderFormStatus.REVERSAL.getStatus());
    params.add(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    params.add(Constants.STATE_OK);
    params.add(SupplierOrderFormReviewStatus.NORMAL.getCode());

    if (StrUtil.isNotEmpty(supplierId)) {
      sql.append(" AND o.supplier_id = ? ");
      params.add(supplierId);
    }
    if (StrUtil.isNotEmpty(productName)) {
      sql.append(" AND op.c_name = ? ");
      params.add(productName);
    }
    if (StrUtil.isNotEmpty(productCode)) {
      sql.append(" AND op.c_code = ? ");
      params.add(productCode);
    }
    if (StrUtil.isNotEmpty(invoiceNumber)) {
      sql.append(" AND itd2.c_invoice_number = ? ");
      params.add(invoiceNumber);
    }
    if (Boolean.TRUE.equals(historyOrder)) {
      sql.append(" AND o.c_order_type = ? ");
      params.add(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    } else {
      sql.append(" AND o.c_order_type != ? ");
      params.add(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey());
    }
    sql.append(" order by otf.c_create_time asc, o.c_code asc ");
    return getSqlTypedObjList(sql.toString(), ProductDetailInfoDTO.class, params.toArray());
  }

  @Override
  public List<OrderProductDetailInfoDTO> getDetailInfoByOrderId(String id) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();

    sql.append("SELECT op.c_code productCode,op.c_name productName,op.c_manu_code manuCode,op.c_brand brand,");
    sql.append("(COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) as ");
    sql.append("settleQty,od.c_price price,odBase.c_tax_rate taxRate,odBase.c_price * (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) totalPrice, ");
    sql.append("op.c_specification specification,op.c_model model ");
    sql.append(" FROM t_supplier_order_detail od ");
    sql.append("LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id ");
    sql.append("LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id ");
    sql.append("LEFT JOIN t_supplier_order_product op ON od.order_product_id = op.id ");
    sql.append("LEFT JOIN t_supplier_order_detail odBase ON od.detailed_id = odBase.id ");
    sql.append("WHERE o.c_state = ? and o.id = ? AND otf.c_type = ? ");
    sql.append("and (otf.c_status NOT IN (?, ?, ?) OR otf.c_status IS NULL) ");
    //关联订单/入库单，明细里需要过滤掉入库数量为0的物料行
    sql.append("and (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) !=0 ");
    sql.append("and (otf.c_review_status is null or otf.c_review_status = ? ) ");

    params.add(Constants.STATE_OK);
    params.add(id);
    params.add(SupplierOrderFormType.WAREHOUSING.getType());
    params.add(SupplierOrderFormStatus.REVERSAL.getStatus());
    params.add(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    params.add(SupplierOrderFormStatus.CANCEL.getStatus());
    params.add(SupplierOrderFormReviewStatus.NORMAL.getCode());

    return getSqlTypedObjList(sql.toString(), OrderProductDetailInfoDTO.class, params.toArray());
  }

  @Override
  public List<OrderProductDetailInfoDTO> getWarehousingOrderDetail(String id) {
    StringBuilder sql =
        new StringBuilder(
            "SELECT od.id id,op.c_code productCode,op.c_name productName,op.c_manu_code manuCode,op.c_brand brand,"
                + "  (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) as "
                + "settleQty,od.c_price price,odBase.c_tax_rate taxRate,odBase.c_price * (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) totalPrice"
                + ", op.c_specification specification,op.c_model model "
                + " FROM t_supplier_order_detail od "
                + "LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id "
                + "LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id "
                + "LEFT JOIN t_supplier_order_product op ON od.order_product_id = op.id "
                + "LEFT JOIN t_supplier_order_detail odBase ON od.detailed_id = odBase.id "
                + "WHERE o.c_state = ? and otf.id = ? AND otf.c_type = ? ");
    //关联订单/入库单，明细里需要过滤掉入库数量为0的物料行
    sql.append(" and (COALESCE(od.c_stock_input_qty, 0) - COALESCE(od.c_stock_output_qty, 0)) !=0 ");
    sql.append(" and (otf.c_review_status is null or otf.c_review_status = ? ) ");
    Object[] params =
        new Object[] {Constants.STATE_OK, id,
            SupplierOrderFormType.WAREHOUSING.getType(),
            SupplierOrderFormReviewStatus.NORMAL.getCode()};
    return getSqlTypedObjList(sql.toString(), OrderProductDetailInfoDTO.class,params);
  }

  @Override
  public List<Object> getProductListByTableHeaderRef(Map<String, Object> queryMap) {
    StringBuilder sql =
        new StringBuilder(
            "select distinct ");
    PurchaseOrderProductFilterTypeEnum filterTypeEnum =
        PurchaseOrderProductFilterTypeEnum.getByType(Convert.toStr(queryMap.get("filterType")));
    sql.append(filterTypeEnum.getValue());
    sql.append(" from " + "t_supplier_order_detail sod "
        + "left join t_supplier_order_to_form sotf " + "on sod.order_to_form_id = sotf.id "
        + "left join t_supplier_order so " + "on sotf.supplier_order_id = so.id "
        + "left join t_supplier_order_product sop " + "on sod.order_product_id = sop.id ");
    sql.append(" left join t_purchase_apply_for_order pafo ");
    sql.append(" on sod.purchase_apply_for_order_id = pafo.id ");
    Object[] params = buildWhereQuery(sql, queryMap);
    sql.append(" group by ").append(filterTypeEnum.getValue());
    return getSqlObjList(sql.toString(),params);
  }


  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  public void updateOrderCode(String code, String supplierOpenInvoiceState) {
    String hql = "update t_supplier_order set c_supplier_open_invoice_state = ? where 1 = 1 ";
    Object[] params = new Object[] {supplierOpenInvoiceState};
    hql += "and (";
    List<String> split = StrUtil.split(code, ',', true, true);
    for (int i = 0; i < split.size(); i++) {
      String s = split.get(i);
      hql += " c_code like ? ";
      if (i != split.size() - 1) {
        hql += "or ";
      }
      params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, s);
    }
    hql += ") ";
    executeSqlUpdate(hql, params);
  }

  @Override
  public Page<SupplierOrderDetail> getOrderDetailPageByOrderId(
      String orderToFormId, String keyWord, Integer pageNo, Integer pageSize, List<String> ids) {
    String hql =
        "from SupplierOrderDetail  where state != ? and orderToFormId = ? and waitQty > ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderToFormId, BigDecimal.ZERO};
    if (StrUtil.isNotBlank(keyWord)) {
      hql +=
          "and (supplierOrderProduct.code like ? or supplierOrderProduct.brand like ? or supplierOrderProduct.name like ? or supplierOrderProduct.manuCode like "
              + "?) ";
      params =
          com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params =
          com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params =
          com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params =
          com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
    }
    if (CollUtil.isNotEmpty(ids)) {
      hql += " and id in( ";
      for (String type : ids) {
        hql += " ?,";
        params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, type);
      }
      hql = hql.substring(0, hql.length() - 1);
      hql += " ) ";
    }
    hql += " order by sortNum desc";
    return findPage(hql, params, pageNo, pageSize);
  }

  @Override
  public SupplierOrderDetail getFirstByPurchaseApplyForOrderIdNotInOrderStateOrderByCreateTimeDesc(
      String purchaseApplyForOrderId,String orderState) {
    StringBuilder sql = new StringBuilder(
        "SELECT DISTINCT od.* FROM t_supplier_order_detail od "
            + "LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id "
            + "LEFT JOIN t_supplier_order o ON otf.supplier_order_id = o.id "
            + "WHERE  o.c_state = ? and otf.c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK,Constants.STATE_OK};
    if (StrUtil.isNotBlank(orderState)) {
      sql.append(" and o.c_order_state != ? ");
      params = ObjectUtils.addObjectToArray(params, orderState);
    }
    if (StrUtil.isNotBlank(purchaseApplyForOrderId)) {
      sql.append(" and od.purchase_apply_for_order_id = ? ");
      params = ObjectUtils.addObjectToArray(params, purchaseApplyForOrderId);
    }
    sql.append(" order by od.c_purchase_deliver_time desc limit 1");
    return getUniqueSqlEntity(sql.toString(), params);
  }

  @Override
  public List<SupplierOrderDetail> findAvaibleWarehousingDetailByPurchaseOrderId(List<String> supplierOrderIds) {
    StringBuilder sql = new StringBuilder(
        "SELECT DISTINCT od.* FROM t_supplier_order_detail od "
            + "LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id "
            + "WHERE  od.c_state = ? and otf.c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK, Constants.STATE_OK};
    if (CollUtil.isNotEmpty(supplierOrderIds)) {
      sql.append(" and od.purchase_order_id in ( ");
      for (String supplierOrderId : supplierOrderIds) {
        sql.append(" ?,");
        params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, supplierOrderId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }
    sql.append(" and otf.c_type = ? ");
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormType.WAREHOUSING.getType());
    sql.append(" and (otf.c_status NOT IN (?, ?, ?) OR otf.c_status IS NULL) ");
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getStatus());
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.CANCEL.getStatus());
    sql.append(" and (otf.c_review_status is null or otf.c_review_status = ? ) ");
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormReviewStatus.NORMAL.getCode());
    return getSqlList(sql.toString(), params);
  }

  @Override
  public List<SupplierOrderDetail> findAvaibleOutDetailByPurchaseOrderId(List<String> supplierOrderIds) {
    StringBuilder sql = new StringBuilder(
        "SELECT DISTINCT od.* FROM t_supplier_order_detail od "
            + "LEFT JOIN t_supplier_order_to_form otf ON od.order_to_form_id = otf.id "
            + "WHERE  od.c_state = ? and otf.c_state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK, Constants.STATE_OK};
    if (CollUtil.isNotEmpty(supplierOrderIds)) {
      sql.append(" and od.purchase_order_id in ( ");
      for (String supplierOrderId : supplierOrderIds) {
        sql.append(" ?,");
        params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, supplierOrderId);
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
    }
    sql.append(" and otf.c_type = ? ");
    params =
        com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormType.RETURN.getType());
    sql.append(" and (otf.c_status NOT IN (?, ?, ?) OR otf.c_status IS NULL) ");
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getStatus());
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormStatus.CANCEL.getStatus());
    sql.append(" and (otf.c_review_status is null or otf.c_review_status = ? ) ");
    params = com.xhiot.boot.core.common.util.ObjectUtils.objectAdd(params, SupplierOrderFormReviewStatus.NORMAL.getCode());
    return getSqlList(sql.toString(), params);
  }


  @Override
  public BigDecimal getSupplierOrderReturnQty(String supplierOrderId) {
    StringBuilder sql =
        new StringBuilder(
            "select IFNULL(SUM(od.c_stock_output_qty) , 0) "
                + "FROM t_supplier_order_detail od "
                + "LEFT JOIN t_supplier_order_to_form sof "
                + "ON od.order_to_form_id = sof.id  "
                + "Where od.c_state = ? and sof.supplier_order_id = ? "
                + "and sof.c_type = ? ");
    Object[] params = new Object[] {Constants.STATE_OK, supplierOrderId,SupplierOrderFormType.DETAILED.getType()};
    return BigDecimalUtil.formatForStandard((BigDecimal) getFirstSqlObj(sql.toString(), params));
  }

  @Override
  public BigDecimal getReturnExchangeRemainNum(String supplierOrderId) {
    StringBuilder sql =
        new StringBuilder(
            "select IFNULL(SUM(od.c_num) , 0) - IFNULL(SUM(od.c_stock_output_qty) , 0) - IFNULL(SUM(od.c_cancel_qty) , 0) "
                + "FROM t_supplier_order_detail od "
                + "LEFT JOIN t_supplier_order_to_form sof "
                + "ON od.order_to_form_id = sof.id  "
                + "Where od.c_state = ? and sof.supplier_order_id = ? "
                + "and sof.c_type = ? ");
    Object[] params = new Object[] {Constants.STATE_OK, supplierOrderId,SupplierOrderFormType.DETAILED.getType()};
    return BigDecimalUtil.formatForStandard((BigDecimal) getFirstSqlObj(sql.toString(), params));
  }

  @Override
  public List<SearchSupplierOrderDetailDTO> findByProductCodeAndBatchNo(
      List<SearchSupplierOrderDetailForm> forms) {
    if (CollUtil.isEmpty(forms)) {
      return new ArrayList<>();
    }
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select distinct sop.c_code, ");
    sql.append("sod.c_batch_no, ");
    sql.append("sod.c_price, ");
    sql.append("sod.c_tax_rate ");
    sql.append("from t_supplier_order_detail sod ");
    sql.append("left join t_supplier_order_product sop on sop.id = sod.order_product_id ");
    sql.append("left join t_supplier_order_to_form sotf on sod.order_to_form_id = sotf.id ");
    sql.append("where sod.c_state = ? and sotf.c_state = ? ");
    sql.append(" and (sotf.c_review_status is null or sotf.c_review_status = ? ) ");
    sql.append("and sotf.c_type = ? ");
    params.add(Constants.STATE_OK);
    params.add(Constants.STATE_OK);
    params.add(SupplierOrderFormReviewStatus.NORMAL.getCode());
    params.add(SupplierOrderFormType.WAREHOUSING.getType());
    sql.append("and sotf.c_status != ? and sotf.c_status != ? ");
    params.add(SupplierOrderFormStatus.REVERSAL.getStatus());
    params.add(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
    sql.append("and ( ");
    for (int i = 0; i < forms.size(); i++) {
      SearchSupplierOrderDetailForm form = forms.get(i);
      sql.append("(sop.c_code = ? and sod.c_batch_no = ?) ");
      params.add(form.getProductCode());
      params.add(form.getBatchNo());
      if (i != forms.size() - 1) {
        sql.append("or ");
      }
    }
    sql.append(") ");
    List<Object[]> sqlObjList = getSqlObjList(sql.toString(), params.toArray());
    return sqlObjList.stream().map(item -> {
      SearchSupplierOrderDetailDTO dto = new SearchSupplierOrderDetailDTO();
      dto.setProductCode(Convert.toStr(item[0]));
      dto.setBatchNo(Convert.toStr(item[1]));
      dto.setPrice(Convert.toBigDecimal(item[2]));
      dto.setTax(Convert.toBigDecimal(item[3]));
      return dto;
    }).collect(Collectors.toList());
  }
}
