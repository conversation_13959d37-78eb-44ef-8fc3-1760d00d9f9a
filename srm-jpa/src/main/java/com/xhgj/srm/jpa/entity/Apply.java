package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "t_apply")
@Data
/**
 * 无用表
 */
@Deprecated
public class Apply implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "c_relationId")
    private String relationId;

    @Column(name = "c_userId")
    private String userId;

    @Column(name = "c_reason")
    private String reason;

    @Column(name = "c_description")
    private String description;

    @Column(name = "c_operation")
    private String operation;

    @Column(name = "c_operaType")
    private String operaType;

    @Column(name = "c_source")
    private String source;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;

}
