package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.OrderDeliveryDetailDao;
import com.xhgj.srm.jpa.entity.OrderDeliveryDetail;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderDeliveryDetailDaoImpl extends AbstractExtDao<OrderDeliveryDetail> implements OrderDeliveryDetailDao {

    @Override
    public List<OrderDeliveryDetail> getOrderDeliveryDetailByDeliveryId(String orderId) {
        String hql = "from OrderDeliveryDetail od where od.state != ? and od.delivery.id = ? order by od.createTime desc";
        Object[] params = new Object[]{Constants.STATE_DELETE, orderId};
        return getHqlList(hql, params);
    }

}
