package com.xhgj.srm.jpa.dao;


import com.xhgj.srm.jpa.entity.TemplateFieldConfig;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import java.util.List;

public interface TemplateFieldConfigDao extends BootBaseDao<TemplateFieldConfig> {

  /**
   * 根据状态和排除id查询
   * @param state 状态
   * @param excludeId id
   * @return List<TemplateFieldConfig>
   */
  List<TemplateFieldConfig> findByStateAndBigTypeAndExcludeId(String state,String bigType,
      String excludeId);

  /**
   * 根据id集合查询
   * @param ids
   * @return
   */
  List<TemplateFieldConfig> findByInId(List<String> ids);
}
