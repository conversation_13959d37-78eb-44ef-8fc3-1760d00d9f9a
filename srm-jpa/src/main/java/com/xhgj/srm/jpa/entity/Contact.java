package com.xhgj.srm.jpa.entity;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/** 联系人 */
@Entity
@Data
@Table(name = "t_contact")
public class Contact implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  @JoinColumn(name = "supplierId", insertable = false, updatable = false)
  @ManyToOne
  private Supplier supplier;

  @Column(name = "supplierId")
  private String supplierId;

  @JoinColumn(name = "supplierFbId")
  @ManyToOne
  private SupplierFb supplierFb;

  /** 组织内供应商 id */
  @Column(name = "supplier_in_group_id")
  private String supplierInGroupId;

  /** 姓名 */
  @Column(name = "c_name")
  private String name;

  /** 性别 */
  @Column(name = "c_sex")
  private String sex;

  /** 固话 */
  @Deprecated
  @Column(name = "c_tel")
  private String tel;

  /** 联系方式 */
  @Column(name = "c_phone")
  private String phone;

  /** 职务 */
  @Column(name = "c_duty")
  private String duty;

  /** 邮箱 */
  @Column(name = "c_mail")
  private String mail;

  /** 传真 */
  @Deprecated
  @Column(name = "c_fax")
  private String fax;

  /** 负责区域 */
  @Column(name = "c_area")
  private String area;

  @Column(name = "c_createTime")
  private Long createTime;

  @Column(name = "c_state")
  private String state;

  @Column(name = "c_createMan")
  private String createMan;

  /** 修改时间 */
  @Column(name = "c_update_time")
  private Long updateTime;
  /** 原始 id */
  @Column(name = "origin_id")
  private String originId;
  /**
   * 联系地址
   */
  @Column(name = "c_contact_address")
  private String contactAddress;

  /**
   * 是否默认：0：否；1：是
   */
  @Column(name = "c_is_default")
  private String isDefault;
}
