package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.enums.FieldConfigEnum;
import com.xhgj.srm.jpa.util.LazyLoadEntityListener;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name = "t_template_field_config")
@Entity
@Data
@EntityListeners(LazyLoadEntityListener.class)
public class TemplateFieldConfig implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 名称
   */
  @Column(name = "c_name")
  private String name;

  /**
   * 备注
   */
  @Column(name = "c_remark")
  private String remark;

  /**
   * 作用组织
   */
  @Column(name = "c_organization_role")
  private String organizationRole;

  /**
   * json 内容
   */
  @Column(name = "c_text")
  private String text;

  /**
   * 状态 0 删除 1 正常
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 大类。1-采购申请，2-采购订单
   * @see FieldConfigEnum
   */
  @Column(name = "c_big_type")
  private String bigType;


}
