# 共有8个级别，按照从低到高为：ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF。
Configuration:
  status: warn
  monitorInterval: 30
  # 定义全局变量
  Properties:
    # 缺省配置（用于开发环境）。其他环境需要在VM参数中指定，如下：
    Property:
      - name: log.level.console
        value: debug
      - name: log.path
        # 启动时使用 -Dlog.path=C:\oms-admin-prod 动态控制日志根目录
        value: /home/<USER>/srm-cloud/srm-api-mobile
      - name: project.name
        value: srm-api-mobile
      - name: log.pattern
        value: "%d{yyyy-MM-dd HH:mm:ss.SSS}[%-5p] [%X{TRACE_ID}] - %m : %l%n"
  Appenders:
    #输出到控制台
    Console:
      name: CONSOLE
      target: SYSTEM_OUT
      PatternLayout:
        pattern: ${sys:log.pattern}
      ThresholdFilter:
        level: ${sys:log.level.console}
        onMatch: ACCEPT
        onMismatch: DENY
    #   启动日志
    RollingFile:
      - name: LOG_DEBUG
        fileName: ${sys:log.path}/${sys:project.name}-debug.log
        filePattern: "${sys:log.path}/${sys:project.name}-debug-%i.log"
        PatternLayout:
          pattern: ${sys:log.pattern}
        Filters:
          # 一定要先去除不接受的日志级别，然后获取需要接受的日志级别
          ThresholdFilter:
            - level: INFO
              onMatch: DENY
              onMismatch: NEUTRAL
            - level: DEBUG
              onMatch: ACCEPT
              onMismatch: DENY
        Policies:
          # 按大小
          SizeBasedTriggeringPolicy:
            size: "5MB"
        # debug 历史文件最多 1 个
        DefaultRolloverStrategy:
          max: 1
      - name: LOG_INFO
        fileName: ${sys:log.path}/${sys:project.name}-info.log
        filePattern: "${sys:log.path}/historyLog/${sys:project.name}-info-%d{yyyy-MM-dd}-%i.log"
        PatternLayout:
          pattern: ${sys:log.pattern}
        Filters:
          # 一定要先去除不接受的日志级别，然后获取需要接受的日志级别
          ThresholdFilter:
            - level: warn
              onMatch: DENY
              onMismatch: NEUTRAL
            - level: info
              onMatch: ACCEPT
              onMismatch: DENY
        Policies:
          # 按天分类
          TimeBasedTriggeringPolicy:
            modulate: true
            interval: 1
        # 文件最多100个
        DefaultRolloverStrategy:
          max: 100
      - name: LOG_WARN
        fileName: ${sys:log.path}/${sys:project.name}-warn.log
        filePattern: "${sys:log.path}/historyLog/${sys:project.name}-warn-%d{yyyy-MM-dd}-%i.log"
        PatternLayout:
          pattern: ${sys:log.pattern}
        Filters:
          # 一定要先去除不接受的日志级别，然后获取需要接受的日志级别
          ThresholdFilter:
            - level: error
              onMatch: DENY
              onMismatch: NEUTRAL
            - level: warn
              onMatch: ACCEPT
              onMismatch: DENY
        Policies:
          # 按天分类
          TimeBasedTriggeringPolicy:
            modulate: true
            interval: 1
        # 文件最多100个
        DefaultRolloverStrategy:
          max: 100
      - name: LOG_ERROR
        fileName: ${sys:log.path}/${sys:project.name}-error.log
        filePattern: "${sys:log.path}/historyLog/${sys:project.name}-error-%d{yyyy-MM-dd}-%i.log"
        PatternLayout:
          pattern: ${sys:log.pattern}
        Filters:
          # 一定要先去除不接受的日志级别，然后获取需要接受的日志级别
          ThresholdFilter:
            - level: ERROR
              onMatch: ACCEPT
              onMismatch: DENY
        Policies:
          # 按天分类
          TimeBasedTriggeringPolicy:
            modulate: true
            interval: 1
        # 文件最多100个
        DefaultRolloverStrategy:
          max: 100
      - name: LOG_BOOT_INFO
        ignoreExceptions: false
        fileName: ${sys:log.path}/${sys:project.name}_boot_info.log
        filePattern: "${sys:log.path}/boot/historyLog/${sys:project.name}_boot_ifo-%d{yyyy-MM-dd}-%i.log.gz"
        PatternLayout:
          pattern: ${sys:log.pattern}
        ThresholdFilter:
          - level: INFO
            onMatch: ACCEPT
            onMismatch: DENY
        Policies:
          TimeBasedTriggeringPolicy: # 按天分类
            modulate: true
            interval: 1
        DefaultRolloverStrategy: # 文件最多100个
          max: 100
      - name: LOG_BOOT_WARN
        ignoreExceptions: false
        fileName: ${sys:log.path}/${sys:project.name}_boot_warn.log
        filePattern: "${sys:log.path}/boot/historyLog/${sys:project.name}_boot_warn-%d{yyyy-MM-dd}-%i.log.gz"
        PatternLayout:
          pattern: ${sys:log.pattern}
        ThresholdFilter:
          - level: WARN
            onMatch: ACCEPT
            onMismatch: DENY
        Policies:
          TimeBasedTriggeringPolicy: # 按天分类
            modulate: true
            interval: 1
        DefaultRolloverStrategy: # 文件最多100个
          max: 100
      - name: LOG_BOOT_ERROR
        ignoreExceptions: false
        fileName: ${sys:log.path}/${sys:project.name}_boot_error.log
        filePattern: "${sys:log.path}/boot/historyLog/${sys:project.name}_boot_error-%d{yyyy-MM-dd}-%i.log.gz"
        PatternLayout:
          pattern: ${sys:log.pattern}
        ThresholdFilter:
          - level: ERROR
            onMatch: ACCEPT
            onMismatch: DENY
        Policies:
          TimeBasedTriggeringPolicy: # 按天分类
            modulate: true
            interval: 1
        DefaultRolloverStrategy: # 文件最多100个
          max: 100


  Loggers:
    Root:
      level: info
      AppenderRef:
        - ref: CONSOLE
        - ref: LOG_DEBUG
        - ref: LOG_INFO
        - ref: LOG_WARN
        - ref: LOG_ERROR
    Logger:
      - name: com.xhiot.boot
        additivity: false
        AppenderRef:
          - ref: CONSOLE
          - ref: LOG_BOOT_INFO
          - ref: LOG_BOOT_WARN
          - ref: LOG_BOOT_ERROR