package com.xhgj.srm.mobile.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName ContactAddParam Create by Liuyq on 2021/6/7 11:13
 */
@Data
public class ContactAddParam {
  @NotBlank(message = "供应商id不能为空")
  @ApiModelProperty("供应商id")
  private String supplierId;

  @NotBlank(message = "账户id不能为空")
  @ApiModelProperty("账户id")
  private String supplierUserId;

  @ApiModelProperty("联系人列表")
  @Valid
  private List<ContactListAddParam> contactList;
}
