package com.xhgj.srm.mobile.dto.login;

import com.xhgj.srm.jpa.entity.SupplierUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName SupplierData Create by Liuyq on 2021/6/8 15:57
 */
@Data
public class SupplierUserData {
  @ApiModelProperty("供应商id")
  private String id;

  @ApiModelProperty("姓名")
  private String name;

  @ApiModelProperty("手机")
  private String phone;

  @ApiModelProperty("邮箱")
  private String mail;

  @ApiModelProperty("用户名")
  private String realName;

  public SupplierUserData(SupplierUser supplierUser) {
    this.id = supplierUser.getId();
    this.name = supplierUser.getName();
    this.phone = supplierUser.getMobile();
    this.mail = supplierUser.getMail();
    this.realName = supplierUser.getRealName();
  }
}
