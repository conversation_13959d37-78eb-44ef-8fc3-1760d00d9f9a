package com.xhgj.srm.mobile.service;

import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.mobile.dto.BrandAddParam;
import com.xhgj.srm.mobile.dto.BrandAuthorizationAddParam;
import com.xhgj.srm.mobile.dto.BrandAuthorizationData;
import com.xhgj.srm.mobile.dto.BrandAuthorizationPageData;
import com.xhgj.srm.mobile.dto.BrandAuthorizationUpdateParam;
import com.xhgj.srm.mobile.dto.BrandDelParam;
import com.xhgj.srm.mobile.dto.BrandPage;
import com.xhgj.srm.mobile.dto.BrandPageData;
import com.xhgj.srm.mobile.dto.login.ArrayBaseParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

/**
 * @ClassName BrandService Create by Liuyq on 2021/6/3 9:34
 */
public interface BrandService extends BootBaseService<Brand, String> {

  /**
   * 根据搜索条件获取品牌列表 @Author: liuyq @Date: 2021/6/23 17:18
   *
   * @param supplierId
   * @param brandName
   * @param manageType
   * @param isPermission
   * @param schemeId
   * @param pageNo
   * @param pageSize
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.api.dto.BrandPageData>
   */
  PageResult<BrandPageData> getBrandPage(
      String supplierId,
      String brandName,
      String manageType,
      String isPermission,
      String schemeId,
      int pageNo,
      int pageSize);

  /**
   * 新增品牌 @Author: liuyq @Date: 2021/6/3 10:08
   *
   * @param addParam
   * @return com.xhgj.srm.jpa.entity.Brand
   */
  void addBrand(BrandAddParam addParam);

  /**
   * 新增品牌授权 @Author: liuyq @Date: 2021/6/4 11:35
   *
   * @param addParam
   * @return com.xhgj.srm.jpa.entity.Brand
   */
  void addBrandAuthorization(BrandAuthorizationAddParam addParam);

  /**
   * 根据搜索条件获取品牌授权列表 @Author: liuyq @Date: 2021/6/4 14:51
   *
   * @param supplierId
   * @param fileName
   * @param brandName
   * @param supplierName
   * @param type
   * @param schemeId
   * @param pageNo
   * @param pageSize
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.api.dto.BrandAuthorizationPageData>
   */
  BrandPage<BrandAuthorizationPageData> getBrandAuthorizationPage(
      String supplierId,
      String fileName,
      String brandName,
      String supplierName,
      String type,
      String schemeId,
      String pageNo,
      String pageSize);

  /**
   * 修改品牌授权 @Author: liuyq @Date: 2021/6/4 16:55
   *
   * @param updateParam
   * @return com.xhgj.srm.jpa.entity.File
   */
  void updateBrandAuthorization(BrandAuthorizationUpdateParam updateParam);

  /**
   * 获取mdm品牌 @Author: liuyq @Date: 2021/6/15 10:27
   *
   * @param search
   * @param pageNo
   * @param pageSize
   * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.api.dto.MdmBrandPageData>
   */
  PageResult<MdmBrandPageData> getMdmBrandPage(
      String supplierId, String search, int pageNo, int pageSize);

  /**
   * 供应商品牌复制到副本表
   *
   * @param supplierId
   * @param supplierFb
   * @param copyLogoAndRemark
   */
  void copySupplierBrandToSupplierFb(
      String supplierId, SupplierFb supplierFb, boolean copyLogoAndRemark);

  /**
   * 删除品牌授权 @Author: liuyq @Date: 2021/6/18 18:05
   *
   * @param deleteParam
   * @return void
   */
  void deleteBrandAuthorizationById(ArrayBaseParam deleteParam);

  /**
   * 获取品牌授权详情 @Author: liuyq @Date: 2021/6/21 9:28
   *
   * @param id
   * @return com.xhgj.srm.api.dto.BrandAuthorizationData
   */
  BrandAuthorizationData getBrandAuthorizationById(String id);

  /**
   * 删除品牌 @Author: liuyq @Date: 2021/6/21 9:47
   *
   * @param deleteParam
   * @return void
   */
  void deleteBrandById(BrandDelParam deleteParam);
}
