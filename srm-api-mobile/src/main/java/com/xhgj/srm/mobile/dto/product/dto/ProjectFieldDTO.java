package com.xhgj.srm.mobile.dto.product.dto;

import com.xhgj.srm.request.dto.product.KeyAndValueDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-04-23 17:00
 */
@Data
public class ProjectFieldDTO {

  @ApiModelProperty("字段 id")
  private String id;

  @ApiModelProperty("字段")
  private String fieldKey;

  @ApiModelProperty("字段名")
  private String fieldName;

  @ApiModelProperty("当前值")
  private String fieldValue;

  @ApiModelProperty("控件类型")
  private String controlType;

  @ApiModelProperty("取值范围")
  private List<KeyAndValueDTO> rangeList;
}
