package com.xhgj.srm.mobile.domain;

import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.security.component.BootUserDetails;
import java.util.Collections;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/9/15 16:51
 */
@Getter
public class SrmUserDetails extends BootUserDetails {

  private static final long serialVersionUID = 7769493342948860728L;
  private final User user;

  public SrmUserDetails(User user) {
    super(user.getId(), true, user.getPassword(), user.getName(), Collections.emptyList());
    this.user = user;
  }

}
