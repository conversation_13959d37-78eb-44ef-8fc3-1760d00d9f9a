package com.xhgj.srm.mobile.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/13 10:48
 */
@Data
public class BrandNameParamDTO {
  @ApiModelProperty("品牌中文名称")
  @NotBlank(message = "品牌中文名称不能为空")
  private String brandNameCn;

  @ApiModelProperty("品牌英文名称")
  @NotBlank(message = "品牌英文名称不能为空")
  private String brandNameEn;

  @ApiModelProperty("品牌 mdmId")
  private String brandMdmId;

  @ApiModelProperty("品牌 logo")
  private String logoPic;
}
