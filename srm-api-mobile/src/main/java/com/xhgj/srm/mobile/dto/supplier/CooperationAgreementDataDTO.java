package com.xhgj.srm.mobile.dto.supplier;

import com.xhgj.srm.mobile.dto.CooperationAgreementData;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/12 10:39
 */
@Data
public class CooperationAgreementDataDTO {

  @ApiModelProperty("合作协议")
  private List<CooperationAgreementData> cooperationAgreementDatas;

  @ApiModelProperty("合作协议等级")
  private String level;

  @ApiModelProperty("是否是模板必填")
  private Boolean require;

  @ApiModelProperty("所属组织")
  private String groupName;

  @ApiModelProperty("组织下供应商 id")
  private String supplierInGroupId;
}
