package com.xhgj.srm.request.service.third.erp.sap.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * SapMaterialCardInquiriesParam
 */
@Data
public class SapMaterialCardInquiriesParam {

  @ApiModelProperty("采购组织")
  @NotBlank(message = "采购组织必填")
  private String bukrs;

  @ApiModelProperty("订单名称")
  private String ktext;

  @ApiModelProperty("资料卡片名称")
  private String anlhtxt;

  @ApiModelProperty("关联销售订单")
  private String kdauf;

}
