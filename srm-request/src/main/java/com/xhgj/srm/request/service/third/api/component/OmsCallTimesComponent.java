package com.xhgj.srm.request.service.third.api.component;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;

/**
 * <AUTHOR>
 * OMS API每日调用记录
 */
@Component
@Slf4j
public class OmsCallTimesComponent {

  @Resource
  private RedisTemplate<String, Object> redisTemplate;

  private static final String KEY_PREFIX = "srm2oms_api_call_count";

  public Long incrementPathCount(String path) {
    String key = generateKey(path);
    return redisTemplate.opsForValue().increment(key, 1);
  }

  private String generateKey(String path) {
    String today = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
    return KEY_PREFIX + ":" + path + ":" + today;
  }

  @Scheduled(cron = "0 0 0 * * ?")  // Every day at midnight
  public void resetCountsDaily() {
    LocalDate yesterday = LocalDate.now().minusDays(1);  // 获取昨天的日期
    String keyPattern = KEY_PREFIX + ":" + "*:" + yesterday.format(DateTimeFormatter.ISO_DATE);

    Set<String> keys = redisTemplate.keys(keyPattern);
    if (keys != null && !keys.isEmpty()) {
      redisTemplate.delete(keys);
    }
  }

}
