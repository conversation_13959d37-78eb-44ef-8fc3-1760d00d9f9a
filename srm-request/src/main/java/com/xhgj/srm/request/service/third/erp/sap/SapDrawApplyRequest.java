package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam;
import com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Date;

/**
  *@ClassName SapDrawApplyRequest
  *<AUTHOR>
  *@Date 2024/1/8 20:01
*/
@Component
@Slf4j
public class SapDrawApplyRequest  extends BaseSapRequest {
  /**
   * 提款申请
   * @param param
   * @return
   * @throws RuntimeException
   * @deprecated 请使用 {@link SAPServiceImpl#sapDraw(SapDrawApplyParam)} 方法
   *
   */
  @Deprecated
  public boolean sapDrawApply(SapDrawApplyParam param)
      throws RuntimeException {
    Assert.notNull(param);
    AdvanceApplyResult result;
    try {
      String responseBody = postSap(SAPMethod.ZFM_MM_066,param);
      result =
          JSON.parseObject(responseBody, new TypeReference<AdvanceApplyResult>() {});
      if(StrUtil.equals(result.getReturnX().getType(),Constants_Sap.SUCCESS_TYPE)){
        return true;
      }else {
        throw new CheckException("调用sap接口失败:" + result.getReturnX().getMsg());
      }
    }catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException("SAP网络异常请求失败，请联系管理员处理。请求时间"+ DateUtil.format(new Date(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
  }
}
