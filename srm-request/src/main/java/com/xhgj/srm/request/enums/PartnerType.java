package com.xhgj.srm.request.enums;

import com.xhiot.boot.core.common.util.dict.BootDictEnum;

/**
 * 合作商类型
 *
 * <AUTHOR>
 * @since 2022/7/20 18:56
 */
public enum PartnerType implements BootDictEnum<String, String> {
  /** 国内 */
  CHINA("1", "国内"),
  /** 海外 */
  ABROAD("2", "海外"),
  /** 个人 */
  PERSON("3", "个人"),
  /** 内部 */
  INTERNAL("4", "内部");

  /** 合作商类型 */
  private final String key;

  /** 合作商类型名称 */
  private final String value;

  PartnerType(String key, String value) {
    this.key = key;
    this.value = value;
  }

  @Override
  public String getKey() {
    return key;
  }

  @Override
  public String getValue() {
    return value;
  }
}
