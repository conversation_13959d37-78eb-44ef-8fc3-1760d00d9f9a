package com.xhgj.srm.common.utils;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 并行处理工具类，目前适用场景：entity --> vo，稳定有序。
 * author: GengShy
 */

public class ParallelProcessUtil {
  private ParallelProcessUtil(){}

  /**
   * @param threadNum 使用的线程数量
   * @param collection 需要处理的原始集合
   * @param executor 执行器
   * @param convertFunction 结果转换函数
   * @param <T> 原始集合元素类型
   * @param <R> 转换之后的集合元素类型
   * @return 并行处理之后的集合
   * @throws IllegalArgumentException 线程数不合法时
   * @throws NullPointerException 集合或者执行器为空时
   */
  public static <T,R> List<List<R>> parallelProcessOfCollection(int threadNum,
      Collection<T> collection, Executor executor, Function<T, R> convertFunction) {
    Objects.requireNonNull(collection);
    Objects.requireNonNull(executor);
    if (CollUtil.isEmpty(collection)) return Collections.emptyList();
    if (threadNum <= 0) throw new IllegalArgumentException("线程数不能小于等于0");
    if (threadNum > collection.size()) threadNum = 1;
    List<List<T>> lists = splitCollection(collection, threadNum);
    LinkedList<CompletableFuture<List<R>>> completableFutures = new LinkedList<>();
    for (List<T> list : lists) {
      CompletableFuture<List<R>> completableFuture = CompletableFuture.supplyAsync(() ->{
        return list.stream().map(convertFunction).collect(Collectors.toList());
      }, executor);
      completableFutures.add(completableFuture);
    }
    CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()])).join();
    return completableFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());
  }

  public static <T,R> List<List<R>> simpleParallelProcessOfCollection(Collection<T> collection,
      Executor executor, Function<T, R> convertFunction) {
    return parallelProcessOfCollection(Runtime.getRuntime().availableProcessors(), collection,
        executor, convertFunction);
  }

  /**
   * 二维集合扁平化
   * @param collections 二维集合
   * @param <T> 集合元素类型
   * @return 转换之后的一维集合
   */
  public static <T> List<T> flattenTwoDimensionalList(Collection<?
      extends Collection<? extends T>> collections) {
    return collections.stream().flatMap(Collection::stream).collect(Collectors.toList());
  }

  private static <T> List<List<T>> splitCollection(Collection<T> collection, int splitNum) {
    int splitSize = collection.size() / splitNum;
    int remainder = collection.size() % splitNum;
    ArrayList<List<T>> results = new ArrayList<>();
    int startIndex = 0;
    int endIndex = splitSize;
    for (int i = 0; i < splitNum; i++) {
      results.add(doSplitCollection(collection, startIndex, endIndex));
      startIndex = startIndex + splitSize;
      endIndex = endIndex + splitSize;
    }
    if (remainder != 0) {
      List<T> ts = doSplitCollection(collection, collection.size() - remainder, collection.size());
      results.add(ts);
    }
    return results;
  }


  private static <T> List<T> doSplitCollection(Collection<T> collection,
      int startIndex, int endIndex) {
    if (startIndex < 0 || startIndex >= endIndex || endIndex > collection.size()) {
      throw new IllegalArgumentException("startIndex or endIndex is illegal");
    }
    List<T> subList = new ArrayList<>(endIndex - startIndex);
    Iterator<T> iterator = collection.iterator();
    int index = 0;
    while (iterator.hasNext() && index < endIndex) {
      if (index >= startIndex) {
        subList.add(iterator.next());
      }else {
        iterator.next();
      }
      index++;
    }
    return subList;
  }


}
