package com.xhgj.srm.common.enums.inventorySafety;/**
 * @since 2025/2/19 14:44
 */

/**
 *<AUTHOR>
 *@date 2025/2/19 14:44:29
 *@description
 */
public enum InventorySafetyStatus {
  /**
   * 关闭
   */
  CLOSE((byte) -1, "关闭"),
  /**
   * 开启
   */
  OPEN((byte) 1, "开启")
  ;

  private Byte code;

  private String name;

  InventorySafetyStatus(Byte code, String name) {
    this.code = code;
    this.name = name;
  }

  public static String getValueByCode(Byte code) {
    for (InventorySafetyStatus value : InventorySafetyStatus.values()) {
      if (value.getCode().equals(code)) {
        return value.getName();
      }
    }
    return null;
  }

  public static Byte getCodeByName(String name) {
    for (InventorySafetyStatus value : InventorySafetyStatus.values()) {
      if (value.getName().equals(name)) {
        return value.getCode();
      }
    }
    return null;
  }

  public Byte getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
