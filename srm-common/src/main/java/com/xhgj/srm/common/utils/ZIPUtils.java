package com.xhgj.srm.common.utils;/**
 * @since 2024/11/21 13:31
 */

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 *<AUTHOR>
 *@date 2024/11/21 13:31:22
 *@description
 */
public class ZIPUtils {
  /**
   * 文件批量压缩为 ZIP 格式，不保存到磁盘，而是返回字节数组。
   *
   * @param byteList 文件内容的 Map，键为文件名，值为文件字节内容
   * @return 压缩后的 ZIP 数据字节数组
   */
  public static byte[] batchFileToZIP(Map<String, byte[]> byteList) {
    // 判断是否有重复，重复则添加序号
    Map<String, Integer> map = new HashMap<>();
    try (ByteArrayOutputStream out = new ByteArrayOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out)) {

      for (Map.Entry<String, byte[]> entry : byteList.entrySet()) {
        String fileName = entry.getKey();
        byte[] fileContent = entry.getValue();

        // 校验文件名和内容合法性
        if (fileName == null || fileName.trim().isEmpty()) {
          throw new IllegalArgumentException("文件名不能为空");
        }
        if (fileContent == null) {
          throw new IllegalArgumentException("文件内容不能为空: " + fileName);
        }
        ZipEntry zipEntry = null;
        int count = map.getOrDefault(fileName, 0);
        map.put(fileName, count + 1);
        if (count > 0) {
          fileName = fileName.substring(0, fileName.lastIndexOf('.')) + "(" + count + ")"
              + fileName.substring(fileName.lastIndexOf('.'));
          zipEntry = new ZipEntry(fileName);
        } else {
          zipEntry = new ZipEntry(fileName);
        }
        // 创建 ZIP 条目
        zipOutputStream.putNextEntry(zipEntry);
        zipOutputStream.write(fileContent);
        zipOutputStream.closeEntry(); // 每次写完后关闭条目
      }

      zipOutputStream.finish(); // 明确完成压缩流
      return out.toByteArray(); // 返回压缩结果

    } catch (IOException e) {
      e.printStackTrace();
      throw new RuntimeException("压缩文件失败", e);
    }
  }
}

