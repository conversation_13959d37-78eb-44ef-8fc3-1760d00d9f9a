package com.xhgj.srm.common.utils.returnExchangeOrder;/**
 * @since 2025/2/18 9:52
 */
import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.utils.runner.JedisUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;

/**
 *<AUTHOR>
 *@date 2025/2/18 09:52:30
 *@description
 */
public class OrderNumberCleanerAndGenerator {
  // 单例实例
  public static final OrderNumberCleanerAndGenerator INSTANCE =
      new OrderNumberCleanerAndGenerator();
  private static final ThreadLocal<String> ORDER_NEW = ThreadLocal.withInitial(() -> null);
  private static final JedisUtil jedisUtil = JedisUtil.getInstance();
  private static final int RANGE_START = 1;
  private static final int RANGE_END = 99;
  private static final String REDIS_ORDER_USED_KEY_PREFIX  = "srm_used_order_numbers:";
  private LocalDate now;

  public static void clear() {
    ORDER_NEW.remove();
  }

  // 获取当前日期（yyyy-MM-dd）
  private String getCurrentDate() {
    now = LocalDate.now();
    return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
  }

  // 获取当天 Redis 键名
  private String getRedisKeyForUsedOrders() {
    return REDIS_ORDER_USED_KEY_PREFIX + getCurrentDate();
  }

  public synchronized String generate() {
    String orderNumber = generateOrderNumber();
    if (orderNumber == null) {
      throw new CheckException("本日退换货单流水号已用完");
    }
    String datetime = now.format(DateTimeFormatter.ofPattern("yyMMdd"));
    return "29" + datetime + orderNumber;
  }

  // 生成一个新的订单号
  private String generateOrderNumber() {
    String redisKey = getRedisKeyForUsedOrders();
    // 尝试生成一个未使用的单号
    // 获取已使用的单号集合
    Set<String> usedOrderNumbers = jedisUtil.smembers(redisKey);
    usedOrderNumbers = CollUtil.emptyIfNull(usedOrderNumbers);
    // 尝试生成一个未使用的单号
    for (int i = RANGE_START; i <= RANGE_END; i++) {
      String orderNumber = String.format("%02d", i);
      if (!usedOrderNumbers.contains(orderNumber)) {
        // 如果该单号没有被使用，加入到已使用集合中
        jedisUtil.sadd(redisKey, orderNumber);
        ORDER_NEW.set(orderNumber);
        return orderNumber;
      }
    }
    return null;
  }

  // 将单号回退到 Redis 列表中
  public void rollbackOrderNumber() {
    if (ORDER_NEW.get() != null) {
      String redisKey = getRedisKeyForUsedOrders();
      // 将失败的单号从已使用集合中删除
      jedisUtil.srem(redisKey, ORDER_NEW.get());
    }
  }

  // 清空昨日的
  public void clearYesterdayOrderNumbers(LocalDate cronTime) {
    // 获取昨天的日期
    LocalDate yesterday = cronTime.minusDays(1);
    String yesterdayDateStr = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    // 构建 Redis 键的前缀，删除昨天的数据
    String redisKeyForYesterday = REDIS_ORDER_USED_KEY_PREFIX + yesterdayDateStr;

    // 删除该日期下的所有记录
    jedisUtil.del(redisKeyForYesterday);
  }
}
