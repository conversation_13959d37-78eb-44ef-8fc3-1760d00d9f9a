package com.xhgj.srm.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
  *@ClassName PayStateSAPEnums
  *<AUTHOR>
  *@Date 2024/1/12 11:01
*/
@Getter
@AllArgsConstructor
@SuppressWarnings("ALL")
public enum PayStateSAPEnums {
  //sap 返回的付款状态 产品说后面可能会用
//  PREPARATION("010", "准备中"),
//  REJECTED("020", "已拒绝"),
//  APPROVAL("030", "审批中"),
//  APPROVE_PAYMENT("040", "批准支付"),
//  PAYMENT_PROGRESS("050", "支付进行中"),
//  PAYMENT_VOUCHER_CREATED("060", "付款凭证已创建"),
//  CLOSED("070", "已关闭"),
//  WARNING("080", "警告"),
//  EXCEPTION("090", "异常"),
//  PAY_BANK("100", "支付至银行"),
//  BANK_PROCESSED("110", "银行已处理"),
//  ADD_PROCESSING_PROJECT("120", "添加至处理项目"),
//  BANK_REFUSES_PAYMENT("130", "银行拒绝付款"),
//  SENT("150", "已发送"),
//  TRANSFER_DRAFT("900", "传输至汇票");

  WAIT_SUBMITTED("a", "待提交直联"),
  SUBMITTED("b", "已提交直联"),
  ACCEPTED("c", "银行已受理"),
  UNACCEPTED("d", "银行未受理"),
  SUSPICIOUS("e", "可疑"),
  WAIT_CONFIRM("f", "待人工确认"),
  PAY_SUCCEMM("g", "支付成功");


  /**
   * 支付类型
   */
  private final String code;

  /**
   * 名称
   */
  private final String name;

  /**
   * 通过code获取枚举
   *
   * @param code 省份code
   * @return ProvinceEnum
   */
  public static String getNameByCode(String code) {
    for (PayStateSAPEnums value : PayStateSAPEnums.values()) {
      if(StrUtil.equals(value.getCode(),code)){
        return value.name;
      }
    }
    return null;
  }

  public static String getCodeByName(String name) {
    PayStateSAPEnums[] values = PayStateSAPEnums.values();
    for (PayStateSAPEnums value : PayStateSAPEnums.values()) {
      if(StrUtil.equals(value.getName(),name)){
        return value.code;
      }
    }
    return null;
  }
}
