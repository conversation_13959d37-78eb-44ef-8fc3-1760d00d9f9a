package com.xhgj.srm.common.enums;

/**
 * 钉钉各接口地址
 * Created by <PERSON><PERSON> on 2023/9/4
 */
public enum DingTalkUrlEnum {

  CREATE_APPROVE("https://api.dingtalk.com/v1.0/workflow/processInstances", "创建审批实例"),
  GET_USER_INFO("https://oapi.dingtalk.com/topapi/v2/user/get", "获取用户详情"),
  GET_INSTANCE_DETAIL("https://api.dingtalk.com/v1.0/workflow/processInstances", "获取单个审批实例详情"),
  AGREE_OR_REJECT_TASK("https://api.dingtalk.com/v1.0/workflow/processInstances/execute", "同意或拒绝审批任务");


  final String url;
  final String desc;

  private DingTalkUrlEnum(String url, String desc) {
    this.url = url;
    this.desc = desc;
  }

  public String getUrl() {
    return url;
  }
  public String getDesc() {
    return desc;
  }

}
