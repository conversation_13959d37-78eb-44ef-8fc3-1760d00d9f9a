package com.xhgj.srm.common.enums.supplierUser;/**
 * @since 2025/4/14 18:48
 */

/**
 *<AUTHOR>
 *@date 2025/4/14 18:48:55
 *@description 供应商用户账号权限
 */
public enum SupplierUserPermission {
  /**
   * 1 电商供应商权限
   */
  ECOMMERCE_SUPPLIER("1", "电商供应商"),
  /**
   * 2 自营供应商权限
   */
  SELF_SUPPLIER("2", "自营供应商"),
  ;
  private String code;
  private String name;

  SupplierUserPermission(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public static String getNameByCode(String code) {
    for (SupplierUserPermission permission : SupplierUserPermission.values()) {
      if (permission.getCode().equals(code)) {
        return permission.getName();
      }
    }
    return null;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
