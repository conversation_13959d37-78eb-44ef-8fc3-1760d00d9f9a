package com.xhgj.srm.common.enums;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONType;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 2023/8/11
 */
@JSONType(serializeEnumAsJavaBean = true)
public enum InvoiceTypeEnum {
  VAT_ELECTRONIC_SPECIAL("增值税电子专用发票", "20", "vat_electronic_special_invoice"),
  VAT_SPECIAL("增值税专用发票", "01", "vat_special_invoice"),
  ELECTRONIC_VAT_SPECIAL("电子发票（增值税专用发票）", "31", "vat_electronic_special_invoice_new"),
  VAT_ORDINARY_INVOICE("增值税普通发票", "04", "vat_common_invoice"),
  VAT_ELECTRONIC_INVOICE("增值税电子普通发票", "10", "vat_electronic_invoice"),
  VAT_ELECTRONIC_INVOICE_NEW("电子发票（普通发票）", "32", "vat_electronic_invoice_new"),
  OTHER("其他", "-1", "other"),
  BLOCKCHAIN_ELECTRONIC_INVOICE("区块链电子发票", "-2", "blockchain_electronic_invoice");
  private final String description;
  private final String key;
  private final String identifier;

  InvoiceTypeEnum(String description, String key, String identifier) {
    this.description = description;
    this.key = key;
    this.identifier = identifier;
  }

  public String getDescription() {
    return description;
  }

  public String getKey() {
    return key;
  }

  public String getIdentifier() {
    return identifier;
  }

  public static InvoiceTypeEnum fromDescription(String description) {
    for (InvoiceTypeEnum type : values()) {
      if (type.getDescription().equals(description)) {
        return type;
      }
    }
    return null;
  }

  public static InvoiceTypeEnum fromKey(String key) {
    for (InvoiceTypeEnum type : values()) {
      if (type.getKey().equals(key)) {
        return type;
      }
    }
    return null;
  }

  public static InvoiceTypeEnum fromIdentifier(String identifier) {
    for (InvoiceTypeEnum type : values()) {
      if (type.getIdentifier().equals(identifier)) {
        return type;
      }
    }
    return null;
  }

  private static final Map<String, InvoiceTypeEnum> ENUM_MAP = new HashMap<>();
  static {
    for (InvoiceTypeEnum type : InvoiceTypeEnum.values()) {
      ENUM_MAP.put(type.getIdentifier(), type);
      ENUM_MAP.put(type.getKey(), type);
      ENUM_MAP.put(type.getDescription(), type);
    }
  }

  public static InvoiceTypeEnum getByDescription(String description) {
    return ENUM_MAP.getOrDefault(description, null);
  }

  public static InvoiceTypeEnum getByKey(String key) {
    return ENUM_MAP.getOrDefault(key, null);
  }

  public static InvoiceTypeEnum getByIdentifier(String identifier) {
    return ENUM_MAP.getOrDefault(identifier, null);
  }

  /**
   * 根据key判断是否为普通发票
   * @return
   */
  public static boolean isCommonInvoice(String key) {
    return StrUtil.equalsAny(key, VAT_ORDINARY_INVOICE.getKey(), VAT_ELECTRONIC_INVOICE.getKey(),
        VAT_ELECTRONIC_INVOICE_NEW.getKey());
  }

  @Override
  public String toString() {
    return "InvoiceTypeEnum{" + "description='" + description + '\'' + ", key='" + key + '\''
        + ", identifier='" + identifier + '\'' + '}';
  }
}
