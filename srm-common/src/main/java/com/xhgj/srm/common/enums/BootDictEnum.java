package com.xhgj.srm.common.enums;

import lombok.Getter;

/**
 * @Author: fanghuanxu
 * @Date: 2025/4/29 10:46
 * @Description: 数据字典大key枚举
 */
@Getter
public enum BootDictEnum {
  // 采购订单类型
  PROCUREMENT_ORDER("SRM.PURCHASE_ORDER_TYPE", "采购订单类型"),
  // 发票种类（用于采购订单）
  INVOICE_TYPE_FOR_PURCHASE_ORDER("SRM.INVOICE_TYPE.FOR_PURCHASE_ORDER", "发票种类（用于采购订单）"),
  // 采购用途
  PURCHASE_USE("SRM.PURCHASE_PURPOSES", "采购用途"),
  // 项目类别
  PROJECT_TYPE("SRM.PROJECT_TYPE", "项目类别"),
  // 科目分配类别
  SUBJECT_ALLOCATION_TYPE("SRM.SUBJECT_ALLOCATION_TYPE", "科目分配类别"),
  // SRM采购申请类型
  PROCUREMENT_APPLICATION("SRM.PURCHASE_APPLY_TYPE", "SRM采购申请类型"),
  // SRM账期
  SRM_ACCOUNT_PERIOD("SRM.ACCOUNT_PERIOD", "SRM账期"),
  ;

  private final String key;
  private final String desc;
  BootDictEnum(String key,String desc) {
    this.key = key;
    this.desc = desc;
  }
}
