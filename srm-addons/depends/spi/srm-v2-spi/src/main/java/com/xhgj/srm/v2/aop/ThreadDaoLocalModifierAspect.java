package com.xhgj.srm.v2.aop;/**
 * @since 2025/4/17 19:18
 */

import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

/**
 *<AUTHOR>
 *@date 2025/4/17 19:18:11
 *@description
 */
@Aspect
@Component
@Slf4j
public class ThreadDaoLocalModifierAspect {

  /**
   * 是否为此版本添加的version
   */
  private static final ThreadLocal<Boolean> ASPECT_ADD = new ThreadLocal<>();

  // 1. 切点：你自己 v2 包里的接口
  @Pointcut("execution(* com.xhgj.srm.v2.repository..*.*(..))")
  private void v2RepoInterface() {}

  // 2. 切点：Spring Data JPA 实现类，但仅限 v2 包
  @Pointcut(
      "execution(* org.springframework.data.repository.CrudRepository+.*(..))"
          + " && within(com.xhgj.srm.v2.repository..*)"
  )
  private void v2RepoImpl() {}

  // 3. 还包括你自己的 DAO 包
  @Pointcut("execution(* com.xhgj.srm.v2.dao..*.*(..))")
  private void v2Dao() {}


  @Before("v2RepoInterface() || v2RepoImpl() || v2Dao()")
  public void modifyThreadLocalBeforeExecution() {
    if (ShardingContext.getVersion() == null) {
      log.info("ThreadDaoLocalModifierAspect modifyThreadLocalBeforeExecution");
      ASPECT_ADD.set(true);
      ShardingContext.setVersion(VersionEnum.V2);
    }
  }

  @After("v2RepoInterface() || v2RepoImpl() || v2Dao()")
  public void cleanupThreadLocal() {
    if (Boolean.TRUE.equals(ASPECT_ADD.get())) {
      log.info("ThreadDaoLocalModifierAspect cleanupThreadLocal");
      ShardingContext.clear();
      ASPECT_ADD.remove();
    }
  }
}
