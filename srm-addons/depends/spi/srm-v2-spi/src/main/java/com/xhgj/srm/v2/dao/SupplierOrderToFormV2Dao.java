package com.xhgj.srm.v2.dao;/**
 * @since 2025/4/28 11:33
 */

import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.v2.dto.RetreatWarehousePageV2DTO;
import com.xhgj.srm.v2.dto.WarehousingV2DTO;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *<AUTHOR>
 *@date 2025/4/28 11:33:41
 *@description
 */
public interface SupplierOrderToFormV2Dao extends BootBaseDao<SupplierOrderToFormV2> {

  /**
   *
   * @param type
   * @param statusList
   * @param supplierOrderId
   * @return
   */
  List<SupplierOrderToFormV2> getSupplierOrderFormByTypeAndStatus(
      String type, List<String> statusList, String supplierOrderId);

  /**
   * 根据供应商订单 id 和单据类型单据状态获取发货单数量
   */
  long getSumSupplierOrderFormByTypeAndStatus(String type,
      String supplierOrderId, List<String> statusList);

  /**
   * 根据单子类型、冲销状态和供应商订单 id 获得对应的单据
   *
   * @param type 类型 必传
   * @param supplierOrderId 供应商订单 id 必传
   */
  List<SupplierOrderToFormV2> getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAscSQL(
      String type, String supplierOrderId);


  /**
   *  @Author: liuyq @Date: 2022/12/14 15:05
   *
   * @param type 单据类型
   * @param supplierOrderId 供应商id，必传
   * @return java.util.List<com.xhgj.srm.jpa.entity.SupplierOrderToForm>
   */
  List<SupplierOrderToFormV2> getSupplierOrderFormByTypeAndState(
      String type, String supplierOrderId);


  PageResult<WarehousingV2DTO> warehousingPageRef(Map<String, Object> queryMap);

  PurchaseOrderWarehousingStatistics warehousingStatistics2(Map<String, Object> queryMap);

  /**
   * v2退库单数据统计
   * @param queryMap
   * @return
   */
  PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics2(Map<String, Object> queryMap);

  /**
   * 分页查询退库单
   */
  PageResult<RetreatWarehousePageV2DTO> outBoundDeliveryPageRef(Map<String, Object> queryMap);
}
