package com.xhgj.srm.open.service;

import com.xhgj.srm.open.form.brand.OpenSupplierBrandQueryForm;
import com.xhgj.srm.open.form.brand.OpenSupplierBrandSaveForm;
import com.xhgj.srm.open.form.product.OpenProductQueryForm;
import com.xhgj.srm.open.form.product.OpenProductSaveForm;
import com.xhgj.srm.open.vo.brand.OpenSupplierBrandAuditVO;
import com.xhgj.srm.open.vo.brand.OpenSupplierBrandVO;
import com.xhgj.srm.open.vo.product.OpenProductListVO;
import com.xhgj.srm.open.vo.product.OpenProductVO;
import com.xhiot.boot.mvc.base.PageResult;

/**
 * <AUTHOR>
 */
public interface OpenProductService {
  /**
   * 查询品牌审核详情
   */
  OpenSupplierBrandAuditVO getSupplierBrandAuditDetail(String brandCode);

  /**
   * 分页获取品牌信息
   * @param form
   * @return
   */
  PageResult<OpenSupplierBrandVO> getSupplierBrandPage(OpenSupplierBrandQueryForm form);

  /**
   * 保存品牌信息
   * @param form
   */
  void saveSupplierBrand(OpenSupplierBrandSaveForm form);

  /**
   * 查询MPM物料列表
   * @param form
   * @return
   */
  PageResult<OpenProductListVO> getMPMProductList(OpenProductQueryForm form);

  /**
   * 查询MPM物料详情
   * @param code
   * @return
   */
  OpenProductVO getMPMProductDetail(String code);

  /**
   * 查询审核物料详情
   */
  OpenProductVO getAuditProductDetail(String code);

  /**
   * 保存商品
   * @param form
   */
  void saveProduct(OpenProductSaveForm form);
}
