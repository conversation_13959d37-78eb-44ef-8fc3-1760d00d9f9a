package com.xhgj.srm.registration.dto.entryregistration;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EntryRegistrationOrderAddParam {

  @ApiModelProperty("合作信息")
  @NotNull(message = "请填写合作信息")
  @Valid
  private EntryRegistrationOrderDTO entryRegistrationOrder;
  @ApiModelProperty("电商供应商信息")
  @Valid
  private EntryRegistrationLandingMerchantDTO entryRegistrationLandingMerchant;
  @ApiModelProperty("组织")
  @NotNull(message = "请填写组织")
  private String userGroup;
}
