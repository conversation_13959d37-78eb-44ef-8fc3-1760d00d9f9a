package com.xhgj.srm.registration.factory;/**
 * @since 2024/12/5 17:13
 */
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDetailDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantAddParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantgetDetailDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderDetailDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 *<AUTHOR>
 *@date 2024/12/5 17:13:45
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {
  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * updateEntryRegistrationEntity
   * @param source
   * @param target
   * @return
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  @Mapping(target = "supplyFile", ignore = true)
  @Mapping(target = "supplyFileInput", source = "supplyFile")
  void updateEntryRegistrationEntity(EntryRegistrationOrderDTO source, @MappingTarget
  EntryRegistrationEntity target);

  /**
   * updateEntryRegistrationEntity
   * @param source
   * @param target
   * @return
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationEntity(EntryRegistrationOrder source, @MappingTarget
  EntryRegistrationEntity target);

  /**
   * updateEntryRegistrationLandingMerchantDTO
   * @param source
   * @param target
   * @return
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationLandingMerchantDTO(EntryRegistrationLandingMerchantAddParam source,
      @MappingTarget EntryRegistrationLandingMerchantDTO target);

  /**
   * updateEntryRegistrationLandingMerchantDTO
   * @param source
   * @param target
   * @return
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationLandingMerchantDTO(EntryRegistrationLandingMerchant source,
      @MappingTarget EntryRegistrationLandingMerchantDTO target);

  /**
   * updateEntryRegistrationOrder
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationOrder(EntryRegistrationEntity source, @MappingTarget EntryRegistrationOrder target);

  /**
   * updateEntryRegistrationDiscount
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationDiscount(EntryRegistrationDiscountDTO source, @MappingTarget EntryRegistrationDiscount target);

  /**
   * updateEntryRegistrationLandingMerchant
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = org.mapstruct.NullValuePropertyMappingStrategy.IGNORE)
  void updateEntryRegistrationLandingMerchant(EntryRegistrationLandingMerchantDTO source, @MappingTarget EntryRegistrationLandingMerchant target);


  /**
   * EntryRegistrationLandingMerchant to EntryRegistrationLandingMerchantDTO
   * @param source
   * @return
   */
  EntryRegistrationLandingMerchantgetDetailDTO toEntryRegistrationLandingMerchantgetDetailDTO(EntryRegistrationLandingMerchant source);

  /**
   * EntryRegistrationDiscount to EntryRegistrationDiscountDetailDTO
   * @param source
   * @return
   */
  EntryRegistrationDiscountDetailDTO toEntryRegistrationDiscountDetailDTO(EntryRegistrationDiscount source);

  /**
   * EntryRegistrationEntity to EntryRegistrationOrderDetailDTO
   * @param source
   * @return
   */
  @Mapping(target = "step", source = "step", ignore = true)
  EntryRegistrationOrderDetailDTO toEntryRegistrationOrderDetailDTO(EntryRegistrationEntity source);
}
