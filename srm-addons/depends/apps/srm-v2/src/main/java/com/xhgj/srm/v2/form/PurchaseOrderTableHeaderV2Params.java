package com.xhgj.srm.v2.form;

import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * PurchaseOrderTableHeaderV2Params
 */
@Data
public class PurchaseOrderTableHeaderV2Params extends PurchaseApplyForOrderV2QueryForm{

  /**
   * 订货状态OrderGoodsStateV2Enum
   * {@link com.xhgj.srm.common.enums.purchase.order.PurchaseApplyFilterTypeV2Enum}
   */

  @ApiModelProperty("筛选类型: 1.业务员，2.跟单员，3.采购员，4.物料编码，5.品牌，6.型号，7.是否急单，8.是否直发，9.采购申请类型，10.采购部门，"
      + "11.订货状态,12:申请单备注，13：物料名称，14：物料备注，15：规格，16：物料序号")
  @NotBlank(message = "筛选类型 必传")
  private String filterType;

  public Map<String, Object> toQueryMap(MergeUserPermission mergeUserPermission) {
    Map<String, Object> queryMap = super.toQueryMap(mergeUserPermission);
    queryMap.put("filterType", this.filterType);
    return queryMap;
  }
}
