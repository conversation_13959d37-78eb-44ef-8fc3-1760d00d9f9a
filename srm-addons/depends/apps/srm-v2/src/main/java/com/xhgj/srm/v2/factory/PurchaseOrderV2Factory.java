package com.xhgj.srm.v2.factory;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.dtflys.forest.http.ForestBody;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.http.ForestURL;
import com.dtflys.forest.http.body.NameValueRequestBody;
import com.dtflys.forest.http.body.ObjectRequestBody;
/**
 * @since 2025/4/29 14:27
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.BootDictEnum;
import com.xhgj.srm.common.enums.PaymentTermsEnum;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.VoucherTypeEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus;
import com.xhgj.srm.common.utils.OrderNumUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.BaseSupplierOrderDetail;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.entity.v2.PurchaseOrderPaymentTermsV2;
import com.xhgj.srm.jpa.entity.v2.PurchaseOrderPaymentTermsV2.PurchaseOrderPaymentTermsV2Builder;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderSyncRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrderOriginData;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm.SupplierOrderDetailProcessForm;
import com.xhgj.srm.v2.form.feida.SupplierOrderProcessForm.SupplierOrderFileProcessForm;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.AnnexParam;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.EntrustProduct;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.ProductDetailAdd;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.PurchaseOrderPaymentTermsAddParam;
import com.xhgj.srm.v2.helper.PurchaseOrderFeidaProcessCompareHelper;
import com.xhgj.srm.v2.provider.TemplateFieldConfigProvider;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhgj.srm.v2.repository.PurchaseOrderPaymentTermsV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository.SupplierOrder2DetailProjectionV2;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.dict.core.service.BootDictService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/4/29 14:27:04
 *@description
 */
@Component
public class PurchaseOrderV2Factory {

  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private FinancialVoucherRepository financialVoucherRepository;
  @Resource
  private BootDictService bootDictService;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private PurchaseOrderPaymentTermsV2Repository purchaseOrderPaymentTermsV2Repository;
  @Resource
  private FileDao fileDao;
  @Resource
  private PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  private SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  private HZeroProcessConfig hZeroProcessConfig;
  @Resource
  private SupplierOrderSyncRepository supplierOrderSyncRepository;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private SrmConfig srmConfig;
  @Resource
  private TemplateFieldConfigProvider templateFieldConfigProvider;
  @Resource
  private UserRepository userRepository;

  /**
   * #check 校验采购部门
   */
  public void checkPurchaseDeptCode(PurchaseOrderAddV2Form form, SupplierOrderV2 supplierOrder) {
    if (BooleanUtil.isTrue(form.getSap085Flag())) {
      return;
    }
    Group group =
        groupRepository.findFirstByErpCodeAndState(form.getPurchaseDeptCode(), Constants.STATE_OK);
    if (group == null) throw new CheckException("采购部门不存在！");
    //修改的情况
    if (supplierOrder != null) {
      if (!Objects.equals(supplierOrder.getGroupCode(), group.getGroupCode())) {
        throw new CheckException("采购部门不属于该采购单的采购组织");
      }
    }//新增的情况
    else if (!Objects.equals(form.getUserGroup(), group.getGroupCode())) {
      throw new CheckException("采购部门不属于该采购组织");
    }
  }


  /**
   * #check 是否走scp校验
   * @param form
   */
  public void checkSelfScp(PurchaseOrderAddV2Form form) {
    boolean fieldValid = templateFieldConfigProvider.isFieldShow(form.getUserGroup(), form.getOrderType(), "scp");
    String orderTypeDesc =
        bootDictService.getDictValueByKey(BootDictEnum.PROCUREMENT_ORDER.getKey(), form.getOrderType());
    if (fieldValid) {
      if (!Boolean.TRUE.equals(form.getScp())) {
        throw new CheckException(orderTypeDesc + "是否走scp必须为是");
      }
    }
  }


  /**
   * #check 检查采购订单付款条件入参是否合法
   */
  public void checkPurchaseOrderPaymentTerms(List<PurchaseOrderPaymentTermsAddParam> params) {
    if (CollUtil.isEmpty(params)) return;
    for (PurchaseOrderPaymentTermsAddParam param : params) {
      Set<String> condition = param.getCondition();
      //当用户未选择付款条件时默认为订单审核通过
      if (CollUtil.isEmpty(condition)) {
        // 修复BUG: 如果condition为null，需要先初始化
        if (condition == null) {
          condition = new HashSet<>();
          param.setCondition(condition);
        }
        condition.add(PaymentTermsEnum.ORDER_APPROVED.getKey());
      }
    }
    List<Set<String>> conditions = params.stream().map(PurchaseOrderPaymentTermsAddParam::getCondition)
        .collect(Collectors.toList());
    Set<Set<String>> uniqueSets = new HashSet<>();
    for (Set<String> set : conditions) {
      if (!uniqueSets.add(set)) {
        throw new CheckException("不能选择重复的付款条件！");
      }
    }
  }

  /**
   * # check 检查采购订单明细入参是否合法
   * @param productList
   */
  public void checkTaxRate(List<ProductDetailAdd> productList) {
    if (CollUtil.isEmpty(productList)) {
      return;
    }
    // 过滤掉免费行
    productList = productList.stream()
        .filter(item -> !Constants.YES.equals(item.getFreeState()))
        .collect(Collectors.toList());
    BigDecimal orginRate = null;
    // 校验税率一致性
    for (ProductDetailAdd product : productList) {
      BigDecimal taxRate = product.getTaxRate()  == null ? BigDecimal.ZERO : product.getTaxRate();
      if (orginRate == null) {
        orginRate = taxRate;
      }
      if (taxRate.compareTo(orginRate) != 0) {
        throw new CheckException("订单物料行的税率必须一致，请检查订单税率");
      }
    }
  }

  /**
   * # check 查询是否有取消单
   */
  public void checkCancelOrder(String supplierOrderId) {
    Long count = supplierOrderToFormV2Repository.countByTypeAndSupplierOrderIdAndState(
        SupplierOrderFormType.CANCEL.getType(), supplierOrderId, Constants.STATE_OK);
    if (count > 0) {
      throw new CheckException("该订单存在取消单无法进行编辑");
    }
  }

  /**
   * # check 是否为首次提交
   */
  public boolean checkFirstSubmit(Integer saveType, String orderState) {
    // 原订单状态是否为暂存
    boolean originIsStaging =
        SupplierOrderState.STAGING.getOrderState().equals(orderState);
    // 暂存
    boolean saveTypeStaging = Constants.SAP_INVOICE_STAGING.equals(saveType);
    // 提交
    boolean saveTypeSubmit = Constants.SAP_INVOICE_SUBMIT.equals(saveType);
    if (saveTypeStaging) {
      if (!SupplierOrderState.STAGING.getOrderState().equals(orderState)) {
        throw new CheckException("非暂存订单无法进行暂存");
      }
    }
    return saveTypeSubmit && !originIsStaging;
  }

  /**
   * # check 首次提交校验
   * @param supplierOrderId
   * @param productSize
   * @param supplierName
   * @param supplierId
   * @param originSupplierName
   * @param originSupplierId
   */
  public void checkSupplierOrderFirstSubmit(String supplierOrderId,
      Integer productSize,
      String supplierName,
      String supplierId,
      String originSupplierName,
      String originSupplierId
  ) {
    // 获取该订单的一条订单明细
    SupplierOrderToFormV2 supplierOrderToForm =
        supplierOrderToFormV2Repository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.DETAILED.getType(), supplierOrderId, Constants.STATE_OK);
    if (supplierOrderToForm == null) {
      throw new CheckException("未获取到订单明细单据");
    }
    if (supplierOrderDetailV2Repository.countByOrderToFormIdAndState(supplierOrderToForm.getId(), Constants.STATE_OK)
        > productSize
    ) {
      throw new CheckException("提交的订单行数不能小于已存在的行数");
    }
    if (ObjectUtil.notEqual(supplierName,originSupplierName)
        || ObjectUtil.notEqual(supplierId,originSupplierId)) {
      throw new CheckException("提交订单时不允许修改供应商信息");
    }
  }

  /**
   * # check 校验修改后的订单金额不能小于已预付金额
   */
  public void checkOrderAmount(String purchaseOrderCode, BigDecimal totalAmountIncludingTax) {
    // 获取预付金额
    BigDecimal actualPrepaidAmount = this.getActualPrepaidAmount(purchaseOrderCode);
    // 修改后的订单金额不能小于已预付金额
    if (NumberUtil.isGreater(actualPrepaidAmount, totalAmountIncludingTax)) {
      throw new CheckException("订单已预付"+actualPrepaidAmount.stripTrailingZeros().toPlainString()+
          "元，修改后的订货金额不能小于已预付金额!");
    }
  }

  /**
   * # check 校验亏本单--动态根据模板判断
   */
  public void checkLossOrder(PurchaseOrderAddV2Form form) {
    boolean fieldValid = templateFieldConfigProvider.isFieldShow(form.getUserGroup(), form.getOrderType(), "loss");
    if (!BooleanUtil.isTrue(form.getLoss()) && fieldValid) {
        List<String> rowError = new ArrayList<>();
        for (int i = 0; i < form.getProductList().size(); i++) {
          ProductDetailAdd supplierProduct = form.getProductList().get(i);
          // 亏本：(结算单价/(1+物料税率))<=去税单价 去税单价 = 含税单价/（1+订单税率） 原币=含税单价
          // 去税单价
          BigDecimal unitPriceExcludingTax = NumberUtil.div(supplierProduct.getPrice(),
              NumberUtil.add(BigDecimal.ONE, NumberUtil.null2Zero(supplierProduct.getTaxRate())));
          if (NumberUtil.isLessOrEqual(
              NumberUtil.div(NumberUtil.null2Zero(supplierProduct.getSettlementPrice()),
                  NumberUtil.add(BigDecimal.ONE,
                      NumberUtil.null2Zero(supplierProduct.getProductRate()))),
              unitPriceExcludingTax)) {
            rowError.add((i + 1) + "行");
          }
          // 项目类别与是否免费校验
          if (ObjectUtil.equals(Constants.PROJECT_TYPE_JS, supplierProduct.getProjectType())
              && !ObjectUtil.equals(SimpleBooleanEnum.NO.getKey(),
              supplierProduct.getFreeState())) {
            throw new CheckException("项目类别为“寄售”时，是否免费只能是“否”");
          }
        }
        if (CollUtil.isNotEmpty(rowError)) {
          throw new CheckException(CollUtil.join(rowError, "、")
              + "，去税结算单价小于去税单价，无法提交。如需做亏本订单请勾选亏本订单");
        }
      }
    }


  /**
   * 获取实际预付金额
   */
  //实际预付金额
  public BigDecimal getActualPrepaidAmount(String purchaseOrderCode) {
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.getByVoucherTypeAndPurchaseOrderNoAndState(
            VoucherTypeEnum.ADVANCE_CHARGE.getKey(), purchaseOrderCode, Constants.STATE_OK);
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    financialVouchers = financialVouchers.stream().filter(
        financialVoucher -> !Objects.equals(financialVoucher.getPrepaidOffsetStatus(),
            Constants.STATE_OK)).collect(Collectors.toList());
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    BigDecimal relatedAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRelatedAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal refundAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRefundAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    return NumberUtil.sub(relatedAmount, refundAmount);
  }

  /**
   * 获取实际应付款金额
   */
  public BigDecimal getActualAmount(String purchaseOrderCode) {
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.getByVoucherTypeAndPurchaseOrderNoAndState(
            VoucherTypeEnum.ACCOUNTS_PAYABLE.getKey(), purchaseOrderCode, Constants.STATE_OK);
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    financialVouchers = financialVouchers.stream().filter(
        financialVoucher -> !Objects.equals(financialVoucher.getPrepaidOffsetStatus(),
            Constants.STATE_OK)).collect(Collectors.toList());
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    BigDecimal relatedAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRelatedAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal refundAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRefundAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    return NumberUtil.sub(relatedAmount, refundAmount);
  }


  /**
   * 生成采购订单
   */
  public SupplierOrderV2 create(PurchaseOrderAddV2Form form, User user) {
    SupplierOrderV2 supplierOrder = new SupplierOrderV2();
    // supplier
    Supplier supplier = supplierRepository.findById(form.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商不存在！"));
    // department
    Group department =
        groupRepository.findFirstByErpCodeAndState(form.getPurchaseDeptCode(), Constants.STATE_OK);
    Group group =
        groupRepository.findFirstByErpCodeAndState(form.getUserGroup(), Constants.STATE_OK);
    if (department == null) {
      throw new CheckException("采购部门不存在！");
    }
    if (group == null) {
      throw new CheckException("采购组织不存在！");
    }
    if (StrUtil.isBlank(form.getId())) {
      // 新增
      // 生成唯一订单号
      if (StrUtil.isNotBlank(form.getOrderCode())) {
        // 订单号不为空，使用用户输入的订单号
        supplierOrder.setCode(form.getOrderCode());
        // 判断系统中是否存在相同的订单号
        SupplierOrderV2 findOne =
            supplierOrderV2Repository.findFirstByCodeAndState(form.getOrderCode(),
                Constants.STATE_OK);
        if (findOne != null) {
          throw new CheckException("订单号已存在，请勿重复创建！");
        }
      } else {
        // 订单号为空，使用系统生成的订单号
        supplierOrder.setCode(OrderNumUtil.getInstance().getSeq());
      }
      supplierOrder.setOrderCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateMan(user.getId());
      supplierOrder.setUpdateMan(user.getId());
      supplierOrder.setUpdateTime(System.currentTimeMillis());
      // #check 校验采购部门
      this.checkPurchaseDeptCode(form, null);
    } else {
      // 修改
      supplierOrder = supplierOrderV2Repository.findById(form.getId())
          .orElseThrow(() -> new CheckException("订单不存在！"));
      //#check 查询是否有取消单
      this.checkCancelOrder(form.getId());
      //#check 是否为首次提交
      boolean isFirstSubmit = this.checkFirstSubmit(form.getSaveType(), supplierOrder.getOrderState());
      // 提交时校验且非暂存订单
      if (isFirstSubmit) {
        // #check 首次提交校验
        this.checkSupplierOrderFirstSubmit(form.getId(),
            form.getProductList().size(),
            supplier.getEnterpriseName(),
            form.getSupplierId(),
            supplierOrder.getSupplierName(),
            supplierOrder.getSupplierId());
      }
      //#check 校验修改后的订单金额不能小于已预付金额
      this.checkOrderAmount(supplierOrder.getCode(), form.getTotalAmountIncludingTax());
      //#check 校验采购部门
      this.checkPurchaseDeptCode(form, supplierOrder);
      supplierOrder.setUpdateMan(user.getId());
      supplierOrder.setUpdateTime(System.currentTimeMillis());
    }
    // #check 校验亏本单--动态根据模板判断
    if (!BooleanUtil.isTrue(form.getSap085Flag())) {
      this.checkLossOrder(form);
    }
    // #check scp填写校验--动态根据模板判断
//    this.checkSelfScp(form);
    supplierOrder.setOrderState(SupplierOrderState.UNAUDITED.getOrderState());
    if (form.getSaveType() == 1) {
      supplierOrder.setOrderState(SupplierOrderState.STAGING.getOrderState());
    }
    if (BooleanUtil.isTrue(form.getSap085Flag())) {
      supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
    }
    supplierOrder.setSupplierId(form.getSupplierId());
    supplierOrder.setSupplierName(supplier.getEnterpriseName());
    supplierOrder.setGroupCode(form.getUserGroup());
    supplierOrder.setGroupName(group.getName());
    // 编码判断是否厂家直发
    if (CollUtil.isNotEmpty(form.getProductList())) {
      // 所有仓库都为直销库时为厂家直发
      boolean isDirectSend = form.getProductList().stream()
          .allMatch(productDetailAdd -> WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(productDetailAdd.getWarehouse()));
      supplierOrder.setDirectShipment(isDirectSend);
    }
    // 价税合计总和
    supplierOrder.setPrice(form.getTotalAmountIncludingTax());
    supplierOrder.setReceiveMobile(form.getReceiveMobile());
    supplierOrder.setReceiveMan(form.getReceiveMan());
    User purchaseMan =
        userRepository.findFirstByCodeAndState(form.getPurchaseManCode(), Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("采购员不存在！"));
    // 工号后4位 + 名字
    String purchaseManName = purchaseMan.getCode().substring(purchaseMan.getCode().length() - 4)
        + purchaseMan.getRealName();
    supplierOrder.setPurchaseMan(purchaseManName);
    supplierOrder.setReceiveAddress(form.getReceiveAddress());
    supplierOrder.setOrderConfirmState(true);
    supplierOrder.setOrderCancelState(false);
    supplierOrder.setOrderReturnState(false);
    supplierOrder.setOrderShipWaitStockState(false);
    supplierOrder.makeAndSetStockProgress(BigDecimal.ZERO, form.getTotalNum());
    supplierOrder.setTotalNum(form.getTotalNum());
    supplierOrder.setState(Constants.STATE_OK);
    supplierOrder.setMark(form.getMark());
    supplierOrder.setRefuseState(Constants.STATE_NO);
    supplierOrder.setSupplierOpenInvoiceState(Constants.ORDER_INVOICE_STATE_NOT_DONE);
    supplierOrder.setOrderType(form.getOrderType());
    supplierOrder.setPurchaseDept(department.getName());
    supplierOrder.setPurchaseDeptCode(form.getPurchaseDeptCode());
    supplierOrder.setPurchaseCode(purchaseMan.getCode());
    supplierOrder.setInvoicingParty(form.getInvoicingParty());
    supplierOrder.setMoneyCode(form.getMoneyCode());
    supplierOrder.setOrderRate(form.getOrderRate());
    supplierOrder.setSupContacts(form.getSupContacts());
    supplierOrder.setSupMobile(form.getSupMobile());
    supplierOrder.setSupEmail(form.getSupEmail());
    supplierOrder.setSupFax(form.getSupFax());
    supplierOrder.setInvoiceType(form.getInvoiceType());
    supplierOrder.setFreight(form.getFreight());
    if (PurchaseOrderTypeEnum.GIFT.getKey().equals(form.getOrderType())) {
      supplierOrder.setFreeState(true);
    }else{
      supplierOrder.setFreeState(false);
    }
    // 如果是自采单，默认是自采
    if (PurchaseOrderTypeEnum.SELF_PURCHASE.getKey().equals(form.getOrderType())) {
      supplierOrder.setSelfState(true);
    } else {
      // 如果不是自采单，默认是非自采
      supplierOrder.setSelfState(false);
    }
    supplierOrder.setPurchaseUse(form.getPurchaseUse());
    if (form.getSaveType() == 2) {
      supplierOrder.setPaymentTermsStr(form.getPaymentTermsStr());
    }
    supplierOrder.setLoss(form.getLoss());
    supplierOrder.setCauseOfLoss(form.getCauseOfLoss());
    boolean scp = BooleanUtil.isTrue(form.getScp());
    supplierOrder.setScp(scp);
    if (!scp) {
      supplierOrder.setOrderConfirmState(false);
    }
    supplierOrder.setFinalPrice(BigDecimal.ZERO);
    return supplierOrder;
  }

  /**
   * 保存付款条件信息
   * @param paymentTerms
   * @param purchaseOrderId
   */
  public void savePurchaseOrderPaymentTerms(List<PurchaseOrderPaymentTermsAddParam> paymentTerms,
      String purchaseOrderId) {
    List<PurchaseOrderPaymentTermsV2> origins =
        purchaseOrderPaymentTermsV2Repository.findAllByPurchaseOrderIdAndState(purchaseOrderId,
            Constants.COMMONSTATE_OK);
    purchaseOrderPaymentTermsV2Repository.deleteAll(origins);
    // 重新保存
    final String conditionNumber = "条件";
    int index = 1;
    paymentTerms = CollUtil.emptyIfNull(paymentTerms);
    for (PurchaseOrderPaymentTermsAddParam paymentTerm : paymentTerms) {
      PurchaseOrderPaymentTermsV2Builder builder = PurchaseOrderPaymentTermsV2.builder();
      builder.accountPeriod(paymentTerm.getAccountPeriod())
          .condition(CollUtil.join(paymentTerm.getCondition(), ","))
          .advanceRatio(paymentTerm.getAdvanceRatio())
          .prepaidAmount(paymentTerm.getPrepaidAmount())
          .createTime(System.currentTimeMillis())
          .purchaseOrderId(purchaseOrderId)
          .itemNumber(conditionNumber + index++)
          .state(Constants.STATE_OK);
      PurchaseOrderPaymentTermsV2 purchaseOrderPaymentTerms = builder.build();
      purchaseOrderPaymentTermsV2Repository.save(purchaseOrderPaymentTerms);
    }
  }

  /**
   * 保存附件
   */
  public void saveAnnexParam(SupplierOrderV2 supplierOrder, PurchaseOrderAddV2Form form) {
    fileDao.deleteByRelationIdAndRelationType(supplierOrder.getId(),Constants_FileRelationType.ORDER_ANNEX);
    if (CollUtil.isEmpty(form.getFileList())) {
      return;
    }
    int max_annex_num = 20;
    if (form.getFileList().size() > max_annex_num) {
      throw new CheckException("超出订单最大附件数量");
    }
    List<AnnexParam> fileList = form.getFileList();
    for (AnnexParam annexParam : fileList) {
      File file = annexParam.buildFile(supplierOrder.getId());
      file.setRelationType(Constants_FileRelationType.ORDER_ANNEX);
      fileDao.save(file);
    }
  }

  /**
   * 释放订单明细数据 -- 同时记录原始数据
   */
  public SupplierOrderOriginData delSupplierOrderInfo(String supplierOrderId,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap,
      SupplierOrderOriginData orderOriginData) {
    if (StrUtil.isBlank(supplierOrderId)) {
      return orderOriginData;
    }
    // 删除 订单明细，物料数据
    // 按供应商订单 ID 和类型和状态查找
    List<SupplierOrderToFormV2> supplierOrderToForms =
        supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(supplierOrderId,
            SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    for (SupplierOrderToFormV2 supplierOrderToForm : supplierOrderToForms) {
      // 真删
      supplierOrderToForm.setState(Constants.STATE_DELETE);
      supplierOrderToFormV2Repository.delete(supplierOrderToForm);
      List<SupplierOrderDetailV2> byOrderToFormIdAndState =
          supplierOrderDetailV2Repository.findByOrderToFormIdAndState(supplierOrderToForm.getId(),
              Constants.STATE_OK);
      orderOriginData.setOriginSupplierOrderDetailList(byOrderToFormIdAndState);
      List<SupplierOrderProductV2> supplierOrderProductV2List = new ArrayList<>();
      for (SupplierOrderDetailV2 supplierOrderDetail : byOrderToFormIdAndState) {
        supplierOrderProductV2List.add(supplierOrderDetail.getSupplierOrderProduct());
        if (StrUtil.isNotEmpty(supplierOrderDetail.getPurchaseApplyForOrderId())) {
          PurchaseApplyForOrderV2 purchaseApplyForOrder = purchaseApplyForOrderMap.computeIfAbsent(
              supplierOrderDetail.getPurchaseApplyForOrderId(),
              k -> purchaseApplyForOrderV2Repository.findById(
                      supplierOrderDetail.getPurchaseApplyForOrderId())
                  .orElseThrow(() -> new CheckException("采购申请单不存在")));
          // 增加订货数量并且保存
          BigDecimal orderGoodsNumber =
              Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber())
                  .orElse(BigDecimal.ZERO);
          purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
          purchaseApplyForOrder.setOrderGoodsNumber(
              orderGoodsNumber.subtract(supplierOrderDetail.getNum()));
          purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
          if (purchaseApplyForOrder.getOrderGoodsNumber().compareTo(BigDecimal.ZERO) > 0) {
            purchaseApplyForOrder.setPushDownState(Constants.STATE_OK);
          } else {
            purchaseApplyForOrder.setPushDownState(Constants.STATE_NO);
          }
        }
        supplierOrderDetail.setState(Constants.STATE_DELETE);
        supplierOrderDetailV2Repository.delete(supplierOrderDetail);
      }
      orderOriginData.setOriginSupplierOrderProductList(supplierOrderProductV2List);
      List<String> detailIds = byOrderToFormIdAndState.stream().map(BaseSupplierOrderDetail::getId)
          .collect(Collectors.toList());
      List<SupplierOrderDetailV2> entrustDetails =
          supplierOrderDetailV2Repository.findAllByEntrustDetailIdInAndState(detailIds,
              Constants.STATE_OK);
      if (CollUtil.isNotEmpty(entrustDetails)) {
        supplierOrderDetailV2Repository.deleteAll(entrustDetails);
      }
    }
    return orderOriginData;
  }

  public List<String> getOriginApplyIds(String supplierOrderId) {
    if (StrUtil.isBlank(supplierOrderId)) {
      return Collections.emptyList();
    }
    List<SupplierOrder2DetailProjectionV2> projections = supplierOrderDetailV2Repository.getDetailsByOrderIds2(Collections.singletonList(supplierOrderId),
            SupplierOrderFormType.DETAILED.getType());
    List<SupplierOrderDetailV2> supplierOrderDetailV2List =
        projections.stream().map(SupplierOrder2DetailProjectionV2::getSupplierOrderDetail)
            .collect(Collectors.toList());
    return supplierOrderDetailV2List.stream()
        .map(BaseSupplierOrderDetail::getPurchaseApplyForOrderId)
        .filter(StrUtil::isNotBlank)
        .distinct().collect(Collectors.toList());
  }

  public SupplierOrderToFormV2 createLinkSupplierOrderForm(String supplierOrderId,
      PurchaseOrderAddV2Form form, User user) {
    SupplierOrderToFormV2 supplierOrderToForm = new SupplierOrderToFormV2();
    supplierOrderToForm.setSupplierOrderId(supplierOrderId);
    supplierOrderToForm.setType(SupplierOrderFormType.DETAILED.getType());
    supplierOrderToForm.setCreateTime(System.currentTimeMillis());
    supplierOrderToForm.setState(Constants.STATE_OK);
    if (user != null) {
      supplierOrderToForm.setCreateUser(user.getId());
      supplierOrderToForm.setCreateUserName(user.getRealName());
    }
    supplierOrderToForm.setNum(form.getTotalNum());
    return supplierOrderToForm;
  }

  public List<SupplierOrderProductV2> createLinkSupplierOrderProduct(List<ProductDetailAdd> productList) {
    List<SupplierOrderProductV2> res = new ArrayList<>();
    for (ProductDetailAdd productDetailAdd : productList) {
      SupplierOrderProductV2 supplierOrderProduct = new SupplierOrderProductV2();
//      String itemGroup =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getItemGroupCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getItemGroupCodeName()));
      supplierOrderProduct.setItemGroup(productDetailAdd.getItemGroupCodeName());
      supplierOrderProduct.setItemGroupCode(productDetailAdd.getItemGroupCode());
      supplierOrderProduct.setSpecification(productDetailAdd.getSpecification());
      supplierOrderProduct.setModel(productDetailAdd.getModel());
//      String assignmentCategory =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getAssignmentCategoryCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getAssignmentCategoryName()));
      supplierOrderProduct.setAssignmentCategory(productDetailAdd.getAssignmentCategoryName());
      supplierOrderProduct.setAssignmentCategoryCode(productDetailAdd.getAssignmentCategoryCode());
//      String productCategory =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getProfileCardCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getProfileCardName()));
      supplierOrderProduct.setProfileCard(productDetailAdd.getProfileCardName());
      supplierOrderProduct.setProfileCardCode(productDetailAdd.getProfileCardCode());
//      String costCenter =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getCostCenterCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getCostCenterName()));
      supplierOrderProduct.setCostCenter(productDetailAdd.getCostCenterName());
      supplierOrderProduct.setCostCenterCode(productDetailAdd.getCostCenterCode());
//      String ledgerSubject =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getLedgerSubjectCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getLedgerSubjectName()));
      supplierOrderProduct.setLedgerSubject(productDetailAdd.getLedgerSubjectName());
      supplierOrderProduct.setLedgerSubjectCode(productDetailAdd.getLedgerSubjectCode());
//      String order =
//          StrUtil.format("{}{}",
//              StrUtil.emptyIfNull(productDetailAdd.getOrderCode()),
//              StrUtil.emptyIfNull(productDetailAdd.getOrderName()));
      supplierOrderProduct.setOrder(productDetailAdd.getOrderName());
      supplierOrderProduct.setOrderCode(productDetailAdd.getOrderCode());
      supplierOrderProduct.setQualityCheck(productDetailAdd.getQualityCheck());
      supplierOrderProduct.setCode(productDetailAdd.getProductCode());
      supplierOrderProduct.setId(null);
      supplierOrderProduct.setBrand(productDetailAdd.getBrand());
//      supplierOrderProduct.setBrandCode();
      supplierOrderProduct.setName(productDetailAdd.getProductName());
      supplierOrderProduct.setUnit(productDetailAdd.getUnit());
      supplierOrderProduct.setUnitCode(productDetailAdd.getUnitCode());
      supplierOrderProduct.setUnitDigit(3);
      supplierOrderProduct.setSalesman(productDetailAdd.getSalesman());
      supplierOrderProduct.setFollowUpPersonName(productDetailAdd.getFollowUpPersonName());
      supplierOrderProduct.setBusinessCompanyName(productDetailAdd.getBusinessCompanyName());
      supplierOrderProduct.setMakeManName(productDetailAdd.getMakeManName());
      supplierOrderProduct.setSoldToParty(productDetailAdd.getSoldToParty());
      res.add(supplierOrderProduct);
    }
    return res;
  }

  public List<SupplierOrderDetailV2> createLinkSupplierOrderDetail(
      SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 supplierOrderToForm,
      List<SupplierOrderProductV2> linkSupplierOrderProduct,
      PurchaseOrderAddV2Form form,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap
  ) {
    List<SupplierOrderDetailV2> res = new ArrayList<>();
    List<ProductDetailAdd> productList = form.getProductList();
    for (int i = 0; i < form.getProductList().size(); i++) {
      SupplierOrderProductV2 supplierOrderProduct = linkSupplierOrderProduct.get(i);
      ProductDetailAdd productDetailAdd = productList.get(i);
      SupplierOrderDetailV2 detail = new SupplierOrderDetailV2();
      detail.setSupplierOrderProduct(supplierOrderProduct);
      detail.setOrderToFormType(supplierOrderToForm.getType());
      BigDecimal currShipPrice = NumberUtil.mul(productDetailAdd.getNum(), productDetailAdd.getPrice());
      detail.setTotalPrice(currShipPrice);
      detail.setOrderProductId(supplierOrderProduct.getId());
      detail.setOrderToFormId(supplierOrderToForm.getId());
      detail.setNum(BigDecimalUtil.setScaleBigDecimalHalfUp(productDetailAdd.getNum(),3));
      detail.setWaitQty(BigDecimalUtil.setScaleBigDecimalHalfUp(productDetailAdd.getNum(),3));
      detail.setShipQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setCancelQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setStockInputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setWaitStockInputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setStockOutputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      detail.setSettleQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"), 3));
      detail.setMark(productDetailAdd.getMark());
      detail.setSalesOrderNo(productDetailAdd.getSalesOrderNo());
      detail.setCreateTime(System.currentTimeMillis());
      detail.setUpdateTime(System.currentTimeMillis());
      detail.setState(Constants.STATE_OK);
      detail.setSortNum(i + 1);
      detail.setIndex(detail.getSortNum().toString());
      detail.setPrice(productDetailAdd.getPrice());
      detail.setDescription(productDetailAdd.getDescription());
      detail.setPurchaseApplyForOrderId(productDetailAdd.getPurchaseApplyForOrderId());
      detail.setWarehouse(productDetailAdd.getWarehouse());
      detail.setWarehouseName(productDetailAdd.getWarehouseName());
      detail.setTaxRate(productDetailAdd.getTaxRate());
      detail.setTotalAmountIncludingTax(productDetailAdd.getTotalAmountIncludingTax());
      detail.setFreeState(productDetailAdd.getFreeState());
      detail.setProjectType(productDetailAdd.getProjectType());
//      detail.setEntrustDetailId();
//      detail.setSapRowId();
      detail.setDeliverTime(productDetailAdd.getSupplierDeliverTime());
      detail.setPurchaseDeliverTime(productDetailAdd.getPurchaseDeliverTime());
//      detail.setInWareHouseId();
//      detail.setInWareHouseName();
      detail.setBatchNo(productDetailAdd.getBatchNo());
//      detail.setSapReversalRowNo();
//      detail.setOpenInvoiceState();
//      detail.setInvoicableNum();
      detail.setPurchaseOrderId(supplierOrder.getId());
//      detail.setInvoicedNum();
      detail.setProductRate(productDetailAdd.getProductRate());
      detail.setMarkupCoefficient(productDetailAdd.getMarkupCoefficient());
      detail.setTransferPrice(productDetailAdd.getTransferPrice());
      detail.setSurcharge(productDetailAdd.getSurcharge());
      detail.setSettlementPrice(productDetailAdd.getSettlementPrice());
      detail.setTotalSettlementPrice(productDetailAdd.getTotalSettlementPrice());
      detail.setTariff(productDetailAdd.getTariff());
      detail.setTariffAmount(productDetailAdd.getTariffAmount());
      detail.setPaymentAmount(productDetailAdd.getPaymentAmount());
      detail.setFreight(productDetailAdd.getFreight());
      detail.setProjectNo(productDetailAdd.getProjectNo());
      detail.setProjectName(productDetailAdd.getProjectName());
      detail.setFreightSupplierId(productDetailAdd.getFreightSupplierId());
      detail.setTariffSupplierId(productDetailAdd.getTariffSupplierId());
      detail.setIncidentalSupplierId(productDetailAdd.getIncidentalSupplierId());
      if (StrUtil.isNotBlank(productDetailAdd.getFreightSupplierId())) {
        Supplier supplier = supplierRepository.findById(productDetailAdd.getFreightSupplierId())
            .orElseThrow(() -> new CheckException("运费供应商不存在！"));
        detail.setFreightSupplierName(supplier.getEnterpriseName());
      }
      if (StrUtil.isNotBlank(productDetailAdd.getTariffSupplierId())) {
        Supplier supplier = supplierRepository.findById(productDetailAdd.getTariffSupplierId())
            .orElseThrow(() -> new CheckException("关税供应商不存在！"));
        detail.setTariffSupplierName(supplier.getEnterpriseName());
      }
      if (StrUtil.isNotBlank(productDetailAdd.getIncidentalSupplierId())) {
        Supplier supplier = supplierRepository.findById(productDetailAdd.getIncidentalSupplierId())
            .orElseThrow(() -> new CheckException("杂费供应商不存在！"));
        detail.setIncidentalSupplierName(supplier.getEnterpriseName());
      }
      detail.setIncidentalAmount(productDetailAdd.getIncidentalAmount());
//      detail.setReturnPrice();
//      detail.setReturnAmount();
//      detail.setOriginalPrice();
//      detail.setOriginalTotalPrice();
      detail.setReturnFlag(false);
      // 如果是采购申请单
      if (StrUtil.isNotBlank(productDetailAdd.getPurchaseApplyForOrderId())) {
        PurchaseApplyForOrderV2 purchaseApplyForOrder =
            purchaseApplyForOrderMap.computeIfAbsent(productDetailAdd.getPurchaseApplyForOrderId(),
                k -> purchaseApplyForOrderV2Repository.findById(productDetailAdd.getPurchaseApplyForOrderId())
                    .orElseThrow(() -> new CheckException("采购申请单不存在")));
        // 增加订货数量并且保存
        BigDecimal orderGoodsNumber =
            Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber())
                .orElse(BigDecimal.ZERO);
        BigDecimal applyNum =
            Optional.ofNullable(purchaseApplyForOrder.getApplyForNumber())
                .orElse(BigDecimal.ZERO);
        purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber.add(productDetailAdd.getNum()));
        // 判断订货数量是否超过了申请数量
        if (NumberUtil.isGreater(purchaseApplyForOrder.getOrderGoodsNumber(), applyNum)) {
          throw new CheckException("采购申请单" + purchaseApplyForOrder.getApplyForOrderNo() + "的订货数量超过了申请数量");
        }
        purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
        if (purchaseApplyForOrder.getOrderGoodsNumber().compareTo(BigDecimal.ZERO) > 0) {
          purchaseApplyForOrder.setPushDownState(Constants.STATE_OK);
        } else {
          purchaseApplyForOrder.setPushDownState(Constants.STATE_NO);
        }
      }
      res.add(detail);
    }
    return res;
  }

  public void saveEntrustDetail(PurchaseOrderAddV2Form form, List<SupplierOrderDetailV2> supplierOrderDetails) {
    List<ProductDetailAdd> productList = form.getProductList();
    for (int i = 0; i < productList.size(); i++) {
      ProductDetailAdd productDetailAdd = productList.get(i);
      SupplierOrderDetailV2 supplierOrderDetail = supplierOrderDetails.get(i);
      List<EntrustProduct> entrustProductList = productDetailAdd.getEntrustProductList();
      if (CollUtil.isNotEmpty(entrustProductList)) {
        for (EntrustProduct entrustProduct : entrustProductList) {
          this.saveEntrustDetail(entrustProduct, supplierOrderDetail);
        }
      }
    }
  }

  /**
   * 保存委外详情
   */
  private void saveEntrustDetail(EntrustProduct entrustProduct,
      SupplierOrderDetailV2 supplierOrderDetail) {
    SupplierOrderProductV2 supplierOrderProductEntrust = new SupplierOrderProductV2();
    supplierOrderProductEntrust.setCode(entrustProduct.getProductCode());
    supplierOrderProductEntrust.setBrand(entrustProduct.getBrand());
    supplierOrderProductEntrust.setName(entrustProduct.getProductName());
    supplierOrderProductEntrust.setModel(entrustProduct.getModel());
    supplierOrderProductEntrust.setSpecification(entrustProduct.getSpecification());
    supplierOrderProductEntrust.setManuCode(entrustProduct.getSpecification() + entrustProduct.getModel());
    supplierOrderProductEntrust.setUnit(entrustProduct.getUnit());
    supplierOrderProductEntrust.setComponentDemandDate(entrustProduct.getComponentDemandDate());
    supplierOrderProductEntrust.setLineItemCategory(entrustProduct.getLineItemCategory());
    supplierOrderProductEntrust.setMrpType(entrustProduct.getMrpType());
    supplierOrderProductEntrust.setComponentUnit(entrustProduct.getComponentUnit());
    supplierOrderProductEntrust.setComponentUnitName(entrustProduct.getComponentUnitName());
    supplierOrderProductEntrust.setUnitDigit(3);

    supplierOrderProductV2Repository.save(supplierOrderProductEntrust);
    SupplierOrderDetailV2 supplierOrderDetailChild = new SupplierOrderDetailV2();
    supplierOrderDetailChild.setOrderProductId(supplierOrderProductEntrust.getId());
    supplierOrderDetailChild.setState(Constants.STATE_OK);
    supplierOrderDetailChild.setSupplierOrderProduct(supplierOrderProductEntrust);
    supplierOrderDetailChild.setDescription(entrustProduct.getDescription());
    supplierOrderDetailChild.setNum(entrustProduct.getNum());
    supplierOrderDetailChild.setEntrustDetailId(supplierOrderDetail.getId());
    supplierOrderDetailV2Repository.save(supplierOrderDetailChild);
  }

  /**
   * 生成飞搭表单参数
   */
  public StartProcessParam createFeidaProcessParam(
      SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap,
      User user
  ) {
    SupplierOrderProcessForm newProcessForm =
        createBaseFeidaProcessParam(supplierOrder, supplierOrderDetails, purchaseApplyForOrderMap);
    // 查询上次审核成功的飞搭申请日志
    SupplierOrderSync lastSuccessFeida =
        supplierOrderSyncRepository.findFirstByStateAndTargetAndSupplierOrderIdAndReviewStatusOrderByCreateTimeDesc(
            Constants.STATE_OK, "飞搭", supplierOrder.getId(),
            SupplierOrderSyncStatus.SUCCESS.getCode());
    if (lastSuccessFeida == null) {
      // 新增
      newProcessForm.setType("新增");
    } else {
      // 修改
      newProcessForm.setType("修改");
      JSONObject jsonObject = JSON.parseObject(lastSuccessFeida.getReq());
      Object docJsonMap = jsonObject.get("docJsonMap");
      SupplierOrderProcessForm originProcessForm = JSON.parseObject(docJsonMap.toString(),
          SupplierOrderProcessForm.class);
      PurchaseOrderFeidaProcessCompareHelper compareHelper = new PurchaseOrderFeidaProcessCompareHelper();
      String updateFields = compareHelper.compareProcessForms(originProcessForm, newProcessForm);
      newProcessForm.setModifiedFields(updateFields);
    }
    String desc = StrUtil.format("{}提交的采购订单{}-{}", user.getRealName(),
        supplierOrder.getCode(),
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getPurchaseOrderFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(user.getCode().toLowerCase()).description(desc).variableMap(null)
            .docJsonMap(JSON.parseObject(JSON.toJSONString(newProcessForm))).build();
    return processParam;
  }

  private SupplierOrderProcessForm createBaseFeidaProcessParam(
      SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap
  ) {
    SupplierOrderProcessForm supplierOrderProcessForm = new SupplierOrderProcessForm();
    supplierOrderProcessForm.setOrderCode(supplierOrder.getCode());
    String orderTypeName =
        bootDictService.getDictValueByKey(BootDictEnum.PROCUREMENT_ORDER.getKey(), supplierOrder.getOrderType());
    supplierOrderProcessForm.setOrderTypeName(orderTypeName);
    supplierOrderProcessForm.setOrganization(supplierOrder.getGroupName());
    supplierOrderProcessForm.setPurchaseDepartment(supplierOrder.getPurchaseDept());
    supplierOrderProcessForm.setPurchaseContracts("");
    supplierOrderProcessForm.setOrderAttachments("");
    supplierOrderProcessForm.setCreateTime(supplierOrder.getCreateTime());
    supplierOrderProcessForm.setSupplierName(supplierOrder.getSupplierName());
    supplierOrderProcessForm.setInvoiceName(supplierOrder.getInvoicingParty());
    supplierOrderProcessForm.setOrderExchangeRate(supplierOrder.getOrderRate());
    String invoiceTypeName =
        bootDictService.getDictValueByKey(BootDictEnum.INVOICE_TYPE_FOR_PURCHASE_ORDER.getKey(), supplierOrder.getInvoiceType());
    supplierOrderProcessForm.setInvoiceTypeName(invoiceTypeName);
    supplierOrderProcessForm.setRemarks(supplierOrder.getMark());
    supplierOrderProcessForm.setPaymentTerms(supplierOrder.getPaymentTermsStr());
    supplierOrderProcessForm.setCurrencyCode(supplierOrder.getMoneyCode());
    supplierOrderProcessForm.setAutoApproval(Boolean.FALSE);
    if (supplierOrder.getSelfState()) {
      supplierOrderProcessForm.setAutoApproval(Boolean.TRUE);
    }
    // 或者为自采订单类型
    if (PurchaseOrderTypeEnum.SELF_PURCHASE.getKey().equals(supplierOrder.getOrderType())) {
      supplierOrderProcessForm.setAutoApproval(Boolean.TRUE);
    }
    supplierOrderProcessForm.setPurchaseManCode(supplierOrder.getPurchaseCode());
    // supplierOrder.getPurchaseMan() 如果存在数字则去掉前面的数字
    String purchaseManRaw = supplierOrder.getPurchaseMan(); // 假设这行能获取到类似 "123张三" 或 "李四" 的字符串
    String purchaseMan = purchaseManRaw; // 默认值设为原始值
    if (purchaseManRaw != null && !purchaseManRaw.isEmpty()) {
      // 使用正则表达式匹配并替换字符串开头的连续数字
      // ^\\d+ 匹配字符串开头(^)的一个或多个数字(\\d+)
      purchaseMan = purchaseManRaw.replaceAll("^\\d+", "");

      // 可选：如果去掉数字后可能会留下前导空格（例如 "123 张三"），可以去掉
      purchaseMan = purchaseMan.trim();
    }
    supplierOrderProcessForm.setPurchaseMan(purchaseMan);
    supplierOrderProcessForm.setDirect(false);
    supplierOrderProcessForm.setLossReason(supplierOrder.getCauseOfLoss());
    if (StrUtil.isNotBlank(supplierOrder.getPurchaseUse())) {
      String purchaseUse = bootDictService.getDictValueByKey(BootDictEnum.PURCHASE_USE.getKey(),
          supplierOrder.getPurchaseUse());
      supplierOrderProcessForm.setPurchasePurpose(purchaseUse);
    }
    supplierOrderProcessForm.setSupplierContact(supplierOrder.getSupContacts());
    supplierOrderProcessForm.setContact(supplierOrder.getSupMobile());
    supplierOrderProcessForm.setReceiptAddress(supplierOrder.getReceiveAddress());
    supplierOrderProcessForm.setOrderAmount(supplierOrder.getPrice());
    supplierOrderProcessForm.setProjectCode(supplierOrder.getProjectNo());
    supplierOrderProcessForm.setSalesOrderNumber(supplierOrder.getSaleOrderNo());
    supplierOrderProcessForm.setCustomerOrderNumber(supplierOrder.getCustomerOrderCode());
    List<SupplierOrderDetailProcessForm> supplierOrderDetailProcessForms = new ArrayList<>();
    for (SupplierOrderDetailV2 supplierOrderDetail : supplierOrderDetails) {
      SupplierOrderProductV2 supplierOrderProduct = supplierOrderDetail.getSupplierOrderProduct();
      SupplierOrderDetailProcessForm detailProcessForm = new SupplierOrderDetailProcessForm();
      detailProcessForm.setLineId(supplierOrderDetail.getSortNum().toString());
      detailProcessForm.setProductCode(supplierOrderProduct.getCode());
      detailProcessForm.setProductName(supplierOrderProduct.getName());
      detailProcessForm.setBrand(supplierOrderProduct.getBrand());
      detailProcessForm.setSpecifications(supplierOrderProduct.getSpecification());
      detailProcessForm.setModel(supplierOrderProduct.getModel());
      detailProcessForm.setUnit(supplierOrderProduct.getUnit());
      detailProcessForm.setNum(supplierOrderDetail.getNum());
      detailProcessForm.setWarehouse(supplierOrderDetail.getWarehouseName());
      detailProcessForm.setAgreedDeliveryDateLong(supplierOrderDetail.getDeliverTime());
      detailProcessForm.setActualDeliveryDateLong(supplierOrderDetail.getPurchaseDeliverTime());
      detailProcessForm.setMaterialGroup(supplierOrderProduct.getItemGroup());
      // 结算金额
      if (supplierOrderDetail.getSettlementPrice() != null) {
        detailProcessForm.setSettlementPrice(supplierOrderDetail.getSettlementPrice());
      }
      if (supplierOrderDetail.getTotalSettlementPrice() != null) {
        detailProcessForm.setTotalSettlementPrice(supplierOrderDetail.getTotalSettlementPrice());
      }
      // 杂费供应商
      if (StrUtil.isNotBlank(supplierOrderDetail.getIncidentalSupplierName())) {
        detailProcessForm.setNameOfSupplier(supplierOrderDetail.getIncidentalSupplierName());
      }
      if (StrUtil.isNotBlank(supplierOrderDetail.getFreightSupplierName())) {
        detailProcessForm.setShippingProvider(supplierOrderDetail.getFreightSupplierName());
      }
      if (StrUtil.isNotBlank(supplierOrderDetail.getTariffSupplierName())) {
        detailProcessForm.setCustomsSupplier(supplierOrderDetail.getTariffSupplierName());
      }
      if (supplierOrderDetail.getIncidentalAmount() != null) {
        detailProcessForm.setMiscellaneous(supplierOrderDetail.getIncidentalAmount());
      }
      if (supplierOrderDetail.getFreight() != null) {
        detailProcessForm.setFreight(supplierOrderDetail.getFreight());
      }
      if (supplierOrderDetail.getTariffAmount() != null) {
        detailProcessForm.setAmountOfTariffs(supplierOrderDetail.getTariffAmount());
      }
      if (supplierOrderDetail.getSurcharge() != null) {
        detailProcessForm.setAdditionalCharges(supplierOrderDetail.getSurcharge());
      }
      detailProcessForm.setIncludedTaxPrice(supplierOrderDetail.getPrice());
      if (supplierOrderDetail.getPrice() != null) {
        BigDecimal rateDecimal =
            Optional.ofNullable(supplierOrderDetail.getTaxRate()).orElse(BigDecimal.ZERO);
        // 计算去税单价
        BigDecimal onePlusRate = BigDecimal.ONE.add(rateDecimal);
        BigDecimal taxFreeCbPrice = supplierOrderDetail.getPrice().divide(onePlusRate, 2,
            RoundingMode.HALF_UP);
        detailProcessForm.setUnitPrice(taxFreeCbPrice);
        // 税额 = 不含税金额 * 税率
        // 不含税金额 = 单价原币 * 数量
        BigDecimal taxAmount =
            taxFreeCbPrice.multiply(supplierOrderDetail.getNum()).multiply(rateDecimal);
        detailProcessForm.setTaxes(taxAmount);
      }
      detailProcessForm.setTaxRate(supplierOrderDetail.getTaxRate());
      // 单价原币 * 数量
      detailProcessForm.setTotalPrice(supplierOrderDetail.getPrice().multiply(supplierOrderDetail.getNum()));
      // 总价原币
      if (supplierOrderDetail.getTotalAmountIncludingTax() != null) {
        detailProcessForm.setTotalPriceTax(supplierOrderDetail.getTotalAmountIncludingTax());
      }
      // 总价付汇金额 = 总价原价 * 汇率 * 1.001
      if (supplierOrder.getOrderRate() != null) {
        BigDecimal totalAmountIncludingTax = supplierOrderDetail.getTotalAmountIncludingTax();
        if (totalAmountIncludingTax != null) {
          BigDecimal totalPricePayAmount = totalAmountIncludingTax.multiply(
              new BigDecimal(supplierOrder.getOrderRate()).multiply(new BigDecimal("1.001")));
          detailProcessForm.setAmountOfPayment(totalPricePayAmount);
        }
      }

      // 不含税总计 = 总价付汇金额 + 关税金额 + 运费金额 + 杂费金额
      BigDecimal totalPricePayAmount = NumberUtil.add(
          detailProcessForm.getAmountOfPayment(),
          detailProcessForm.getAmountOfTariffs(),
          detailProcessForm.getFreight(),
          detailProcessForm.getMiscellaneous()
      );
      detailProcessForm.setTotalTaxPrice(totalPricePayAmount);
      // 不含税单价 = 不含税总价 / 数量
      if (supplierOrderDetail.getNum() != null) {
        BigDecimal totalTaxPrice = supplierOrderDetail.getTotalPrice().divide(supplierOrderDetail.getNum(), 2,
            RoundingMode.HALF_UP);
        detailProcessForm.setDeTaxUnitPrice(totalTaxPrice);
      }
      // 结算单价 = MPM结算价 + 附加费 * （1+加价系数）保留2位
      // 结算总价 = 结算单价 * 数量
      if (supplierOrderDetail.getTransferPrice() != null) {
        BigDecimal transferPrice = supplierOrderDetail.getTransferPrice();
        if (supplierOrderDetail.getSurcharge() != null) {
          BigDecimal surcharge = supplierOrderDetail.getSurcharge();
          if (supplierOrderDetail.getMarkupCoefficient() != null) {
            BigDecimal markupCoefficient = supplierOrderDetail.getMarkupCoefficient();
            transferPrice = transferPrice.add(surcharge.multiply(markupCoefficient));
          }
        }
        detailProcessForm.setIncludedUnitPrice(transferPrice);
        detailProcessForm.setTotalTaxIncluded(transferPrice.multiply(supplierOrderDetail.getNum()));
      }
      detailProcessForm.setLineRemarks(supplierOrderDetail.getMark());
      String freeState = supplierOrderDetail.getFreeState();
      Boolean isFree = false;
      // 0 1转换成boolean
      if (StrUtil.isNotBlank(freeState)) {
        isFree = freeState.equals("1");
      }
      detailProcessForm.setFree(isFree);
      detailProcessForm.setQualityCheck(supplierOrderProduct.getQualityCheck());
      String projectType = Constants.PROJECT_TYPE_MAP.get(supplierOrderDetail.getProjectType());
      detailProcessForm.setProjectCategory(projectType);
      String projectTypeValue =
          bootDictService.getDictValueByKey(BootDictEnum.PROJECT_TYPE.getKey(),
          supplierOrderDetail.getProjectType());
      if (StrUtil.isNotBlank(projectTypeValue)) {
        detailProcessForm.setProjectCategory(projectTypeValue);
      }
      detailProcessForm.setBatchNumber(supplierOrderDetail.getBatchNo());
      detailProcessForm.setSubjectDistribution(supplierOrderProduct.getAssignmentCategory());
      detailProcessForm.setCostCenter(supplierOrderProduct.getCostCenter());
      detailProcessForm.setAssetCard(supplierOrderProduct.getProfileCard());
      detailProcessForm.setOrder(supplierOrderProduct.getOrder());
      detailProcessForm.setGeneralLedger(supplierOrderProduct.getLedgerSubject());
      PurchaseApplyForOrderV2 purchaseApplyForOrder =
          purchaseApplyForOrderMap.get(supplierOrderDetail.getPurchaseApplyForOrderId());
      if (purchaseApplyForOrder != null) {
        detailProcessForm.setApplyForOrderNo(purchaseApplyForOrder.getApplyForOrderNo());
      }
      detailProcessForm.setProjectName(supplierOrderDetail.getProjectName());
      detailProcessForm.setProjectNo(supplierOrderDetail.getProjectNo());
      if (supplierOrderDetail.getProductRate() != null) {
        detailProcessForm.setProductTaxRate(supplierOrderDetail.getProductRate());
      }
      detailProcessForm.setSalesOrders(supplierOrderDetail.getSalesOrderNo());
      supplierOrderDetailProcessForms.add(detailProcessForm);
    }
    supplierOrderProcessForm.setDetailProcessForms(supplierOrderDetailProcessForms);
    // 处理附件
    List<SupplierOrderFileProcessForm> files = new ArrayList<>();
    // 供应商订单附件
    List<File> annexes =
        fileRepository.findAllByRelationIdAndRelationTypeAndState(supplierOrder.getId(),
            Constants_FileRelationType.ORDER_ANNEX, Constants.STATE_OK).orElse(new ArrayList<>());
    for (File annex : annexes) {
      SupplierOrderFileProcessForm form = new SupplierOrderFileProcessForm();
      form.setFileName(annex.getName());
      form.setFileUrl(StrUtil.addSuffixIfNot(srmConfig.getUploadUrl(), "/") + StrUtil.removePrefix(annex.getUrl(), "/"));
      form.setRelationType("订单附件");
      files.add(form);
    }
    // 供应商合同附件
    List<File> contractFiles =
        fileRepository.findAllByRelationIdAndRelationTypeAndState(supplierOrder.getId(),
            Constants_FileRelationType.ORDER_CONTRACT, Constants.STATE_OK).orElse(new ArrayList<>());
    for (File contractFile : contractFiles) {
      SupplierOrderFileProcessForm form = new SupplierOrderFileProcessForm();
      form.setFileName(contractFile.getName());
      form.setFileUrl(StrUtil.addSuffixIfNot(srmConfig.getUploadUrl(), "/") + StrUtil.removePrefix(contractFile.getUrl(), "/"));
      form.setRelationType("采购合同附件");
      files.add(form);
    }
    supplierOrderProcessForm.setFileProcessForms(files);
    return supplierOrderProcessForm;
  }


  public void refreshRedundantSupplierOrder(
      PurchaseOrderAddV2Form form,
      SupplierOrderV2 supplierOrder,
      List<SupplierOrderDetailV2> linkSupplierOrderDetails,
      Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderMap
  ) {
    List<String> applyForOrderIds =
        linkSupplierOrderDetails.stream().map(
                BaseSupplierOrderDetail::getPurchaseApplyForOrderId)
            .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    // 根据applyForOrderIds查询purchaseApplyForOrderMap中的数据
    List<PurchaseApplyForOrderV2> purchaseApplyForOrders = purchaseApplyForOrderMap
        .values().stream().filter(item -> applyForOrderIds.contains(item.getId()))
        .collect(Collectors.toList());
    Set<String> salesOrderNo =
        CollUtil.emptyIfNull(form.getProductList()).stream()
            .map(supplierProduct -> StrUtil.subPre(supplierProduct.getSalesOrderNo(),10))
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    // 获取项目编号
    Set<String> projectNoJoin =
        CollUtil.emptyIfNull(form.getProductList()).stream()
            .map(ProductDetailAdd::getProjectNo)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> soldToParty =
        CollUtil.emptyIfNull(form.getProductList()).stream()
            .map(ProductDetailAdd::getSoldToParty)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> salesman =
        CollUtil.emptyIfNull(form.getProductList()).stream()
            .map(ProductDetailAdd::getSalesman)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> projectName =
        CollUtil.emptyIfNull(form.getProductList()).stream()
            .map(ProductDetailAdd::getProjectName)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    // 设置customerOrderCode
    Set<String> customerOrderCode =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getCustomerOrderNumber)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> salesOrderNo2 =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getSaleOrderNo)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> projectNo2 =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getProjectNo)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> soldToParty2 =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getSoldToParty)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> salesman2 =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getSalesman)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    Set<String> projectName2 =
        CollUtil.emptyIfNull(purchaseApplyForOrders).stream()
            .map(PurchaseApplyForOrderV2::getProjectName)
            .filter(StrUtil::isNotBlank)  // 过滤掉null值
            .collect(Collectors.toSet());
    salesOrderNo.addAll(salesOrderNo2);
    projectNoJoin.addAll(projectNo2);
    soldToParty.addAll(soldToParty2);
    salesman.addAll(salesman2);
    projectName.addAll(projectName2);

    // 只有在集合非空时才设置值
    if (!salesOrderNo.isEmpty()) {
      supplierOrder.setSaleOrderNo(StrUtil.join(",", salesOrderNo));
    }
    if (!projectNoJoin.isEmpty()) {
      supplierOrder.setProjectNo(StrUtil.join(",", projectNoJoin));
    }
    if (!soldToParty.isEmpty()) {
      supplierOrder.setSoldToParty(StrUtil.join(",", soldToParty));
    }
    if (!salesman.isEmpty()) {
      supplierOrder.setSalesman(StrUtil.join(",", salesman));
    }
    if (!projectName.isEmpty()) {
      supplierOrder.setProjectName(StrUtil.join(",", projectName));
    }
    if (!customerOrderCode.isEmpty()) {
      supplierOrder.setCustomerOrderCode(StrUtil.join(",", customerOrderCode));
    }
  }
}
