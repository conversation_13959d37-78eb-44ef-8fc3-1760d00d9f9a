package com.xhgj.srm.v2.factory;/**
 * @since 2025/4/18 16:05
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BootDictEnum;
import com.xhgj.srm.common.enums.OrderGoodsStateV2Enum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.purchaseApplyForOrder.PurchaseApplyRecordStatus;
import com.xhgj.srm.jpa.entity.BasePurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.PurchaseApplyItem;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord.PurchaseApplyRecordJson;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.enums.PurchaseApplyRecordFields;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyItemRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.mpm.UnitResult;
import com.xhgj.srm.request.dto.oms.PurchaseApplyDetailResult;
import com.xhgj.srm.request.dto.oms.PurchaseApplyDetailResult.DetailData;
import com.xhgj.srm.request.service.third.oms.OmsRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjMPMRequest;
import com.xhgj.srm.v2.dto.AsmDisOrderApplyLinkV2DTO;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2AddForm;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2AddForm.OutsourcingApplyDetail;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2UpdateForm;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2UpdateForm.PurchaseApplyForOrderUpdateDetail;
import com.xhgj.srm.v2.form.feida.ApplyForOrderProcessForm;
import com.xhgj.srm.v2.form.feida.ApplyForOrderProcessForm.ApplyForOrderDetailProcessForm;
import com.xhgj.srm.v2.provider.AsmDisOrderProvider;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.dict.core.service.BootDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *<AUTHOR>
 *@date 2025/4/18 16:05:37
 *@description
 */
@Component
@Slf4j
public class PurchaseApplyForOrderV2Factory {

  @Resource
  PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  XhgjMPMRequest xhgjMPMRequest;
  @Resource
  OmsRequest omsRequest;
  @Resource
  HZeroProcessConfig hZeroProcessConfig;
  @Resource
  UserRepository userRepository;
  @Resource
  GroupRepository groupRepository;
  @Resource
  SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  SupplierOrderRepository supplierOrderRepository;
  @Resource
  AsmDisOrderProvider asmDisOrderProvider;
  @Resource
  PurchaseApplyItemRepository purchaseApplyItemRepository;
  @Resource
  BootDictService bootDictService;
  @Resource
  SupplierRepository supplierRepository;
  /**
   * #check 校验申请单号是否一致
   */
  public void checkApplyForOrderNos(List<PurchaseApplyForOrderV2AddForm> forms) {
    if (CollUtil.isEmpty(forms)) {
      return;
    }
    Set<String> applyForOrderNos = forms.stream().map(PurchaseApplyForOrderV2AddForm::getApplyForOrderNo)
        .collect(Collectors.toSet());
    if (applyForOrderNos.size() != 1) {
      throw new CheckException("参数非法，申请单号应保持一致！");
    }
  }

  /**
   * #check 对于修改的采购申请校验，校验以下内容。
   * 校验 采购申请为锁定状态 - 不允许修改，并提示 已有审核中的数据，请在修改记录中查看
   * 校验 采购申请单有关联的订单 - 不允许修改，并提示 {采购申请单号}的{物料编码}物料有关联订单，无法修改
   * 校验 采购申请有关联的组装拆卸单 - 不允许修改，并提示 {采购申请单号}的{物料编码}物料有关联组装拆卸单，无法修改
   */
  public void checkUpdateApplyForOrder(List<PurchaseApplyForOrderV2> forms) {
    if (CollUtil.isEmpty(forms)) {
      return;
    }
    List<String> ids =
        forms.stream().map(BasePurchaseApplyForOrder::getId).collect(Collectors.toList());
    // 校验采购申请为锁定状态
    for (PurchaseApplyForOrderV2 form : forms) {
      if (OrderGoodsStateV2Enum.LOCK.getKey().equals(form.getOrderGoodsState())) {
        throw new CheckException("已有审核中的数据，请在修改记录中查看");
      }
    }
    // 校验采购申请单有关联的订单
    Map<String, PurchaseApplyForOrderV2> purchaseApplyForOrderV2Map = forms.stream()
        .collect(Collectors.toMap(PurchaseApplyForOrderV2::getId, form -> form));
    List<SupplierOrderDetail> orderDetails  =
        supplierOrderDetailRepository.findAllByPurchaseApplyForOrderIdInAndState(ids, Constants.STATE_OK);
    for (SupplierOrderDetail orderDetail : orderDetails) {
      SupplierOrder supplierOrder =
          supplierOrderRepository.findById(orderDetail.getPurchaseOrderId()).orElse(null);
      if (supplierOrder != null) {
        PurchaseApplyForOrderV2 purchaseApplyForOrderV2 = purchaseApplyForOrderV2Map.get(orderDetail.getPurchaseApplyForOrderId());
        throw new CheckException(StrUtil.format("{}的{}物料有关联订单，无法修改",
            purchaseApplyForOrderV2.getApplyForOrderNo(), purchaseApplyForOrderV2.getProductCode()));
      }
    }
    // 校验采购申请有关联的组装拆卸单
    List<AsmDisOrderApplyLinkV2DTO> purchaseApplyLink = asmDisOrderProvider.getPurchaseApplyLink(ids);
    for (AsmDisOrderApplyLinkV2DTO asmDisOrderApplyLinkV2DTO : purchaseApplyLink) {
      PurchaseApplyForOrderV2 purchaseApplyForOrderV2 = purchaseApplyForOrderV2Map.get(asmDisOrderApplyLinkV2DTO.getPurchaseApplyForOrderId());
      if (purchaseApplyForOrderV2 != null) {
        throw new CheckException(StrUtil.format("{}的{}物料有关联组装拆卸单，无法修改",
            purchaseApplyForOrderV2.getApplyForOrderNo(), purchaseApplyForOrderV2.getProductCode()));
      }
    }
  }

  /**
   * #check 修改数量不能小于已订货数量
   *        修改数量不能大于原始申请数量
   */
  public void checkUpdateApplyForOrderQuantity(List<PurchaseApplyForOrderV2> origins, List<PurchaseApplyForOrderV2AddForm> forms) {
    for (PurchaseApplyForOrderV2 origin : origins) {
      PurchaseApplyForOrderV2AddForm form =
          forms.stream().filter(item -> item.getIndex().equals(origin.getIndex())).findFirst()
              .orElse(null);
      if (origin == null) {
        continue;
      }
      BigDecimal orderGoodsNumber =
          Optional.ofNullable(origin.getOrderGoodsNumber()).orElse(BigDecimal.ZERO);
      BigDecimal applyForNumber =
          Optional.ofNullable(origin.getApplyForNumber()).orElse(BigDecimal.ZERO);
      // 修改数量不能小于已订货数量
      if (NumberUtil.isLess(form.getQuantity(), orderGoodsNumber)) {
        throw new CheckException(StrUtil.format("申请数量不能小于已订货数量，申请单号：{}，物料编码：{}", origin.getApplyForOrderNo(), origin.getProductCode()));
      }
      // 修改数量不能大于原始申请数量
      if (NumberUtil.isGreater(form.getQuantity(), applyForNumber)) {
        throw new CheckException(StrUtil.format("申请数量不能大于原始申请数量，申请单号：{}，物料编码：{}", origin.getApplyForOrderNo(), origin.getProductCode()));
      }
    }
  }

  /**
   * #check 修改数量不能小于已订货数量
   *        修改数量不能大于原始申请数量
   */
  public void checkUpdateApplyForOrderQuantity(List<PurchaseApplyForOrderV2> origins, PurchaseApplyForOrderV2UpdateForm form) {
    List<PurchaseApplyForOrderUpdateDetail> details = form.getDetails();
    for (PurchaseApplyForOrderV2 origin : origins) {
      PurchaseApplyForOrderUpdateDetail detail =
          details.stream().filter(item -> item.getPurchaseApplyForOrderId().equals(origin.getId())).findFirst()
              .orElse(null);
      if (origin == null) {
        continue;
      }
      BigDecimal orderGoodsNumber =
          Optional.ofNullable(origin.getOrderGoodsNumber()).orElse(BigDecimal.ZERO);
      BigDecimal applyForNumber =
          Optional.ofNullable(origin.getApplyForNumber()).orElse(BigDecimal.ZERO);
      // 修改数量不能小于已订货数量
      if (NumberUtil.isLess(detail.getApplyForNumber(), orderGoodsNumber)) {
        throw new CheckException(StrUtil.format("申请数量不能小于已订货数量，申请单号：{}，物料编码：{}", origin.getApplyForOrderNo(), origin.getProductCode()));
      }
      // 修改数量不能大于原始申请数量
      if (NumberUtil.isGreater(detail.getApplyForNumber(), applyForNumber)) {
        throw new CheckException(StrUtil.format("申请数量不能大于原始申请数量，申请单号：{}，物料编码：{}", origin.getApplyForOrderNo(), origin.getProductCode()));
      }
    }
  }

  /**
   * 创建飞搭流程参数
   * @param forms
   * @param records
   * @param sap
   * @return
   */
  public StartProcessParam createFeidaProcessParam(List<PurchaseApplyForOrderV2> forms,
      List<PurchaseApplyRecord> records, Boolean sap) {

    String applyForUser = forms.get(0).getUpdateUser();
    String organization = forms.get(0).getPurchasingOrganization();
    User user = userRepository.findById(applyForUser).orElse(null);
    if (user == null) {
      throw new CheckException("申请人不存在或已失效");
    }
    Group group = groupRepository.findFirstByErpCodeAndState(organization, Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("采购组织不存在或已失效");
    }
    ApplyForOrderProcessForm form = new ApplyForOrderProcessForm();
    // 修改人工号
    form.setJobNumber(user.getCode());
    // 修改人姓名
    form.setChangeName(user.getRealName());
    // 修改时间
    form.setChangeTime(System.currentTimeMillis());
    if (Boolean.TRUE.equals(sap)) {
      form.setSource("SAP");
    } else {
      form.setSource("SRM");
    }
    form.setOrganization(group.getName());
    // 采购组织
    // 明细
    List<ApplyForOrderDetailProcessForm> processDetails = this.createFeidaProcessDetail(forms, records);
    form.setDetailProcessForms(processDetails);
    // 时间戳 2025-04-18 16:05:37
    String desc = StrUtil.format("{}提交的申请单修改{}-{}", user.getRealName(),
        forms.get(0).getApplyForOrderNo(),
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    StartProcessParam processParam =
        StartProcessParam.builder().flowKey(hZeroProcessConfig.getPurchaseApplyOrderFlowKey())
            .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
            .starter(user.getCode().toLowerCase()).description(desc).variableMap(null)
            .docJsonMap(JSON.parseObject(JSON.toJSONString(form))).build();
    return processParam;
  }

  /**
   * 创建飞达流程明细
   * @param forms
   * @param records
   * @return
   */
  private List<ApplyForOrderDetailProcessForm> createFeidaProcessDetail(
      List<PurchaseApplyForOrderV2> forms, List<PurchaseApplyRecord> records) {
    List<ApplyForOrderDetailProcessForm> res = new ArrayList<>();
    Map<String,PurchaseApplyRecord> recordMap = records.stream()
        .collect(Collectors.toMap(PurchaseApplyRecord::getPurchaseApplyId, record -> record, (k1,k2) -> k1));
    for (PurchaseApplyForOrderV2 origin : forms) {
      PurchaseApplyForOrderV2 purchaseApplyForOrder = MapStructFactory.INSTANCE.toPurchaseApplyForOrderV2(origin);

      ApplyForOrderDetailProcessForm detailProcessForm = new ApplyForOrderDetailProcessForm();
      PurchaseApplyRecord purchaseApplyRecord = recordMap.get(purchaseApplyForOrder.getId());
      if (purchaseApplyRecord != null) {
        List<PurchaseApplyRecordJson> showChangeList = purchaseApplyRecord.getShowChangeList();
        String modifyFields = showChangeList.stream().map(PurchaseApplyRecordJson::getDesc)
            .collect(Collectors.joining(","));
        detailProcessForm.setModifyField(modifyFields);
        PurchaseApplyRecordFields.updateFiles(purchaseApplyForOrder, purchaseApplyRecord.getChangeList());
      }
      detailProcessForm.setApplyForOrderNo(purchaseApplyForOrder.getApplyForOrderNo());
      String applyTypeName =
          bootDictService.getDictValueByKey(BootDictEnum.PROCUREMENT_APPLICATION.getKey(),
              purchaseApplyForOrder.getApplyForType());
      detailProcessForm.setApplyForType(applyTypeName);
      detailProcessForm.setCreateTime(purchaseApplyForOrder.getCreateTime());
      userRepository.findById(purchaseApplyForOrder.getCreateUser())
          .ifPresent(user -> detailProcessForm.setCreator(user.getRealName()));
      detailProcessForm.setPurchaseMan(purchaseApplyForOrder.getPurchaseMan());
      detailProcessForm.setPurchaseManCode(purchaseApplyForOrder.getPurchaseManNumber());
      Group purchaseDepartment =
          groupRepository.findFirstByErpCodeAndState(purchaseApplyForOrder.getPurchaseDepartment(),
              Constants.STATE_OK);
      if (purchaseDepartment == null) {
        throw new CheckException("采购部门不存在或已失效");
      }
      detailProcessForm.setPurchaseDepartment(purchaseDepartment.getName());
      detailProcessForm.setApplicationFormRemarks(
          purchaseApplyForOrder.getApplicationFormRemarks());
      detailProcessForm.setAssignmentCategoryCode(
          purchaseApplyForOrder.getAssignmentCategoryCode());
      detailProcessForm.setAssignmentCategoryName(
          purchaseApplyForOrder.getAssignmentCategoryName());
      detailProcessForm.setOrderCode(purchaseApplyForOrder.getOrderCode());
      detailProcessForm.setOrderName(purchaseApplyForOrder.getOrderName());
      detailProcessForm.setCostCenterCode(purchaseApplyForOrder.getCostCenterCode());
      detailProcessForm.setCostCenterName(purchaseApplyForOrder.getCostCenterName());
      detailProcessForm.setLedgerSubjectCode(purchaseApplyForOrder.getLedgerSubjectCode());
      detailProcessForm.setLedgerSubjectName(purchaseApplyForOrder.getLedgerSubjectName());
      detailProcessForm.setDeliverTime(purchaseApplyForOrder.getDeliverTime());
      detailProcessForm.setSerialNumber(purchaseApplyForOrder.getSerialNumber().toString());
      detailProcessForm.setProfileCard(purchaseApplyForOrder.getProfileCard());
      detailProcessForm.setProductCode(purchaseApplyForOrder.getProductCode());
      detailProcessForm.setProductName(purchaseApplyForOrder.getProductName());
      detailProcessForm.setBrand(purchaseApplyForOrder.getBrand());
      detailProcessForm.setSpecification(purchaseApplyForOrder.getSpecification());
      detailProcessForm.setModel(purchaseApplyForOrder.getModel());
      detailProcessForm.setUnitCombo(purchaseApplyForOrder.getUnitName());
      detailProcessForm.setSoldToParty(purchaseApplyForOrder.getSoldToParty());
      detailProcessForm.setApplyForNumber(
          purchaseApplyForOrder.getApplyForNumber().stripTrailingZeros().toPlainString());
      res.add(detailProcessForm);
    }
    return res;
  }

  /**
   * 采购申请单创建 OR 创建 SAP
   */
  public void createForSap(List<PurchaseApplyForOrderV2AddForm> forms,
      List<PurchaseApplyForOrderV2> addOnes, List<PurchaseApplyForOrderV2> updateOnes,
      List<PurchaseApplyRecord> records) {
    if (CollUtil.isEmpty(forms)) {
      throw new CheckException("采购申请单不能为空");
    }
    AtomicInteger index = new AtomicInteger(0);
    PurchaseApplyDetailResult purchaseApplyDetail = null;
    try {
      purchaseApplyDetail = omsRequest.getPurchaseApplyDetail(forms.get(0).getApplyForOrderNo());
    } catch (RuntimeException e) {
      log.info("获取采购申请单详情失败，申请号：" + forms.get(0).getApplyForOrderNo(), e);
    }
    PurchaseApplyDetailResult finalPurchaseApplyDetail = purchaseApplyDetail;
    for (PurchaseApplyForOrderV2AddForm form : forms) {
      String userCode = form.getPurchasingEmployeeNumber();
      User applyUser =
          userRepository.findFirstByCodeAndState(userCode, Constants.STATE_OK).orElseThrow(() -> new CheckException("申请人不存在或已失效"));
      form.setIndex(index.incrementAndGet());
      UnitResult unitResult = xhgjMPMRequest.findUnitByCodeOrName(form.getUnit(), null);
      PurchaseApplyForOrderV2 findOne =
          purchaseApplyForOrderV2Repository.findFirstByApplyForOrderNoAndRowIdAndState(
              form.getApplyForOrderNo(), form.getRowId(), Constants.STATE_OK);
      if (findOne == null) {
        // 新增
        PurchaseApplyForOrderV2 purchaseApplyForOrder = new PurchaseApplyForOrderV2();
        purchaseApplyForOrder.setIndex(index.get());
        purchaseApplyForOrder.setSpecification(form.getSpecification());
        purchaseApplyForOrder.setProfileCard(
            Stream.of(form.getAssetCard(), form.getAssetCardName()).filter(Objects::nonNull)
                .collect(Collectors.joining()));
        purchaseApplyForOrder.setProfileCardCode(form.getAssetCard());
        purchaseApplyForOrder.setProfileCardName(form.getAssetCardName());
        purchaseApplyForOrder.setAssignmentCategory(
            Stream.of(form.getSubjectAllocationCategoryCode(),
                    form.getSubjectAllocationCategoryName()).filter(Objects::nonNull)
                .collect(Collectors.joining()));
        purchaseApplyForOrder.setAssignmentCategoryCode(form.getSubjectAllocationCategoryCode());
        purchaseApplyForOrder.setAssignmentCategoryName(form.getSubjectAllocationCategoryName());
        purchaseApplyForOrder.setProjectCategory(
            Stream.of(form.getProjectCategoryCode(), form.getProjectCategoryName())
                .filter(Objects::nonNull).collect(Collectors.joining()));
        purchaseApplyForOrder.setProjectCategoryCode(form.getProjectCategoryCode());
        purchaseApplyForOrder.setProjectCategoryName(form.getProjectCategoryName());
        purchaseApplyForOrder.setLedgerSubject(
            Stream.of(form.getGeneralLedgerAccountCode(), form.getGeneralLedgerAccountName())
                .filter(Objects::nonNull).collect(Collectors.joining()));
        purchaseApplyForOrder.setLedgerSubjectCode(form.getGeneralLedgerAccountCode());
        purchaseApplyForOrder.setLedgerSubjectName(form.getGeneralLedgerAccountName());
        purchaseApplyForOrder.setCostCenter(
            Stream.of(form.getCostCenterCode(), form.getCostCenterName()).filter(Objects::nonNull)
                .collect(Collectors.joining()));
        purchaseApplyForOrder.setCostCenterCode(form.getCostCenterCode());
        purchaseApplyForOrder.setCostCenterName(form.getCostCenterName());
        purchaseApplyForOrder.setOrder(
            Stream.of(form.getOrderNo(), form.getOrderDescription()).filter(Objects::nonNull)
                .collect(Collectors.joining()));
        purchaseApplyForOrder.setOrderCode(form.getOrderNo());
        purchaseApplyForOrder.setOrderName(form.getOrderDescription());
        purchaseApplyForOrder.setItemGroup(
            Stream.of(form.getProductGroupCode(), form.getProductGroupName())
                .filter(Objects::nonNull).collect(Collectors.joining()));
        purchaseApplyForOrder.setItemGroupCode(form.getProductGroupCode());
        purchaseApplyForOrder.setItemGroupName(form.getProductGroupName());
        purchaseApplyForOrder.setDeliverTime(form.getDeliveryDate());
        purchaseApplyForOrder.setFixedVendor(form.getFixedSupplier());
        if (StrUtil.isNotBlank(form.getFixedSupplier())) {
          Supplier fixedSupplier =
              supplierRepository.getFirstByMdmCodeAndState(form.getFixedSupplier(),
                  Constants.STATE_OK);
          if (fixedSupplier != null) {
            purchaseApplyForOrder.setFixedVendorName(fixedSupplier.getEnterpriseName());
          }
        }
        purchaseApplyForOrder.setProcurementRecord(form.getPurchaseInfoRecord());
        purchaseApplyForOrder.setOrderGoodsNumber(BigDecimal.ZERO);
        purchaseApplyForOrder.setApplyForOrderNo(form.getApplyForOrderNo());
        purchaseApplyForOrder.setApplyForType(form.getApplyForType());
        purchaseApplyForOrder.setCreateTime(System.currentTimeMillis());
        purchaseApplyForOrder.setCreateUser(applyUser.getId());
        purchaseApplyForOrder.setUpdateTime(String.valueOf(purchaseApplyForOrder.getCreateTime()));
        purchaseApplyForOrder.setUpdateUser(applyUser.getId());
        purchaseApplyForOrder.setFactoryCode(form.getPurchasingOrganization());
        purchaseApplyForOrder.setCompanyCode(form.getSalesOrganization());
        purchaseApplyForOrder.setPurchaseDepartment(form.getPurchaseDepartment());
        purchaseApplyForOrder.setWarehouse(form.getWarehouse());
        purchaseApplyForOrder.setDeliveryAddress(form.getDeliveryAddress());
        purchaseApplyForOrder.setSoldToParty(form.getSoldToParty());
        purchaseApplyForOrder.setRowId(form.getRowId());
        purchaseApplyForOrder.setProductCode(form.getProductCode());
        purchaseApplyForOrder.setProductName(form.getProductName());
        purchaseApplyForOrder.setBrand(form.getBrand());
        purchaseApplyForOrder.setModel(form.getModel());
        purchaseApplyForOrder.setUnit(form.getUnit());
        purchaseApplyForOrder.setApplyForNumber(form.getQuantity());
        purchaseApplyForOrder.setPlanDemandDate(form.getPlanDemandDate());
        purchaseApplyForOrder.setPurchaseMan(form.getPurchaserName());
        if (StrUtil.isNotBlank(form.getPurchasingEmployeeNumber())) {
          purchaseApplyForOrder.setPurchaseManNumber(form.getPurchasingEmployeeNumber().toLowerCase());
        }
        purchaseApplyForOrder.setSaleOrderNo(form.getSaleOrderNo());
        purchaseApplyForOrder.setSaleOrderProductRowId(form.getSaleOrderProductRowId());
        if ("X".equals(form.getCancelStatus())) {
          purchaseApplyForOrder.setCancellationState(SimpleBooleanEnum.YES.getKey());
        } else {
          purchaseApplyForOrder.setCancellationState(SimpleBooleanEnum.NO.getKey());
        }
        purchaseApplyForOrder.updateOrderGoodsState();
        purchaseApplyForOrder.setState(Constants.STATE_OK);
        // todo 可能有问题
        purchaseApplyForOrder.setSerialNumber(index.get());
        purchaseApplyForOrder.setMpmReferenceSettlementPrice(form.getMpmReferenceSettlementPrice());
        purchaseApplyForOrder.setSalesDemandQuantity(form.getSalesDemandQuantity());
        purchaseApplyForOrder.setSalesUnitPrice(form.getSalesUnitPrice());
        purchaseApplyForOrder.setMaterialLineRemarks(form.getMaterialLineRemarks());
        purchaseApplyForOrder.setMaterialDescription(form.getMaterialDescription());
        purchaseApplyForOrder.setContactInformation(form.getContactInformation());
        purchaseApplyForOrder.setConsignee(form.getConsignee());
        purchaseApplyForOrder.setDeliveryType(form.getDeliveryType());
        purchaseApplyForOrder.setApplicationFormRemarks(form.getApplicationFormRemarks());
        purchaseApplyForOrder.setSalesman(form.getSalesman());
        purchaseApplyForOrder.setSalesOrganization(form.getSalesOrganization());
        purchaseApplyForOrder.setPurchasingOrganization(form.getPurchasingOrganization());
        purchaseApplyForOrder.setOrderTime(form.getCreateTime());
        purchaseApplyForOrder.setUnitName(unitResult != null ? unitResult.getName() : form.getUnit());
//        if (finalPurchaseApplyDetail == null) {
//          throw new CheckException("获取采购申请单详情失败");
//        }
//        DetailData data = finalPurchaseApplyDetail.getData();
//        if (data == null) {
//          throw new CheckException("采购申请单详情数据为空");
//        }
        if (finalPurchaseApplyDetail != null && finalPurchaseApplyDetail.getData() != null) {
          DetailData data = finalPurchaseApplyDetail.getData();
          purchaseApplyForOrder.setCustomerOrderNumber(data.getCustomerOrderNumber());
          purchaseApplyForOrder.setFollowUpPersonName(data.getFollowUpPersonName());
          purchaseApplyForOrder.setProjectName(data.getProjectName());
          purchaseApplyForOrder.setProjectNo(data.getProjectNumber());
          purchaseApplyForOrder.setApplicant(data.getApplyMan());
          purchaseApplyForOrder.setIsWorryOrder(form.getIsWorryOrder());
          purchaseApplyForOrder.setDirectShipment(data.getDirectShipment());
          purchaseApplyForOrder.setBusinessCompanyName(data.getBusinessCompanyName());
          purchaseApplyForOrder.setMakeManName(data.getMakeManName());
        }
        addOnes.add(purchaseApplyForOrder);
      } else {
        String organization = forms.get(0).getPurchasingOrganization();
        Group group = groupRepository.findFirstByErpCodeAndState(organization, Constants.STATE_OK);
        if (group == null) {
          throw new CheckException("采购组织不存在或已失效");
        }
        JSONArray jsonArray = new JSONArray();
        // 采购员
        String purchasingEmployeeNumberLower = StrUtil.isNotBlank(form.getPurchasingEmployeeNumber()) ? form.getPurchasingEmployeeNumber().toLowerCase() : null;
        List<PurchaseApplyRecordJson> purchaseApplyRecordJsons =
            PurchaseApplyRecordFields.judgePurchaser(
                findOne.getPurchaseManNumber(), purchasingEmployeeNumberLower,
                findOne.getPurchaseMan(), form.getPurchaserName());
        jsonArray.addAll(purchaseApplyRecordJsons);
        // 采购部门
        Group oldGroup =
            groupRepository.findFirstByErpCodeAndState(findOne.getPurchaseDepartment(),
                Constants.STATE_OK);
        Group newGroup =
            groupRepository.findFirstByErpCodeAndState(form.getPurchaseDepartment(),
                Constants.STATE_OK);
        if (oldGroup != null && newGroup != null) {
          List<PurchaseApplyRecordJson> purchaseDepartment =
              PurchaseApplyRecordFields.judgePurchasingDepartment(
                  oldGroup.getErpCode(), newGroup.getErpCode(),
                  oldGroup.getName(), newGroup.getName());
          jsonArray.addAll(purchaseDepartment);
        }
        // 申请数量
        List<PurchaseApplyRecordJson> applyForNumber =
            PurchaseApplyRecordFields.judgeApplyQuantity(
                findOne.getApplyForNumber(), form.getQuantity());
        jsonArray.addAll(applyForNumber);
        // 申请单备注
        List<PurchaseApplyRecordJson> applicationFormRemarks =
            PurchaseApplyRecordFields.judgeApplyOrderRemark(
                findOne.getApplicationFormRemarks(), form.getApplicationFormRemarks());
        jsonArray.addAll(applicationFormRemarks);
        // 科目分配类别
        List<PurchaseApplyRecordJson> assignmentCategory =
            PurchaseApplyRecordFields.judgeSubjectAllocationType(
                findOne.getAssignmentCategoryCode(), form.getSubjectAllocationCategoryCode(),
                findOne.getAssignmentCategoryName(), form.getSubjectAllocationCategoryName());
        jsonArray.addAll(assignmentCategory);
        // 订单
        List<PurchaseApplyRecordJson> order =
            PurchaseApplyRecordFields.judgeOrder(
                findOne.getOrderCode(), form.getOrderNo(),
                findOne.getOrderName(), form.getOrderDescription());
        jsonArray.addAll(order);
        // 成本中心
        List<PurchaseApplyRecordJson> costCenter =
            PurchaseApplyRecordFields.judgeCostCenter(
                findOne.getCostCenterCode(), form.getCostCenterCode(),
                findOne.getCostCenterName(), form.getCostCenterName());
        jsonArray.addAll(costCenter);
        // 总账单科目
        List<PurchaseApplyRecordJson> ledgerSubject =
            PurchaseApplyRecordFields.judgeGeneralLedgerAccount(
                findOne.getLedgerSubjectCode(), form.getGeneralLedgerAccountCode(),
                findOne.getLedgerSubjectName(), form.getGeneralLedgerAccountName());
        jsonArray.addAll(ledgerSubject);
        PurchaseApplyRecord purchaseApplyRecord = new PurchaseApplyRecord();
        purchaseApplyRecord.setPurchaseApplyId(findOne.getId());
        purchaseApplyRecord.setReviewId(null);
        purchaseApplyRecord.setReviewTime(null);
        purchaseApplyRecord.setStatus(PurchaseApplyRecordStatus.IN_REVIEW.getCode());
        purchaseApplyRecord.setSource("SAP");
        purchaseApplyRecord.setChanges(JSON.toJSONString(jsonArray));
        purchaseApplyRecord.setChangeUser(applyUser.getRealName());
        purchaseApplyRecord.setChangeUserId(applyUser.getId());
        purchaseApplyRecord.setCreateTime(System.currentTimeMillis());
        purchaseApplyRecord.setState(Constants.STATE_OK);
        if ("X".equals(form.getCancelStatus())) {
          findOne.setCancellationState(SimpleBooleanEnum.YES.getKey());
        } else {
          findOne.setCancellationState(SimpleBooleanEnum.NO.getKey());
        }
        findOne.updateOrderGoodsState();
        findOne.setUpdateTime(String.valueOf(System.currentTimeMillis()));
        findOne.setUpdateUser(applyUser.getId());
        updateOnes.add(findOne);
        findOne.setIndex(index.get());
        if (CollUtil.isNotEmpty(jsonArray)) {
          records.add(purchaseApplyRecord);
        }
      }
    }
  }

  /**
   * 采购申请单修改 SRM
   * @param form
   * @param updateOnes
   * @param records
   */
  public void updateForSrm(PurchaseApplyForOrderV2UpdateForm form,
      List<PurchaseApplyForOrderV2> updateOnes,
      List<PurchaseApplyRecord> records) {
    if (CollUtil.isEmpty(form.getDetails())) {
      throw new CheckException("采购申请单不能为空");
    }
    List<PurchaseApplyForOrderUpdateDetail> details = form.getDetails();
    for (PurchaseApplyForOrderUpdateDetail detail : details) {
      PurchaseApplyForOrderV2 findOne =
          purchaseApplyForOrderV2Repository.findById(detail.getPurchaseApplyForOrderId())
              .orElseThrow(() -> CheckException.noFindException(PurchaseApplyForOrderV2.class,
                  detail.getPurchaseApplyForOrderId()));
      User user = userRepository.findById(form.getUserId())
          .orElseThrow(() -> CheckException.noFindException(User.class, form.getUserId()));
      Group group = groupRepository.findFirstByErpCodeAndState(form.getUserGroup(), Constants.STATE_OK);
      if (group == null) {
        throw new CheckException("采购组织不存在或已失效");
      }
      JSONArray jsonArray = new JSONArray();
      // 采购员
      List<PurchaseApplyRecordJson> purchaseApplyRecordJsons =
          PurchaseApplyRecordFields.judgePurchaser(
              findOne.getPurchaseManNumber(), form.getPurchaseManCode(),
              findOne.getPurchaseMan(), form.getPurchaseMan());
      jsonArray.addAll(purchaseApplyRecordJsons);
      // 采购部门
      Group oldGroup =
          groupRepository.findFirstByErpCodeAndState(findOne.getPurchaseDepartment(),
              Constants.STATE_OK);
      Group newGroup =
          groupRepository.findFirstByErpCodeAndState(form.getPurchaseDepartment(),
              Constants.STATE_OK);
      if (oldGroup != null && newGroup != null) {
        List<PurchaseApplyRecordJson> purchaseDepartment =
            PurchaseApplyRecordFields.judgePurchasingDepartment(
                oldGroup.getErpCode(), newGroup.getErpCode(),
                oldGroup.getName(), newGroup.getName());
        jsonArray.addAll(purchaseDepartment);
      }
      // 申请数量
      List<PurchaseApplyRecordJson> applyForNumber =
          PurchaseApplyRecordFields.judgeApplyQuantity(
              findOne.getApplyForNumber(), detail.getApplyForNumber());
      jsonArray.addAll(applyForNumber);
      // 申请单备注
      List<PurchaseApplyRecordJson> applicationFormRemarks =
          PurchaseApplyRecordFields.judgeApplyOrderRemark(
              findOne.getApplicationFormRemarks(), form.getRemarks());
      jsonArray.addAll(applicationFormRemarks);
      PurchaseApplyRecord purchaseApplyRecord = new PurchaseApplyRecord();
      purchaseApplyRecord.setPurchaseApplyId(findOne.getId());
      purchaseApplyRecord.setReviewId(null);
      purchaseApplyRecord.setReviewTime(null);
      purchaseApplyRecord.setStatus(PurchaseApplyRecordStatus.IN_REVIEW.getCode());
      purchaseApplyRecord.setSource("SRM");
      purchaseApplyRecord.setChanges(JSON.toJSONString(jsonArray));
      purchaseApplyRecord.setChangeUser(user.getRealName());
      purchaseApplyRecord.setChangeUserId(user.getId());
      purchaseApplyRecord.setCreateTime(System.currentTimeMillis());
      purchaseApplyRecord.setState(Constants.STATE_OK);
      if (CollUtil.isNotEmpty(jsonArray)) {
        records.add(purchaseApplyRecord);
        // 修改
        findOne.setUpdateTime(String.valueOf(System.currentTimeMillis()));
        findOne.setUpdateUser(user.getId());
        updateOnes.add(findOne);
      }
    }
  }

  public void savePurchaseApplyItems(List<PurchaseApplyForOrderV2> applyList, List<PurchaseApplyForOrderV2AddForm> forms) {
    // 删除组件清单
    List<String> ids = applyList.stream().map(PurchaseApplyForOrderV2::getId).collect(Collectors.toList());
    clearPurchaseApplyItems(ids);
    // 新增组件清单
    addPurchaseApplyItems(applyList, forms);
  }

  /**
   * 新增组件清单
   */
  private void addPurchaseApplyItems(List<PurchaseApplyForOrderV2> applyList, List<PurchaseApplyForOrderV2AddForm> forms) {
    List<PurchaseApplyItem> purchaseApplyItem = new ArrayList<>();
    for (PurchaseApplyForOrderV2 apply : applyList) {
      PurchaseApplyForOrderV2AddForm origin =
          forms.stream().filter(item -> item.getIndex().equals(apply.getIndex())).findFirst()
              .orElse(null);
      if (origin == null) {
        continue;
      }
      List<OutsourcingApplyDetail> outsourcingApplyDetailList = CollUtil.emptyIfNull(origin.getOutsourcingApplyDetailList());
      List<PurchaseApplyItem> ones = outsourcingApplyDetailList.stream().map(item -> {
        PurchaseApplyItem one = MapStructFactory.INSTANCE.toPurchaseApplyItem(item);
        UnitResult unitResult = xhgjMPMRequest.findUnitByCodeOrName(one.getComponentUnit(), null);
        one.setComponentUnitName(unitResult != null ? unitResult.getName() : null);
        one.setPurchaseApplyId(apply.getId());
        one.setCreateTime(System.currentTimeMillis());
        one.setUpdateTime(System.currentTimeMillis());
        one.setState(Constants.STATE_OK);
        return one;
      }).collect(Collectors.toList());
      purchaseApplyItem.addAll(ones);
    }
    if (CollUtil.isNotEmpty(purchaseApplyItem)) {
      purchaseApplyItemRepository.saveAll(purchaseApplyItem);
    }
  }

  /**
   * 删除组件清单
   */
  private void clearPurchaseApplyItems(List<String> ids) {
    if (CollUtil.isEmpty(ids)) {
      return;
    }
    List<PurchaseApplyItem> items =
        purchaseApplyItemRepository.findAllByPurchaseApplyIdInAndState(ids, Constants.STATE_OK);
    if (CollUtil.isEmpty(items)) {
      return;
    }
    items.forEach(item ->{
      item.setUpdateTime(System.currentTimeMillis());
      item.setState(Constants.STATE_DELETE);
    });
    purchaseApplyItemRepository.saveAll(items);
  }
}
