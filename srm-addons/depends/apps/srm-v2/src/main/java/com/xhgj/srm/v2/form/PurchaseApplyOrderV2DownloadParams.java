package com.xhgj.srm.v2.form;

import com.xhgj.srm.jpa.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * PurchaseApplyOrderV2DownloadParams
 */
@Data
public class PurchaseApplyOrderV2DownloadParams {

  @ApiModelProperty("申请单号")
  private String applyNo;

  @ApiModelProperty("采购员id")
  private String purchaserId;

  @ApiModelProperty("订货状态：1.可订货，2.订货完成，4.锁定（多个以英文逗号分隔）")
  private String orderGoodsState;

  @ApiModelProperty("取消状态（0，1 已取消）")
  private String cancellationState;

  private User user;

  @ApiModelProperty("采购申请单主键id")
  private String id;

  @ApiModelProperty("申请单号集合")
  private List<String> applyNoList;
}
