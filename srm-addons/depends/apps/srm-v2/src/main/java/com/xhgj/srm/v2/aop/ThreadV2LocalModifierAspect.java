package com.xhgj.srm.v2.aop;/**
 * @since 2025/4/17 19:18
 */

import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 *<AUTHOR>
 *@date 2025/4/17 19:18:11
 *@description
 */
@Aspect
@Component
@Slf4j
public class ThreadV2LocalModifierAspect {

  /**
   * 是否为此版本添加的version
   */
  private static final ThreadLocal<Boolean> ASPECT_ADD = new ThreadLocal<>();


  @Before("execution(* com.xhgj.srm.v2.service..*.*(..))")
  public void modifyThreadLocalBeforeExecution() {
    if (ShardingContext.getVersion() == null) {
      log.info("ThreadV2LocalModifierAspect modifyThreadLocalBeforeExecution");
      ASPECT_ADD.set(true);
      ShardingContext.setVersion(VersionEnum.V2);
    }
  }

  @After("execution(* com.xhgj.srm.v2.service..*.*(..))")
  public void cleanupThreadLocal() {
    if (Boolean.TRUE.equals(ASPECT_ADD.get())) {
      log.info("ThreadV2LocalModifierAspect cleanupThreadLocal");
      ShardingContext.clear();
      ASPECT_ADD.remove();
    }
  }
}
