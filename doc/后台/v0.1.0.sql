ALTER TABLE t_purchase_apply_for_order_v2 ADD c_specification varchar(100) NULL COMMENT '规格';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_profile_card varchar(100) NULL COMMENT '资料卡片:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_profile_card_code varchar(20) NULL COMMENT '资料卡片编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_profile_card_name varchar(60) NULL;
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_assignment_category varchar(50) NULL COMMENT '科目分配类别:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_assignment_category_code varchar(10) NULL COMMENT '科目分配类别编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_assignment_category_name varchar(30) NULL COMMENT '科目分配类别名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_project_category varchar(50) NULL COMMENT '项目类别:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_project_category_code varchar(10) NULL COMMENT '项目类别编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_project_category_name varchar(30) NULL COMMENT '项目类别名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_ledger_subject varchar(50) NULL COMMENT '总账科目:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_ledger_subject_code varchar(10) NULL COMMENT '总账科目编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_ledger_subject_name varchar(20) NULL COMMENT '总账科目名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_cost_center varchar(50) NULL COMMENT '成本中心:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_cost_center_code varchar(30) NULL COMMENT '成本中心编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_cost_center_name varchar(20) NULL COMMENT '成本中心名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_order varchar(100) NULL COMMENT '订单:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_order_code varchar(20) NULL COMMENT '订单编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_order_name varchar(50) NULL COMMENT '订单名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_item_group varchar(50) NULL COMMENT '物料组:编码+名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_item_group_code varchar(10) NULL COMMENT '物料组编码';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_item_group_name varchar(30) NULL COMMENT '物料组名称';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_deliver_time bigint(20) NULL COMMENT '交货日期';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_fixed_vendor varchar(20) NULL COMMENT '固定的供应商';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_procurement_record varchar(20) NULL COMMENT '采购信息记录：标准/寄售';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_purchase_man_number varchar(50) NULL COMMENT '采购员工号';
ALTER TABLE t_purchase_apply_for_order_v2 ADD c_fixed_vendor_name varchar(50) NULL COMMENT '固定的供应商名称';


ALTER TABLE `t_purchase_apply_for_order_v2`
  ADD COLUMN `c_purchase_man_mix` varchar(100) AS (CONCAT(RIGHT(c_purchase_man_number, 4), c_purchase_man)) STORED COMMENT '计算列拼接工号 + 采购员' NULL AFTER `c_fixed_vendor_name`;



## 采购申请修改记录
CREATE TABLE `t_purchase_apply_record`
(
  `id`                  varchar(32) NOT NULL COMMENT '唯一id',
  `c_purchase_apply_id` varchar(32)  DEFAULT NULL COMMENT '关联采购申请id',
  `c_review_id`         varchar(32)  DEFAULT NULL COMMENT '审核唯一id',
  `c_review_time`       bigint(32) DEFAULT NULL COMMENT '审核时间',
  `c_review_reason`     varchar(200) DEFAULT NULL COMMENT '审核原因',
  `c_status`            tinyint(3) DEFAULT NULL COMMENT '审核状态 1 审核中、2 通过 、-1驳回',
  `c_source`            varchar(10)  DEFAULT NULL COMMENT '来源 SRM，SAP',
  `c_changes`           json         DEFAULT NULL COMMENT '变动内容，格式为{"字段名": {"old": "旧值", "new": "新值", "desc":"字段描述"}}',
  `c_change_user`       varchar(50)  DEFAULT NULL COMMENT '修改人',
  `c_change_user_id`    varchar(32)  DEFAULT NULL COMMENT '修改人id',
  `c_create_time`       bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state`             varchar(1)   DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`),
  KEY                   `c_purchase_apply_id` (`c_purchase_apply_id`),
  KEY                   `c_review_id` (`c_review_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请变动记录';

CREATE TABLE `t_purchase_apply_item`
(
  `id`                        varchar(32) NOT NULL COMMENT '唯一id',
  `c_purchase_apply_id`       varchar(32)    DEFAULT NULL COMMENT '关联采购申请主表id',
  `c_project_no`              varchar(200)   DEFAULT NULL COMMENT '项目编码',
  `c_component_product_code`  varchar(200)   DEFAULT NULL COMMENT '物料组件编码',
  `c_component_product_name`  varchar(255)   DEFAULT NULL COMMENT '物料名称',
  `c_component_brand`         varchar(100)   DEFAULT NULL COMMENT '品牌',
  `c_component_specification` varchar(100)   DEFAULT NULL COMMENT '规格',
  `c_component_model`         varchar(100)   DEFAULT NULL COMMENT '型号',
  `c_component_quantity`      decimal(18, 3) DEFAULT NULL COMMENT '需求数量',
  `c_component_unit`          varchar(255)   DEFAULT NULL COMMENT '单位',
  `c_component_factory`       varchar(255)   DEFAULT NULL COMMENT '工厂',
  `c_component_demand_date`   bigint(20) DEFAULT NULL COMMENT '需求日期',
  `c_line_item_category`      varchar(50)    DEFAULT NULL COMMENT '行项目类别',
  `c_mrp_type`                varchar(50)    DEFAULT NULL COMMENT 'MRP类型',
  `c_used_quantity`           decimal(18, 3) DEFAULT NULL COMMENT '已使用数量',
  `c_create_time`             bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_update_time`             bigint(20) DEFAULT NULL COMMENT '更新时间',
  `c_state`                   varchar(1)     DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`),
  KEY                         `c_purchase_apply_id` (`c_purchase_apply_id`),
  KEY                         `c_state` (`c_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购申请组件清单';


--采购申请v2导出字段
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('1001', NULL, '采购申请单号', 1, '77', 1, '1', 0);
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('1002', NULL, '物料编码', 2, '77', 1, '1', 0);
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('1003', NULL, '申请单信息', 3, '77', NULL, '1', 0);
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('1004', NULL, '物料明细', 4, '77', NULL, '1', 0);
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('1005', NULL, '组件信息', 5, '77', NULL, '1', 0);

--申请单信息
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES
  ('1006', '1003', '采购申请类型', 6, '77', NULL, '1', 1),
  ('1007', '1003', '订货状态', 7, '77', NULL, '1', 1),
  ('1008', '1003', '取消状态', 8, '77', NULL, '1', 1),
  ('1009', '1003', '创建日期', 9, '77', NULL, '1', 1),
  ('1010', '1003', '客户订单号', 10, '77', NULL, '1', 1),
  ('1011', '1003', '销售订单号', 11, '77', NULL, '1', 1),
  ('1012', '1003', '跟单员', 12, '77', NULL, '1', 1),
  ('1013', '1003', '业务员', 13, '77', NULL, '1', 1),
  ('1014', '1003', '销售组织', 14, '77', NULL, '1', 1),
  ('1015', '1003', '采购组织', 15, '77', NULL, '1', 0),
  ('1016', '1003', '项目编码', 16, '77', NULL, '1', 1),
  ('1017', '1003', '项目名称', 17, '77', NULL, '1', 1),
  ('1018', '1003', '关联采购订单', 18, '77', NULL, '1', 0),
  ('1019', '1003', '售达方', 19, '77', NULL, '1', 0),
  ('1020', '1003', '收件人', 20, '77', NULL, '1', 1),
  ('1021', '1003', '联系方式', 21, '77', NULL, '1', 1),
  ('1022', '1003', '收件地址', 22, '77', NULL, '1', 0),
  ('1023', '1003', '发货方式', 23, '77', NULL, '1', 0),
  ('1024', '1003', '申请单备注', 24, '77', NULL, '1', 0),
  ('1025', '1003', '修改记录', 25, '77', NULL, '1', 0),
  ('1026', '1003', '组件信息', 26, '77', NULL, '1', 0),
  ('1027', '1003', '科目分配类别', 27, '77', NULL, '1', 0),
  ('1028', '1003', '总账科目', 28, '77', NULL, '1', 0),
  ('1029', '1003', '成本中心', 29, '77', NULL, '1', 0),
  ('1030', '1003', '订单', 30, '77', NULL, '1', 0),
  ('1031', '1003', '交货日期', 31, '77', NULL, '1', 0),
  ('1032', '1003', '物料组', 32, '77', NULL, '1', 0),
  ('1033', '1003', '固定的供应商', 33, '77', NULL, '1', 0);

--物料明细
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES
  ('1034', '1004', '物料序号', 34, '77', NULL, '1', 1),
  ('1035', '1004', '资产卡片', 35, '77', NULL, '1', 0),
  ('1036', '1004', '物料名称', 36, '77', NULL, '1', 1),
  ('1037', '1004', '品牌', 37, '77', NULL, '1', 1),
  ('1038', '1004', '型号', 38, '77', NULL, '1', 0),
  ('1039', '1004', '规格', 39, '77', NULL, '1', 1),
  ('1040', '1004', '单位', 40, '77', NULL, '1', 1),
  ('1041', '1004', '描述', 41, '77', NULL, '1', 1),
  ('1042', '1004', '申请数量', 42, '77', NULL, '1', 1),
  ('1043', '1004', '已订货数量', 43, '77', NULL, '1', 1),
  ('1044', '1004', '销售单价', 44, '77', NULL, '1', 1),
  ('1045', '1004', '销售需求数量', 45, '77', NULL, '1', 1),
  ('1046', '1004', 'MPM参考结算价', 46, '77', NULL, '1', 1),
  ('1047', '1004', '仓库', 47, '77', NULL, '1', 1),
  ('1048', '1004', '计划需求日期', 48, '77', NULL, '1', 1),
  ('1049', '1004', '采购员', 49, '77', NULL, '1', 1),
  ('1050', '1004', '采购部门', 50, '77', NULL, '1', 1);

--组件信息
INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES
  ('1051', '1005', '项目编号', 51, '77', NULL, '1', 0),
  ('1052', '1005', '组件物料编码', 52, '77', NULL, '1', 1),
  ('1053', '1005', '物料名称', 53, '77', NULL, '1', 1),
  ('1054', '1005', '品牌', 54, '77', NULL, '1', 0),
  ('1055', '1005', '单位', 55, '77', NULL, '1', 1),
  ('1056', '1005', '需求数量', 56, '77', NULL, '1', 1),
  ('1057', '1005', '已使用数量', 57, '77', NULL, '1', 1),
  ('1058', '1005', '剩余数量', 58, '77', NULL, '1', 1),
  ('1059', '1005', '规格', 59, '77', NULL, '1', 1),
  ('1060', '1005', '型号', 60, '77', NULL, '1', 0),
  ('1061', '1005', 'MRP类型', 61, '77', NULL, '1', 0),
  ('1062', '1005', '行项目类别', 62, '77', NULL, '1', 0);

ALTER TABLE t_purchase_apply_item ADD c_component_unit_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '单位名称';
ALTER TABLE t_purchase_apply_item CHANGE c_component_unit_name c_component_unit_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '单位名称' AFTER c_component_unit;
