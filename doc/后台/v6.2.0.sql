ALTER TABLE t_order ADD c_title_of_the_contract varchar(15) NULL COMMENT '签约抬头，数据源：OMS';
update t_order set c_title_of_the_contract = "1025";

ALTER TABLE `t_order`
  ADD COLUMN `c_payment_condition` varchar(32) NULL DEFAULT NULL COMMENT '付款形式' AFTER `c_title_of_the_contract`,
ADD COLUMN `c_back_to_back` tinyint(1) NULL DEFAULT NULL COMMENT '账期（是否为背靠背）' AFTER `c_payment_condition`,
ADD COLUMN `c_accounting_period` int(10) NULL DEFAULT NULL COMMENT '账期（单位：天）' AFTER `c_back_to_back`;
ADD COLUMN `c_payment_condition_time` bigint(20) NULL DEFAULT NULL COMMENT '付款条件满足时间' AFTER `c_accounting_period`;
ADD COLUMN `c_predict_payment_time` bigint(20) NULL DEFAULT NULL COMMENT '预计付款时间' AFTER `c_payment_condition_time`;
