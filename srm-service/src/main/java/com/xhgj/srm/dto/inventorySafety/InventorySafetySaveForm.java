package com.xhgj.srm.dto.inventorySafety;/**
 * @since 2025/2/19 13:10
 */

import lombok.Data;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/19 13:10:06
 *@description
 */
@Data
public class InventorySafetySaveForm {
  /**
   * id
   */
  private String id;

  /**
   * 库房id
   */
  @NotBlank(message = "库房id不能为空")
  private String warehouseId;

  /**
   * 物料编码
   */
  @NotBlank(message = "物料编码不能为空")
  private String productCode;

  /**
   * 用户组织
   */
  @NotNull(message = "用户组织")
  private String userGroup;

  /**
   * 物料名称
   */
  private String name;

  /**
   * 品牌名称
   */
  private String brandName;
  /**
   * 型号
   */
  private String model;
  /**
   * 基本单位
   */
  private String unit;
  /**
   * 安全库存数量
   */
  @NotNull(message = "安全库存数量不能为空")
  @Max(value = 99999, message = "安全库存数量不能大于99999")
  private BigDecimal minSafetyStock;
  /**
   * 负责人名称
   */
  @NotBlank(message = "负责人名称不能为空")
  private String notifiedPerson;
  /**
   * 负责人Id
   */
  @NotBlank(message = "负责人Id不能为空")
  private String notifiedPersonId;
  /**
   * 负责人工号
   */
  @NotBlank(message = "负责人工号不能为空")
  private String notifiedPersonCode;
  /**
   * 负责人手机号
   */
  @NotBlank(message = "负责人手机号不能为空")
  private String notifiedPersonPhone;

  /**
   * 通知状态
   */
  @NotNull(message = "通知状态不能为空")
  private Byte status;

  /**
   * 获取品牌名称 - 中文
   * @return
   */
  public String getBrandNameCn() {
    // 根据最中间的/分割，前者
    if (brandName == null || brandName.isEmpty()) {
      return "";
    }
    List<Integer> slashIndices = new ArrayList<>();
    for (int i = 0; i < brandName.length(); i++) {
      if (brandName.charAt(i) == '/') {
        slashIndices.add(i);
      }
    }
    if (slashIndices.isEmpty()) {
      return brandName;
    }
    int midIndex = slashIndices.size() / 2;
    int splitPos = slashIndices.get(midIndex);
    return brandName.substring(0, splitPos);
  }

  /**
   * 获取品牌名称 - 英文
   * @return
   */
  public String getBrandNameEn() {
    // 根据最中间的/分割，后者
    if (brandName == null || brandName.isEmpty()) {
      return "";
    }
    List<Integer> slashIndices = new ArrayList<>();
    for (int i = 0; i < brandName.length(); i++) {
      if (brandName.charAt(i) == '/') {
        slashIndices.add(i);
      }
    }
    if (slashIndices.isEmpty()) {
      return "";
    }
    int midIndex = slashIndices.size() / 2;
    int splitPos = slashIndices.get(midIndex);
    return brandName.substring(splitPos + 1);
  }
}
