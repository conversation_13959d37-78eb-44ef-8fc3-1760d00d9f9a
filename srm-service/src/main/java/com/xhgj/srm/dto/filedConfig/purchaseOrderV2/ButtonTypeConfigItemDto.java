package com.xhgj.srm.dto.filedConfig.purchaseOrderV2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/9 10:18
 * @Description: 采购订单按钮配置参数 - item按钮配置项
 */
@Data
public class ButtonTypeConfigItemDto {

  @ApiModelProperty("标题")
  private String title;

  @ApiModelProperty("订单类型枚举")
  private String orderTypeEnum;

  /**
   * 保存时转成
   * @see ButtonFieldDto
   */
  @ApiModelProperty("字段List")
  private List<ButtonFieldVo> fieldVo;

}
