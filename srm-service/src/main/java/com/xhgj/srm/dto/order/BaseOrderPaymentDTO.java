package com.xhgj.srm.dto.order;

import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.jpa.entity.OrderPayment;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName BaseOrderPaymentDTP */
@Data
@NoArgsConstructor
public abstract class BaseOrderPaymentDTO {

  @ApiModelProperty("付款单 id")
  private String id;

  @ApiModelProperty("付款单号")
  private String paymentNo;

  @ApiModelProperty("付款状态")
  private String paymentStatus;

  @ApiModelProperty("申请付款金额")
  private BigDecimal applyPrice;

  @ApiModelProperty("已付金额")
  private BigDecimal paymentPrice;

  @ApiModelProperty("提交人")
  private String submitMan;

  @ApiModelProperty("提交时间")
  private Long createTime;

  @ApiModelProperty("供应商id")
  private String supplierId;

  /**
   * 来源(1后台 2scp前台 3移动端前台)
   * @see com.xhgj.srm.common.enums.order.OrderPaymentSource
   **/
  @ApiModelProperty("1后台提交 2前台提交 3移动端提交")
  private Byte source;

  @ApiModelProperty("驳回原因")
  private String reason;

  @ApiModelProperty("付款方式")
  private String payType;

  @ApiModelProperty("付款方式中文")
  private String payTypeValue;

  @ApiModelProperty("其他付款描述")
  private String payOtherDesc;

  /**
   * 付款方式
   * @return
   */
  public String getPayTypeValue() {
    return PayTypeSAPEnums.getNameByCode(payType);
  }

  public BaseOrderPaymentDTO(OrderPayment orderPayment,BigDecimal applyPrice) {
    this.id = orderPayment.getId();
    this.paymentNo = orderPayment.getPaymentNo();
    this.paymentStatus = orderPayment.getPaymentStatus();
    this.applyPrice = applyPrice.stripTrailingZeros();
    this.submitMan = orderPayment.getSubmitMan();
    this.createTime = orderPayment.getCreateTime();
    this.paymentPrice = orderPayment.getPaymentPrice();
    this.supplierId = orderPayment.getSupplierId();
    this.source = orderPayment.getSource();
    this.reason = orderPayment.getReason();
    this.payType = orderPayment.getPayType();
    this.payOtherDesc = orderPayment.getPayOtherDesc();
  }
}
