package com.xhgj.srm.dto.entryregistration;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 入驻报备单落地商信息 DTO
 */
@Data
@Validated
public class EntryRegistrationLandingMerchantAddParam {

  /**
   * ID。
   */
  @ApiModelProperty("id")
  private String id;

  /**
   * 企业名称。
   */
  @ApiModelProperty("企业名称")
  @NotBlank(message = "企业名称不能为空")
  private String enterpriseName;

  /**
   * 统一社会信用代码。
   */
  @ApiModelProperty("统一社会信用代码")
  @NotBlank(message = "统一社会信用代码不能为空")
  private String uscc;

  /**
   * 公司属性类型。
   */
  @ApiModelProperty("公司属性类型")
  @NotBlank(message = "公司属性类型不能为空")
  private String companyAttributeType;

  /**
   * 法定代表人。
   */
  @ApiModelProperty("法定代表人")
  @NotBlank(message = "法定代表人不能为空")
  private String corporate;

  /**
   * 注册地址。
   */
  @ApiModelProperty("注册地址")
  @NotBlank(message = "注册地址不能为空")
  private String regAddress;

  /**
   * 账户名称。
   */
  @ApiModelProperty("账户名称")
  @NotBlank(message = "账户名称不能为空")
  private String accountName;

  /**
   * 电话。
   */
  @ApiModelProperty("电话")
  @NotBlank(message = "电话不能为空")
  private String phone;

  /**
   * 开户行地址。
   */
  @ApiModelProperty("开户行地址")
  @NotBlank(message = "开户行地址不能为空")
  private String bankAddress;

  /**
   * 开户行名称。
   */
  @ApiModelProperty("开户行名称")
  @NotBlank(message = "开户行名称不能为空")
  private String bankName;

  /**
   * 银行账户号。
   */
  @ApiModelProperty("银行账户号")
  @NotBlank(message = "银行账户号不能为空")
  private String bankAccountNumber;

  /**
   * 银行联行号。
   */
  @ApiModelProperty("银行联行号")
  @NotBlank(message = "银行联行号不能为空")
  private String bankCode;

  /**
   * 账号使用人。
   */
  @ApiModelProperty("账号使用人")
  @NotBlank(message = "账号使用人不能为空")
  private String accountUser;

  /**
   * 账号使用人电话。
   */
  @ApiModelProperty("账号使用人电话")
  @NotBlank(message = "账号使用人电话不能为空")
  private String accountUserPhone;

  /**
   * 票种。
   */
  @ApiModelProperty("票种")
  @NotBlank(message = "票种不能为空")
  private String invoiceType;

  /**
   * 税率。
   */
  @ApiModelProperty("税率")
  @NotNull(message = "税率不能为空")
  private BigDecimal taxRate;

  /**
   * 邮箱地址。
   */
  @ApiModelProperty("邮箱地址")
  @NotBlank(message = "邮箱地址不能为空")
  private String emailAddress;

  @ApiModelProperty("城市")
  @NotBlank(message = "城市不能为空")
  private String city;

  @ApiModelProperty("区域")
  @NotBlank(message = "区域不能为空")
  private String region;

  @ApiModelProperty("行业")
  @NotBlank(message = "行业不能为空")
  private String industry;

  @ApiModelProperty("省份")
  @NotBlank(message = "省份不能为空")
  private String province;

  @ApiModelProperty("mdm编码")
  @NotBlank(message = "mdm编码不能为空")
  private String mdmCode;

  @ApiModelProperty("营业执照")
  @NotBlank(message = "请上传营业执照")
  private String license;
  @ApiModelProperty("身份证照片（正反面在同一张）")
  @NotBlank(message = "请上传身份证照片")
  private String idCardPhoto;
  @ApiModelProperty("产品资质书")
  private List<String> productQualification;
  @ApiModelProperty("报备单id")
  @NotBlank(message = "报备单id不能为空")
  private String entryRegistrationOrderId;
}