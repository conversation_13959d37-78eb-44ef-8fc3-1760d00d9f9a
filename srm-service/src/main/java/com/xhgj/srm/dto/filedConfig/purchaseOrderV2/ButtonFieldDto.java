package com.xhgj.srm.dto.filedConfig.purchaseOrderV2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/9 9:51
 * @Description: 采购订单按钮配置参数 - 按钮配置项   存库json
 */
@Data
public class ButtonFieldDto {

  @ApiModelProperty("所属页面")
  private String page;

  @ApiModelProperty("条件")
  private String condition;

  @ApiModelProperty("按钮名称")
  private String buttonName;

  @ApiModelProperty("是否选中 0-否 1-是")
  private String isChecked;

}
