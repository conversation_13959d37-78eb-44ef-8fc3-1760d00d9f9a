package com.xhgj.srm.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AccountPageQuery {

    @NotBlank(message = "登录人id不能为空")
    @ApiModelProperty("登录人id")
    private String userId;
    @ApiModelProperty("对账单号")
    private String accountNo;
    @ApiModelProperty("对账单状态(1--待开票,2--已开票,3--部分回款,4--已回款,5--已作废,0--履约平台除待开票外对账单)")
    private String accountState;
    @ApiModelProperty("发票附件(1--未上传,2--已上传)")
    private String invoicingState;
    @ApiModelProperty("供应商名称")
    private String supplierName;
    @ApiModelProperty("对账金额")
    private String accountPrice;
    @ApiModelProperty("已回金额")
    private String returnAmount;
    @ApiModelProperty("提交日期(起始)")
    private String accountStartTime;
    @ApiModelProperty("提交日期(截止)")
    private String accountEndTime;
    @ApiModelProperty("方案id")
    private String schemeId;
    @ApiModelProperty(value="当前页",example = "1")
    private Integer pageNo;
    @ApiModelProperty(value="每页展示数量",example = "10")
    private Integer pageSize;
}
