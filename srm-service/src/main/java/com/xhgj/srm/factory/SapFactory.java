package com.xhgj.srm.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.OrderErpTypeEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PurchaseApplicationTypeEnum;
import com.xhgj.srm.common.enums.SettleCurrency;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderStatus;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderType;
import com.xhgj.srm.common.utils.SAPToolUtils;
import com.xhgj.srm.common.utils.TimeStampUtil;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhgj.srm.jpa.entity.OrderDeliveryDetail;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.OrderCancelDetailRepository;
import com.xhgj.srm.jpa.repository.OrderDeliveryDetailRepository;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.ConstantWms;
import com.xhgj.srm.request.dto.OrderReturnDetailInfo;
import com.xhgj.srm.request.service.third.erp.sap.SapAdvanceApplyRequest;
import com.xhgj.srm.request.service.third.erp.sap.dto.*;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam.DATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO.PurchaseOrderReturnHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Param.HEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.DataInfo;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Head;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.WWDTO;
import com.xhgj.srm.request.service.third.mpm.impl.MPMServiceExtImpl;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderItemVO;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderVO;
import com.xhgj.srm.vo.transferOrder.TransferOrderItemVO;
import com.xhgj.srm.vo.transferOrder.TransferOrderVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.upload.util.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SapFactory {
  /**
   * 厂家直发的快递100的物流公司代码
   */
  public static final String LOGISTICS_COMPANY_CODE = "00001";
  /**
   * MM031退货movementType
   */
  public static final String MOVEMENT_TYPE_122 = "122";
  @Resource
  private OrderDetailRepository orderDetailRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private OrderDeliveryDetailRepository orderDeliveryDetailRepository;
  @Resource
  private SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  private MPMServiceExtImpl mpmServiceExt;
  @Resource
  private UserDao userDao;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private UserRepository userRepository;

  @Resource
  OrderCancelDetailRepository orderCancelDetailRepository;
  @Autowired
  private PaymentApplyDetailRepository paymentApplyDetailRepository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Autowired
  private FinancialVoucherRepository financialVoucherRepository;
  @Resource private FileRepository fileRepository;
  @Resource private OssUtil ossUtil;
  @Resource
  ShareInputInvoiceService shareInputInvoiceService;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  /**
   * 创建退换货订单的MM031参数 --- 新建退库单
   */
  public ReceiptVoucherSynchronizationParam createMM031ParamForAddNewReturn(
      ReturnExchangeOrder exchangeOrder,
      SupplierOrder supplierOrder,
      SupplierOrderToForm returnForm,
      List<SupplierOrderDetail> returnOrderDetails
  ) {
    ReceiptVoucherSynchronizationParam sapParam = new ReceiptVoucherSynchronizationParam();
    DataInfo dataInfo = new DataInfo();
    Head head = new Head();
    List<Item> items = new ArrayList<>();
    String operator = Optional.ofNullable(supplierOrder.getPurchaseMan()).orElse("");
    // operator去除数字
    operator = operator.replaceAll("\\d", "");
    head.setMaterialPostingDate(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
    head.setVoucherDate(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
    head.setSrmId(returnForm.getId());
    Boolean directShipment = Boolean.TRUE.equals(supplierOrder.getDirectShipment());
    if (Boolean.FALSE.equals(directShipment)) {
      head.setZxkbj("X");
    } else {
      head.setZxkbj("");
    }
    head.setMovementType("101");
    head.setTextRemark(exchangeOrder.getReturnReason());
    head.setLogisticsCompany(returnForm.getLogisticsCompany());
    head.setLogisticsNumber(returnForm.getTrackNum());
    head.setOperator(operator);
    head.setConsignee(returnForm.getConsignee());
    head.setConsigneeAddress(returnForm.getReceiveAddress());
    for (SupplierOrderDetail returnOrderDetail : returnOrderDetails) {
      SupplierOrderProduct supplierOrderProduct =
          supplierOrderProductRepository.findById(returnOrderDetail.getOrderProductId())
              .orElseThrow(() -> new CheckException("未找到对应的supplierOrderProduct"));
      Item item = new Item();
      item.setPurchaseOrderNumber(supplierOrder.getCode());
      item.setPurchaseOrderLineItemNo(returnOrderDetail.getSortNum().toString());
      item.setMaterialNumber(supplierOrderProduct.getCode());
      item.setQuantity(returnOrderDetail.getStockOutputQty().stripTrailingZeros().toPlainString());
      item.setBaseUnitOfMeasure(supplierOrderProduct.getUnitCode());
      item.setFactoryCode(supplierOrder.getGroupCode());
      item.setWarehouseLocation(returnOrderDetail.getWarehouse());
      // todo这个值可能有问题
//      item.setReferenceMaterialDocumentNumber("161");
      items.add(item);
    }
    head.setItems(items);
    dataInfo.setHead(head);
    sapParam.setData(dataInfo);
    return sapParam;
  }

  /**
   * 创建退换货订单的 MM021 参数
   */
  public UpdatePurchaseOrderSapParam createMM021ParamForReturnExchangeOrder(
      ReturnExchangeOrder exchangeOrder,
      SupplierOrder supplierOrder,
      List<SupplierOrderProduct> supplierOrderProducts,
      List<SupplierOrderDetail> supplierOrderDetails
  ) {
    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("未找到对应的供应商"));
    Supplier invoiceSupplier =
        supplierRepository.findFirstByEnterpriseNameAndState(supplierOrder.getInvoicingParty(),
            Constants.STATE_OK).orElseThrow(() -> new CheckException("未找到对应的开票方"));
    UpdatePurchaseOrderSapParam result = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO data = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO head = new UpdatePurchaseOrderHEADDTO();
    List<ITEMDTO> itemdtos = new ArrayList<>();
    head.setEbeln(supplierOrder.getCode());
    head.setBsart(supplierOrder.getOrderType());
    head.setEkorg(supplierOrder.getGroupCode());
    head.setEkgrp(supplierOrder.getPurchaseDeptCode());
    head.setBukrs(supplierOrder.getGroupCode());
    head.setZttwb(exchangeOrder.getReturnReason());
    head.setZycgdh(exchangeOrder.getOriginalOrderNo());
    // 凭证日期 若为退换货订单则传递退换货订单创建日期字段
    // Long 转 str
    head.setBedat(DateUtil.format(new Date(exchangeOrder.getCreateTime()), DatePattern.PURE_DATE_PATTERN));
    head.setLifnr(supplier.getMdmCode());
    head.setLifn2(invoiceSupplier.getMdmCode());
    head.setCountry("CN");
    head.setSpras(null);
    head.setZterm_01(null);
    head.setZzsp_01(null);
    head.setZthlx(Constants_Sap.RETURN_ORDER_TYPE_ADD);
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      head.setName1(supplier.getEnterpriseName());
      head.setCity1("一次性供应商");
    }else {
      head.setName1(null);
      head.setCity1(null);
    }
    if (Boolean.TRUE.equals(supplierOrder.getLoss())) {
      head.setZzjth("X");
    }else {
      head.setZzjth("");
    }
    //20250210 创建、修改采购订单时，如果WAERS货币码是JPY，汇率传递订单汇率*100
    head.setWkurs(StrUtil.equals(SettleCurrency.PRE004.getKey(), supplierOrder.getMoneyCode())
        ? NumberUtil.mul(NumberUtil.toBigDecimal(supplierOrder.getOrderRate()), BigDecimal.valueOf(100)).toString()
        : supplierOrder.getOrderRate());
    final String payment_iterm = "0001";
    head.setZterm(payment_iterm);
    head.setWaers(supplierOrder.getMoneyCode());
    head.setSRMID(null);
    //若为退换货订单则不传X,需要审批
    head.setZsp(null);
    head.setBednr(supplierOrder.getPurchaseCode());
    head.setZycgdh(exchangeOrder.getOriginalOrderNo());
    head.setAfnam(supplierOrder.getPurchaseMan().substring(4, supplierOrder.getPurchaseMan().length()));
    for (int i = 0; i < supplierOrderDetails.size(); i++) {
      SupplierOrderProduct supplierOrderProduct = supplierOrderProducts.get(i);
      SupplierOrderDetail supplierOrderDetail = supplierOrderDetails.get(i);
      ITEMDTO itemdto = new ITEMDTO();
      itemdto.setEbelp(supplierOrderDetail.getSortNum().toString());
      itemdto.setMatnr(supplierOrderProduct.getCode());
      itemdto.setTxz01(supplierOrderProduct.getName());
      itemdto.setMeins(supplierOrderProduct.getUnitCode());
      itemdto.setMenge(supplierOrderDetail.getNum().stripTrailingZeros().toPlainString());
      itemdto.setWerks(supplierOrder.getGroupCode());
      itemdto.setLgort(supplierOrderDetail.getWarehouse());
      itemdto.setAplfz(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
      itemdto.setMatkl(null);
      itemdto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(supplierOrderDetail.getTaxRate()));
      Pair<BigDecimal, Integer> convertSapPrice =
          SAPToolUtils.convertSapPrice(supplierOrderDetail.getPrice(), 2);
      itemdto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemdto.setPeinh(convertSapPrice.getValue().toString());
      itemdto.setBprme(supplierOrderProduct.getUnitCode());
      itemdto.setZgsje(null);
      itemdto.setZyyje(null);
      itemdto.setZzf1(null);
      itemdto.setZjsj(null);
      itemdto.setCharX(supplierOrderDetail.getBatchNo());
      itemdto.setRetpo("X");
      if (Constants.STATE_OK.equals(supplierOrderDetail.getFreeState())) {
        itemdto.setZmfbs("X");
      }else {
        itemdto.setZmfbs("");
      }
      if (Constants.PROJECT_TYPE_JS.equals(exchangeOrder.getProjectType())) {
        itemdto.setPstyp("K");
      }else {
        itemdto.setPstyp("");
      }
      if (Boolean.TRUE.equals(exchangeOrder.getNeedRedInvoice())) {
        itemdto.setZsfkhp("X");
      }else {
        itemdto.setZsfkhp("");
      }
      itemdto.setKnttp("");
      itemdto.setMatkl(null);
      itemdto.setZgsje(null);
      itemdto.setZyyje(null);
      itemdto.setZjsj(null);
      itemdto.setKostl(null);
      itemdto.setBanfn(null);
      itemdto.setBnfpo(null);
      itemdto.setElikz(null);
      itemdto.setLoekz(null);
      itemdto.setZzwlsl(null);
      itemdto.setZzfjf(null);
      itemdto.setZzkhddh_01(null);
      itemdto.setZzxmbh_01(null);
      itemdto.setZZPOSNR(null);
      itemdto.setZZVBELN(null);
      itemdtos.add(itemdto);
    }
    head.setItem(itemdtos);
    data.setHead(head);
    result.setData(data);
    return result;
  }

  /**
   * 创建采购订单创建/修改接口 MM_021 参数<br>
   * 所有行调整为交货完成
   * @return
   */
  public UpdatePurchaseOrderSapParam createMM021Param(Order order) {
    UpdatePurchaseOrderSapParam result = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO data = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO head = new UpdatePurchaseOrderHEADDTO();
    head.setEbeln(order.getErpOrderNo());
    head.setZsp("X");
    List<OrderDetail> orderDetails =
        orderDetailRepository.findAllByOrderIdAndState(order.getId(), Constants.STATE_OK);
    ArrayList<ITEMDTO> items = new ArrayList<>();
    for (OrderDetail orderDetail : orderDetails) {
      ITEMDTO item = new ITEMDTO();
      item.setEbelp(orderDetail.getPurchaseOrderRowNo());
      item.setCharX(order.getErpOrderNo());
      item.setZZPOSNR(orderDetail.getRowNo());
      item.setElikz("X");
      items.add(item);
    }
    head.setItem(items);
    data.setHead(head);
    result.setData(data);
    return result;
  }

  public MM_075Param createMM075Param(Order order, OrderReturn orderReturn, List<OrderReturnDetailInfo> returnList) {
    PurchaseOrderReturnHEADDTO head = new PurchaseOrderReturnHEADDTO();
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.throwExceptionIfNotFind(order.getTitleOfTheContract());
    head.setEkorg(titleOfTheContractEnum.getCode());
    // fixme 是否有问题
    //万聚251
    String ekgrp = "251";
    if (titleOfTheContractEnum == TitleOfTheContractEnum.TITLE_OF_HEADQUARTERS) {
      //总部001
      ekgrp = "001";
    }
    head.setEkgrp(ekgrp);
    head.setBukrs(titleOfTheContractEnum.getCode());
    head.setEbeln("20"+ RandomUtil.randomNumbers(8));
    head.setBsart("Z050");
    head.setZttwb("{" + order.getErpOrderNo() + "}通过SRM生成采购退货");
    if (orderReturn.getApplyTime() == null) {
      head.setBedat(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
    }else {
      head.setBedat(DateUtil.format(DateUtil.date(orderReturn.getApplyTime()), "yyyyMMdd"));
    }
    head.setLifnr(order.getSupplier().getMdmCode());
    head.setLifn2(order.getSupplier().getMdmCode());
    head.setCountry("CN");
    head.setZterm("0001");
    head.setWaers("CNY");
    //增加申请人和工号
    head.setZycgdh(order.getErpOrderNo());
    setDockingPurchaseInfo(order, head);
    ArrayList<PurchaseOrderReturnHEADDTO.ITEMDTO> items = new ArrayList<>();
    // returnList，对于相同的orderDetail进行合并
    Map<String, List<OrderReturnDetailInfo>> id2OrderReturnDetailInfos =
        returnList.stream().collect(Collectors.groupingBy(item -> item.getOrderDetail().getId()));
    // 合并相同的 OrderReturnDetailInfo
    List<OrderReturnDetailInfo> newReturnList = id2OrderReturnDetailInfos.values().stream()
        .map(orderReturnDetails -> {
          OrderReturnDetail one = orderReturnDetails.get(0).getOrderReturnDetail();
          one.setReturnNum(orderReturnDetails.stream()
              .map(OrderReturnDetailInfo::getOrderReturnDetail)
              .map(OrderReturnDetail::getReturnNum)
              .reduce(BigDecimal.ZERO, BigDecimal::add));
          OrderDetail firstOne = orderReturnDetails.get(0).getOrderDetail();
          return new OrderReturnDetailInfo(firstOne, one);
        })
        .collect(Collectors.toList());
    for (OrderReturnDetailInfo orderReturnDetailInfo : newReturnList) {
      OrderDetail orderDetail = orderReturnDetailInfo.getOrderDetail();
      OrderReturnDetail orderReturnDetail = orderReturnDetailInfo.getOrderReturnDetail();
      BigDecimal returnNum = orderReturnDetail.getReturnNum();
      PurchaseOrderReturnHEADDTO.ITEMDTO item = new PurchaseOrderReturnHEADDTO.ITEMDTO();
      item.setEbelp(orderDetail.getPurchaseOrderRowNo());
      item.setMatnr(orderDetail.getInnerCode());
      String productUnitCode = mpmServiceExt.getProductUnitCode(orderDetail.getInnerCode());
      item.setMeins(productUnitCode);
      if (returnNum == null) {
        throw new CheckException("退货数量不能为空");
      }
      item.setMenge(returnNum.stripTrailingZeros().toPlainString());
      item.setWerks(titleOfTheContractEnum.getCode());
      item.setLgort("6000");
      item.setAplfz(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
      item.setMwskz(Constants.TAX_RATE_TYPE_NUM.getOrDefault(new BigDecimal(orderDetail.getCostPriceTaxRate()).stripTrailingZeros(), ""));
      BigDecimal price = orderDetail.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      item.setNetpr(convertSapPrice.getKey().toPlainString());
      item.setPeinh(convertSapPrice.getValue().toString());
      item.setBprme(productUnitCode);
//      item.setCharX(order.getErpOrderNo());
      // char修改为入库单采购订单号
      String deliveryDetailId = orderReturnDetail.getDeliveryDetailId();
      OrderDeliveryDetail orderDeliveryDetail =
          orderDeliveryDetailRepository.findById(deliveryDetailId)
              .orElseThrow(() -> new CheckException("未找到对应的发货单明细"));
      OrderDelivery delivery = orderDeliveryDetail.getDelivery();
      item.setCharX(delivery.getOrderErpNo());
      item.setRetpo(Constants_Sap.CONFIRM_IDENTIFICATION);
      items.add(item);
    }
    MM_075Param param = new MM_075Param();
    PurchaseOrderReturnDATADTO data = new PurchaseOrderReturnDATADTO();
    head.setItem(items);
    data.setHead(head);
    param.setData(data);
    return param;
  }

  /**
   * 根据订单信息设置对接采购姓名）和（对接采购工号）属性。
   * @param order 订单对象，包含类型和供应商ID
   * @param head  需要设置afnam和bednr属性的对象
   */
  private void setDockingPurchaseInfo(Order order, PurchaseOrderReturnHEADDTO head) {
    // 查找对接采购ERP代码
    String dockingPurchaseErpCode = Optional.ofNullable(
            supplierPerformanceRepository.findFirstByPlatformCodeAndSupplierIdAndStateOrderByCreateTimeDesc(
                order.getType(),
                order.getSupplierId(),
                Constants.STATE_OK))
        .map(SupplierPerformance::getDockingPurchaseErpCode)
        .orElse(StrUtil.EMPTY);

    // 如果找到了有效的ERP代码，则进一步查找对接采购人的实际姓名并设置到head对象
    if (StrUtil.isNotBlank(dockingPurchaseErpCode)) {
      String dockingPurchaseName = Optional.ofNullable(userDao.getUserByCode(dockingPurchaseErpCode))
          .map(User::getRealName)
          .orElse(StrUtil.EMPTY);
      head.setAfnam(dockingPurchaseName);
      head.setBednr(dockingPurchaseErpCode);
    }
  }

  public ReceiptVoucherSynchronizationParam createMM031OrderReturn(Order order, OrderReturn orderReturn, List<OrderReturnDetailInfo> returnList) {
    String titleOfTheContract = order.getTitleOfTheContract();
    Group group = groupRepository.findFirstByCodeAndState(titleOfTheContract, Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("未找到对应的group");
    }
    String dateTime =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    ReceiptVoucherSynchronizationParam sapParam = new ReceiptVoucherSynchronizationParam();
    DataInfo dataInfo = new DataInfo();
    Head head = new Head();
    head.setMaterialPostingDate(dateTime);
    head.setVoucherDate(dateTime);
    head.setMovementType(MOVEMENT_TYPE_122);
    head.setLogisticsCompany(LOGISTICS_COMPANY_CODE);
    head.setLogisticsNumber("电供业务自动退货，无单号");
    head.setSrmId(orderReturn.getId());
    List<Item> items = new ArrayList<>();
    for (OrderReturnDetailInfo orderReturnDetailInfo : returnList) {
      OrderDetail orderDetail = orderReturnDetailInfo.getOrderDetail();
      OrderReturnDetail orderReturnDetail = orderReturnDetailInfo.getOrderReturnDetail();
      BigDecimal returnNum = orderReturnDetail.getReturnNum();
      Item item = new Item();
      // 采购订单号
      item.setPurchaseOrderNumber(order.getErpOrderNo());
      // 采购订单物料行id
      item.setPurchaseOrderLineItemNo(orderDetail.getPurchaseOrderRowNo());
      // 物料编码
      item.setMaterialNumber(orderDetail.getInnerCode());
      // 数量
      item.setQuantity(returnNum.stripTrailingZeros().toPlainString());
      // 单位
      item.setBaseUnitOfMeasure(orderDetail.getUnit());
      // 传递订单签约抬头对应的ERP编码
      item.setFactoryCode(group.getErpCode());
      // 默认传6000
      item.setWarehouseLocation("6000");
      String deliveryDetailId = orderReturnDetail.getDeliveryDetailId();
      OrderDeliveryDetail orderDeliveryDetail =
          orderDeliveryDetailRepository.findById(deliveryDetailId)
              .orElseThrow(() -> new CheckException("未找到对应的发货单明细"));
      OrderDelivery delivery = orderDeliveryDetail.getDelivery();
      // 关联发货单明细的ERP入库单号
      item.setReferenceMaterialDocumentNumber(delivery.getErpNo());
      // 关联发货单明细的ERP入库单物料行id
      item.setReferenceMaterialDocumentLineNumber(orderDeliveryDetail.getErpRowId());
      items.add(item);
    }
    head.setItems(items);
    dataInfo.setHead(head);
    sapParam.setData(dataInfo);
    return sapParam;
  }

  /**
   * 创建取消退换货订单物料行的 MM021 参数
   * @param returnExchangeOrder 退换货订单
   * @param supplierOrder 关联的采购订单
   * @param cancelDetails 取消详情
   * @return
   */
  public UpdatePurchaseOrderSapParam createMM021ParamForReturnExchangeOrderCancel(
      ReturnExchangeOrder returnExchangeOrder, SupplierOrder supplierOrder,
      List<SupplierOrderDetail> cancelDetails) {
    UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam = new UpdatePurchaseOrderSapParam();
    List<ITEMDTO> itemdtoList = cancelDetails.stream().map(detail -> {
      ITEMDTO itemdto = new ITEMDTO();
      List<WWDTO> wwDtoList = new ArrayList<>();
      WWDTO wwdto = new WWDTO();
      wwdto.setEbelp("@");
      wwdto.setMenge("@");
      wwdto.setWerks("@");
      wwdto.setWlzj("@");
      wwDtoList.add(wwdto);
      itemdto.setWw(wwDtoList);
      itemdto.setEbelp(String.valueOf(detail.getSortNum()));
      itemdto.setMatnr("@");
      itemdto.setTxz01("@");
      itemdto.setMeins("@");
      itemdto.setMenge("@");
      itemdto.setWerks("@");
      itemdto.setLgort("@");
      itemdto.setAplfz("@");
      itemdto.setMatkl("@");
      itemdto.setMwskz("@");
      itemdto.setNetpr("@");
      itemdto.setPeinh("@");
      itemdto.setBprme("@");
      itemdto.setZgsje("@");
      itemdto.setZyyje("@");
      itemdto.setZjsj("@");
      itemdto.setCharX("@");
      itemdto.setRetpo("@");
      itemdto.setZmfbs("@");
      itemdto.setPstyp("@");
      itemdto.setKnttp("@");
      itemdto.setKostl("@");
      itemdto.setBanfn("@");
      itemdto.setBnfpo("@");
      itemdto.setGsgys("@");
      itemdto.setYfgys("@");
      itemdto.setZsfkhp("@");
      //    if (NumberUtil.isGreater(shipQty,BigDecimal.ZERO)||NumberUtil.isGreater(stockInputQty,BigDecimal.ZERO)){
      //      itemdto.setElikz("X");
      //    }
      //    取消物料行调用MM021接口的时候，无论是否发货都在ELIKZ内传X
      //（原先的逻辑要保留，后续有可能还要该回去，现在是因为SAP有一批不太对的数据）
      itemdto.setElikz("X");
      itemdto.setLoekz("@");
      //    if (NumberUtil.equals(shipQty,BigDecimal.ZERO)&&NumberUtil.equals(stockInputQty,
      //        BigDecimal.ZERO)){
      //      itemdto.setLoekz("X");
      //    }
      return itemdto;
    }).collect(Collectors.toList());
    UpdatePurchaseOrderDATADTO dataDTO = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headDTO = new UpdatePurchaseOrderHEADDTO();
    headDTO.setItem(itemdtoList);
    headDTO.setEbeln(supplierOrder.getCode());
    headDTO.setBsart("@");
    headDTO.setEkorg("@");
    headDTO.setEkgrp("@");
    headDTO.setBukrs("@");
    headDTO.setZttwb("@");
    headDTO.setBedat("@");
    headDTO.setLifnr("@");
    headDTO.setLifn2("@");
    headDTO.setWkurs("@");
    headDTO.setZterm("@");
    headDTO.setWaers("@");
    headDTO.setSpras("@");
    headDTO.setZsp("X");
    headDTO.setAfnam("@");
    headDTO.setBednr("@");
    // v7.0.0退换单新增参数
    headDTO.setZycgdh("@");
    headDTO.setZzjth("@");
    headDTO.setZthlx(Constants_Sap.RETURN_ORDER_TYPE_ADD);
    dataDTO.setHead(headDTO);
    updatePurchaseOrderSapParam.setData(dataDTO);
    return updatePurchaseOrderSapParam;
  }

  /**
   * 创建取消退换货订单单个物料行的 MM021 参数
   * @param returnExchangeOrder 退换货订单
   * @param supplierOrder 关联的采购订单
   * @param supplierOrderDetail 取消详情
   * @return
   */
  public UpdatePurchaseOrderSapParam createMM021ParamForReturnExchangeOrderSingleUnCancel(
      ReturnExchangeOrder returnExchangeOrder, SupplierOrder supplierOrder,
      SupplierOrderDetail supplierOrderDetail) {
    UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam = new UpdatePurchaseOrderSapParam();
    List<ITEMDTO> itemdtoList = new ArrayList<>();
    ITEMDTO itemdto = new ITEMDTO();
    List<WWDTO> wwDtoList = new ArrayList<>();
    WWDTO wwdto = new WWDTO();
    wwdto.setEbelp("@");
    wwdto.setMenge("@");
    wwdto.setWerks("@");
    wwdto.setWlzj("@");
    wwDtoList.add(wwdto);
    itemdto.setWw(wwDtoList);
    itemdto.setEbelp(String.valueOf(supplierOrderDetail.getSortNum()));
    itemdto.setMatnr("@");
    itemdto.setTxz01("@");
    itemdto.setMeins("@");
    itemdto.setMenge("@");
    itemdto.setWerks("@");
    itemdto.setLgort("@");
    itemdto.setAplfz("@");
    itemdto.setMatkl("@");
    itemdto.setMwskz("@");
    itemdto.setNetpr("@");
    itemdto.setPeinh("@");
    itemdto.setBprme("@");
    itemdto.setZgsje("@");
    itemdto.setZyyje("@");
    itemdto.setZjsj("@");
    itemdto.setCharX("@");
    itemdto.setRetpo("@");
    itemdto.setZmfbs("@");
    itemdto.setPstyp("@");
    itemdto.setKnttp("@");
    itemdto.setKostl("@");
    itemdto.setBanfn("@");
    itemdto.setBnfpo("@");
    itemdto.setGsgys("@");
    itemdto.setYfgys("@");
    itemdto.setZsfkhp("@");

    itemdto.setElikz(StrUtil.EMPTY);
    itemdto.setLoekz(StrUtil.EMPTY);
    itemdtoList.add(itemdto);

    UpdatePurchaseOrderDATADTO dataDTO = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headDTO = new UpdatePurchaseOrderHEADDTO();
    headDTO.setItem(itemdtoList);
    headDTO.setEbeln(supplierOrder.getCode());
    headDTO.setBsart("@");
    headDTO.setEkorg("@");
    headDTO.setEkgrp("@");
    headDTO.setBukrs("@");
    headDTO.setZttwb("@");
    headDTO.setBedat("@");
    headDTO.setLifnr("@");
    headDTO.setLifn2("@");
    headDTO.setWkurs("@");
    headDTO.setZterm("@");
    headDTO.setWaers("@");
    headDTO.setSpras("@");
    headDTO.setZsp("X");
    headDTO.setAfnam("@");
    headDTO.setBednr("@");
    // v7.0.0退换单新增参数
    headDTO.setZycgdh("@");
    headDTO.setZzjth("@");
    headDTO.setZthlx(Constants_Sap.RETURN_ORDER_TYPE_ADD);
    dataDTO.setHead(headDTO);
    updatePurchaseOrderSapParam.setData(dataDTO);
    return updatePurchaseOrderSapParam;
  }

  /**
   * mm076
   * @param transferOrderVO
   * @return
   */
  public MM_076Param createMM076Param(TransferOrderVO transferOrderVO) {
    MM_076Param head = new MM_076Param();
    head.setBldat(TimeStampUtil.convertTimestampToFormat(transferOrderVO.getReviewTime()));
    head.setBudat(TimeStampUtil.convertTimestampToFormat(System.currentTimeMillis()));
    //为寄售时传k
    String sobkz = TransferOrderType.CONSIGNMENT.getCode().equals(transferOrderVO.getType()) ? "K"
            : StrUtil.EMPTY;
    String bwart = TransferOrderType.CONSIGNMENT.getCode().equals(transferOrderVO.getType()) ?
            "311 K" : "311";
    head.setBwart(bwart);
    head.setUsnam(transferOrderVO.getCreateManName());
    head.setZzbz_01(transferOrderVO.getReason());
    head.setZzname_01(transferOrderVO.getConsignee());
    head.setZzadrc_01(transferOrderVO.getReceiveAddress());
    //成品库退直销库传X  直销库是调入，成品库是调出(1000调入6000和1001调入6000，这两种情况)
    List<TransferOrderItemVO> orderVOItems = transferOrderVO.getItems();
    if (CollUtil.isNotEmpty(orderVOItems)) {
      if (WarehouseEnum.HAI_NING_DIRECT_SALES.getCode()
              .equals(orderVOItems.get(0).getWarehouseInCode()) && (
              StrUtil.equals(orderVOItems.get(0).getWarehouseOutCode(),
                      WarehouseEnum.FINISHED_PRODUCTS.getCode()) || StrUtil.equals(
                      orderVOItems.get(0).getWarehouseOutCode(),
                      WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode()))) {
        head.setZzftc("X");
      }
    }
    ArrayList<MM_076Param.ITEMDTO> items = new ArrayList<>();
    for (TransferOrderItemVO itemVO : orderVOItems) {
      MM_076Param.ITEMDTO item = new MM_076Param.ITEMDTO();
      item.setMatnr(itemVO.getProductCode());
      item.setMenge(NumberUtil.toBigDecimal(itemVO.getNum()).stripTrailingZeros().toPlainString());
      item.setMeins(itemVO.getUnitCode());
      item.setWerks(transferOrderVO.getGroupCode());
      item.setCharg(itemVO.getBatchNo());
      item.setLgort(itemVO.getWarehouseOutCode());
      item.setUmlgo(itemVO.getWarehouseInCode());
      item.setLifnr(transferOrderVO.getSupplierCode());
      item.setSgtxt(itemVO.getRemark());
      item.setSobkz(sobkz);
      items.add(item);
    }
    head.setItem(items);
    return head;
  }

  /**
   * buildMM_077Param
   * @param detail
   * @return
   */
  public MM_077Param buildMM_077Param(AsmDisOrderVO detail) {
    if (detail == null || detail.getFp() == null || CollUtil.isEmpty(detail.getSub())) {
      throw new CheckException("组装拆卸单详情VO不能为空");
    }
    AsmDisOrderItemVO fp = detail.getFp();
    List<AsmDisOrderItemVO> sub = detail.getSub();
    MM_077Param head = new MM_077Param();
    String auart =
        detail.getType().equals(AsmDisOrderType.ASSEMBLY_ORDER.getCode()) ? "ZP98" : "ZP99";
    boolean isDisassemblyOrder = detail.getType().equals(AsmDisOrderType.DISASSEMBLY_ORDER.getCode());
    // 设置头部信息
    head.setAufnr(detail.getCode()); // 订单编号
    head.setAuart(auart); // 订单类型
    head.setGltrs(TimeStampUtil.convertTimestampToFormat(detail.getCreateTime())); // 创建时间
    head.setGstrp(TimeStampUtil.convertTimestampToFormat(detail.getStartTime())); // 开始时间
    head.setGltrp(TimeStampUtil.convertTimestampToFormat(detail.getEndTime())); // 结束时间
    head.setMatnr(fp.getProductCode()); // 物料编号
    head.setMatxt(fp.getProductName()); // 物料描述
    head.setMenge(NumberUtil.toBigDecimal(fp.getNum()).stripTrailingZeros().toPlainString()); // 数量
    head.setMeins(fp.getUnitCode()); // 单位
    head.setWerks(detail.getGroupCode()); // 工厂编码
    head.setLgort(fp.getWarehouseCode()); // 仓库编码
    head.setUsnam(detail.getCreateManName()); // 创建人姓名
    head.setZzvbeln(fp.getSaleOrderNo()); // 销售订单号
    head.setZzposnr(fp.getSaleOrderProductRowId()); // 销售订单产品行项目ID
    head.setScddzdbz(detail.getRemark()); //备注
    List<String> purchaseApplyCodeList = StrUtil.splitTrim(fp.getPurchaseApplyForOrderCode(), "-");
    if (!isDisassemblyOrder && purchaseApplyCodeList.size() == 2) {
      head.setBanfn(purchaseApplyCodeList.get(0)); //采购申请单号
      head.setBnfpo(purchaseApplyCodeList.get(1)); //采购申请行号
    }
    // 初始化明细列表
    List<MM_077Param.ITEMDTO> items = new ArrayList<>();
    //拆卸单需要在明细里加成品，成品行传1，子件明细从2开始
    //组装单不需要加成品明细，子件明细从1开始
    if (isDisassemblyOrder) {
      // 添加成品信息到明细列表
      MM_077Param.ITEMDTO item = new MM_077Param.ITEMDTO();
      //序号-行项目号（成品行传1，子件明细从2开始）
      item.setPosnr("1"); // 行项目号，成品行传1
      item.setMatnr(fp.getProductCode()); // 物料编号
      item.setMenge(NumberUtil.toBigDecimal(fp.getNum())); // 数量
      item.setMeins(fp.getUnitCode()); // 单位
      item.setWerks(detail.getGroupCode()); // 工厂编码
      item.setLgort(fp.getWarehouseCode()); // 仓库编码
      item.setCharg(fp.getBatchNo()); // 批次号
      item.setZzcxcp("拆卸成品");
      item.setZcbdj1(NumberUtil.mul(fp.getOriginNetPrice(),fp.getNum())); // 未税单价
      item.setZjsdj1(NumberUtil.toBigDecimal(fp.getPrice())); // 含税单价
      item.setScddhbz(fp.getRemark()); // 备注
      items.add(item);
      // 循环添加子件信息到明细列表
      int index = 2;// 子件从2开始编号
      for (AsmDisOrderItemVO disOrderItem : sub) {
        MM_077Param.ITEMDTO subItem = new MM_077Param.ITEMDTO();
        subItem.setPosnr(String.valueOf(index));
        subItem.setMatnr(disOrderItem.getProductCode());
        subItem.setMenge(NumberUtil.toBigDecimal(disOrderItem.getNum()));
        subItem.setMeins(disOrderItem.getUnitCode());
        subItem.setWerks(detail.getGroupCode());
        subItem.setLgort(disOrderItem.getWarehouseCode());
        subItem.setCharg(disOrderItem.getBatchNo());
        subItem.setZcbdj1(NumberUtil.mul(fp.getOriginNetPrice(),fp.getNum())); // 成品未税单价
        subItem.setZjsdj1(NumberUtil.toBigDecimal(fp.getPrice())); // 成品含税单价
        subItem.setZcbdj(NumberUtil.mul(disOrderItem.getOriginNetPrice(),disOrderItem.getNum()));
        subItem.setZjsdj(NumberUtil.toBigDecimal(disOrderItem.getPrice()));
        subItem.setScddhbz(disOrderItem.getRemark());
        List<String> subPurchaseApplyCodeList = StrUtil.splitTrim(disOrderItem.getPurchaseApplyForOrderCode(), "-");
        if (subPurchaseApplyCodeList.size() == 2) {
          subItem.setBanfn(subPurchaseApplyCodeList.get(0)); //采购申请单号
          subItem.setBnfpo(subPurchaseApplyCodeList.get(1)); //采购申请行号
        }
        items.add(subItem);
        index++;
      }
      head.setItem(items);
    } else {
      int index = 1;// 子件从1开始编号
      for (AsmDisOrderItemVO disOrderItem : sub) {
        MM_077Param.ITEMDTO subItem = new MM_077Param.ITEMDTO();
        subItem.setPosnr(String.valueOf(index));
        subItem.setMatnr(disOrderItem.getProductCode());
        subItem.setMenge(NumberUtil.toBigDecimal(disOrderItem.getNum()));
        subItem.setMeins(disOrderItem.getUnitCode());
        subItem.setWerks(detail.getGroupCode());
        subItem.setLgort(disOrderItem.getWarehouseCode());
        subItem.setCharg(disOrderItem.getBatchNo());
        subItem.setZzcxcp(StrUtil.EMPTY);
        subItem.setZcbdj1(NumberUtil.mul(fp.getOriginNetPrice(),fp.getNum())); // 成品未税单价
        subItem.setZjsdj1(NumberUtil.toBigDecimal(fp.getPrice())); // 成品含税单价
        subItem.setZcbdj(NumberUtil.mul(disOrderItem.getOriginNetPrice(),disOrderItem.getNum()));
        subItem.setZjsdj(NumberUtil.toBigDecimal(disOrderItem.getPrice()));
        subItem.setScddhbz(disOrderItem.getRemark());
        items.add(subItem);
        index++;
      }
      head.setItem(items);
    }
    return head;
  }

  /**
   * buildMM_078Param
   * @param asmDisOrder
   * @param isDelete 删除或作废
   * @return
   */
  public MM_078Param buildMM_078Param(AsmDisOrder asmDisOrder,boolean isDelete) {
    MM_078Param head = new MM_078Param();
    head.setAufnr(asmDisOrder.getCode());
    String zspjg = asmDisOrder.getStatus().equals(AsmDisOrderStatus.FINISHED.getCode())
        || asmDisOrder.getStatus().equals(AsmDisOrderStatus.WAITING_WAREHOUSE.getCode())
        ? Constants_Sap.SUCCESS_TYPE
        : asmDisOrder.getStatus().equals(AsmDisOrderStatus.REJECT.getCode())
            ? Constants_Sap.ERROR_TYPE : StrUtil.EMPTY;
    head.setZspjg(zspjg);
    head.setZspsm(asmDisOrder.getReviewReason());
    //X表示需要审批或者作废
    head.setZfbs(isDelete ? Constants_Sap.CONFIRM_IDENTIFICATION : StrUtil.EMPTY);
    return head;
  }

  /**
   * buildMM_079Param
   *
   * @param detail 组装拆卸单
   * @param hasWMS 是否涉及wms
   * @param type 操作类型（A，发出；B，收货）
   * @param time 过账时间 优先使用传入的时间 -> 仓库执行时间 -> 当前时间
   */
  public MM_079Param buildMM_079Param(AsmDisOrderVO detail, boolean hasWMS, String type, Long time) {
    if (detail == null || detail.getFp() == null || CollUtil.isEmpty(detail.getSub())) {
      throw new CheckException("组装拆卸单详情VO不能为空");
    }
    AsmDisOrderItemVO fp = detail.getFp();
    List<AsmDisOrderItemVO> sub = detail.getSub();
    MM_079Param mm079Param = new MM_079Param();
    HEADDTO head = new HEADDTO();
    // 设置头部信息
    boolean isAssemblyOrder = detail.getType().equals(AsmDisOrderType.ASSEMBLY_ORDER.getCode());
    head.setAufnr(detail.getCode());
    head.setBldat(TimeStampUtil.convertTimestampToFormat(detail.getCreateTime()));
    /*head.setBudat(TimeStampUtil.convertTimestampToFormat(
            ObjectUtil.defaultIfNull(detail.getReviewTime(), System.currentTimeMillis())));*/
    head.setAuart(isAssemblyOrder ? "ZP98" : "ZP99");
    head.setMatnr(fp.getProductCode());
    head.setMenge(NumberUtil.toBigDecimal(fp.getNum()).stripTrailingZeros().toPlainString());
    head.setMeins(fp.getUnitCode());
    head.setWerks(detail.getGroupCode());
    head.setLgort(fp.getWarehouseCode());
    head.setCharg(fp.getBatchNo());
    head.setZczlx(hasWMS ? type : StrUtil.EMPTY);
    List<MM_079Param.ITEMDTO> items = new ArrayList<>();
    // 根据是否有WMS以及订单类型（组装或拆卸）设置明细项
    if (hasWMS) {
      if (!isAssemblyOrder) {
        //涉及WMS,拆卸单:A传成品明细,B传子件明细
        if (StrUtil.equals(ConstantWms.MM_056_OPERATE_TYPE_SEND,type)) {
          head.setBudat(TimeStampUtil.convertTimestampToFormat(ObjectUtil.defaultIfNull(time,
              detail.getWarehouseTimeFp())));
          addItemFromProduct(fp, items, detail.getGroupCode());
        } else if (StrUtil.equals(ConstantWms.MM_056_OPERATE_TYPE_RECEIVE,type)) {
          head.setBudat(TimeStampUtil.convertTimestampToFormat(ObjectUtil.defaultIfNull(time,
              detail.getWarehouseTimeSub())));
          for (AsmDisOrderItemVO orderItemVO : sub) {
            addItemFromProduct(orderItemVO, items, detail.getGroupCode());
          }
        }
      } else {
        //涉及WMS,组装单:A传子件明细,B传成品明细
        if (StrUtil.equals(ConstantWms.MM_056_OPERATE_TYPE_SEND,type)) {
          head.setBudat(TimeStampUtil.convertTimestampToFormat(ObjectUtil.defaultIfNull(time,
              detail.getWarehouseTimeSub())));
          for (AsmDisOrderItemVO orderItemVO : sub) {
            addItemFromProduct(orderItemVO, items, detail.getGroupCode());
          }
        } else if (StrUtil.equals(ConstantWms.MM_056_OPERATE_TYPE_RECEIVE,type)) {
          head.setBudat(TimeStampUtil.convertTimestampToFormat(ObjectUtil.defaultIfNull(time,
              detail.getWarehouseTimeFp())));
          addItemFromProduct(fp, items, detail.getGroupCode());
        }
      }
    } else {
      if (!isAssemblyOrder) {
        addItemFromProduct(fp, items, detail.getGroupCode());
      }
      for (AsmDisOrderItemVO orderItemVO : sub) {
        addItemFromProduct(orderItemVO, items, detail.getGroupCode());
      }
    }
    if (StrUtil.isBlank(head.getBudat())) {
      head.setBudat(TimeStampUtil.convertTimestampToFormat(ObjectUtil.defaultIfNull(time,
          System.currentTimeMillis())));
    }
    mm079Param.setHead(head);
    mm079Param.setItem(items);
    return mm079Param;
  }

  private void addItemFromProduct(AsmDisOrderItemVO product, List<MM_079Param.ITEMDTO> items, String groupCode) {
    MM_079Param.ITEMDTO item = new MM_079Param.ITEMDTO();
    item.setMatnr(product.getProductCode());
    item.setMenge(NumberUtil.toBigDecimal(product.getNum()).stripTrailingZeros().toPlainString());
    item.setMeins(product.getUnitCode());
    item.setWerks(groupCode);
    item.setLgort(product.getWarehouseCode());
    item.setCharg(product.getBatchNo());
    items.add(item);
  }

  /**
   * 取消的导致已完成 021
   */
  public UpdatePurchaseOrderSapParam createMM021ParamForOrderCancel(Order order, OrderCancel orderCancel) {
    if (!OrderErpTypeEnum.SAP_ERP.getDescription().equals(order.getErpType())) {
      throw new CheckException("非SAP订单不处理");
    }
    if (StrUtil.isBlank(order.getErpOrderNo())) {
      throw new CheckException("ERP 采购单号不能为空");
    }
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO data = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO head = new UpdatePurchaseOrderHEADDTO();
    head.setEbeln(order.getErpOrderNo());
    head.setBsart(PurchaseApplicationTypeEnum.STANDARD.getCode());
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.getEnumByCode(order.getTitleOfTheContract());
    if (titleOfTheContractEnum == null) {
      throw new CheckException("采购订单签约抬头错误");
    }
    head.setEkorg(titleOfTheContractEnum.getCode());
    //如果签约抬头是总部传递001
    String ekgrp = "001";
    if (titleOfTheContractEnum == TitleOfTheContractEnum.TITLE_OF_WAN_JU) {
      //如果签约抬头是万聚传递251
      ekgrp = "251";
    }
    head.setEkgrp(ekgrp);
    head.setBukrs(titleOfTheContractEnum.getCode());
    head.setZttwb(order.getOrderRemark());
    head.setBedat(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
    Supplier supplier = order.getSupplier();
    String mdmCode = supplier.getMdmCode();
    head.setLifnr(mdmCode);
    head.setLifn2(mdmCode);
    head.setCountry("CN");
    head.setZterm("0001");
    head.setWaers("CNY");
    head.setZsp("X");
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(
            order.getType(), order.getSupplierId(), Constants.STATE_OK);
    String dockingPurchaseErpCode = supplierPerformance.getDockingPurchaseErpCode();
    if (StrUtil.isBlank(dockingPurchaseErpCode)) {
      throw new CheckException("对接采购 ERP 编码未配置");
    }
    User user = userRepository.findFirstByCodeAndState(dockingPurchaseErpCode, Constants.STATE_OK)
        .orElse(null);
    if (user == null) {
      throw new CheckException("未找到采购:" + dockingPurchaseErpCode );
    }
    head.setBednr("XHGJ00" + user.getCode());
    head.setAfnam(user.getRealName());
    List<OrderDetail> orderDetails =
        orderDetailRepository.findAllByOrderIdAndState(order.getId(), Constants.STATE_OK);
    ArrayList<ITEMDTO> items = new ArrayList<>();
    List<OrderCancelDetail> orderCancelDetailList =
        orderCancelDetailRepository.findAllByCancelIdAndState(orderCancel.getId(),
            Constants.STATE_OK);
    Map<String, OrderCancelDetail> id2OrderCancelDetail =
        orderCancelDetailList.stream().collect(Collectors.toMap(OrderCancelDetail::getOrderDetailId,
            Function.identity(), (k1, k2) -> k1));
    for (OrderDetail orderDetail : orderDetails) {
      BigDecimal actualNum = NumberUtil.sub(orderDetail.getNum(), orderDetail.getCancelNum(), orderDetail.getReturnNum());
      if (actualNum.compareTo(BigDecimal.ZERO) < 1) {
        OrderCancelDetail orderCancelDetail = id2OrderCancelDetail.get(orderDetail.getId());
        if (orderCancelDetail == null) {
          continue;
        }
        BigDecimal cancelNum = orderCancelDetail.getNum();
        // 全部取消 -> 只传单号、行号、ELIKZ、ZQXSL传当前取消数量
        ITEMDTO item = new ITEMDTO();
        item.setEbelp(orderDetail.getPurchaseOrderRowNo());
        item.setCharX(order.getErpOrderNo());
        item.setZZPOSNR(orderDetail.getRowNo());
        item.setElikz("X");
        item.setZqxsl(cancelNum.stripTrailingZeros().toPlainString());
        items.add(item);
      } else {
        // 部分取消 -> 传基本数据、数量传 订货数量-取消数量
        ITEMDTO item = new ITEMDTO();
        item.setEbelp(orderDetail.getPurchaseOrderRowNo());
        String productUnitCode = mpmServiceExt.getProductUnitCode(orderDetail.getInnerCode());
        item.setMatnr(orderDetail.getInnerCode());
        item.setMeins(productUnitCode);
        item.setMenge(NumberUtil.sub(orderDetail.getNum(), orderDetail.getCancelNum()).stripTrailingZeros().toPlainString());
        item.setWerks(titleOfTheContractEnum.getCode());
        item.setLgort("6000");
        item.setAplfz(DateUtil.today());
        String rateCode =
            Constants.TAX_RATE_TYPE_NUM.get(new BigDecimal(orderDetail.getCostPriceTaxRate()));
        item.setMwskz(rateCode);
        Pair<BigDecimal, Integer> convertSapPrice =
            SAPToolUtils.convertSapPrice(orderDetail.getPrice(), 2);
        item.setNetpr(convertSapPrice.getKey().toPlainString());
        item.setPeinh(convertSapPrice.getValue().toString());
        item.setBprme(productUnitCode);
        BigDecimal mul = NumberUtil.mul(NumberUtil.div(orderDetail.getPrice(),
                NumberUtil.add(BigDecimal.ONE,
                    new BigDecimal(Optional.ofNullable(orderDetail.getCostPriceTaxRate()).orElse("0"))))
            ,NumberUtil.add(BigDecimal.ONE,
                new BigDecimal(Optional.ofNullable(orderDetail.getSalesPriceTaxRate()).orElse("0")))).multiply(new BigDecimal("1.02"));
        BigDecimal zjsj = NumberUtil.round(mul, 2, RoundingMode.UP);
        if (titleOfTheContractEnum == TitleOfTheContractEnum.TITLE_OF_HEADQUARTERS) {
          //如果订单签约抬头是咸享国际科技股份有限公司，结算价传值调整为和净值一致
          item.setZjsj(item.getNetpr());
        }else {
          item.setZjsj(zjsj.stripTrailingZeros().toPlainString());
        }
        item.setCharX(order.getErpOrderNo());
        item.setPstyp(StrUtil.EMPTY);
        item.setKnttp(StrUtil.EMPTY);
        item.setZZVBELN(order.getSaleOrderNo());
        item.setZZPOSNR(orderDetail.getRowNo());
        items.add(item);
      }
    }
    head.setItem(items);
    data.setHead(head);
    param.setData(data);
    return param;
  }

  /**
   * 创建MM034参数  不含落地商付款单
   * @param paymentApplyRecord
   * @param user
   * @return
   */
  public AdvanceApplyParam createMM034Param(PaymentApplyRecord paymentApplyRecord, User user) {
    List<PaymentApplyDetail> paymentApplyDetails =
        paymentApplyDetailRepository.findByPaymentApplyRecordId(paymentApplyRecord.getId());
    List<String> financialVoucherIds =
        paymentApplyDetails.stream().map(PaymentApplyDetail::getFinancialVouchersId)
            .collect(Collectors.toList());
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.findAllById(financialVoucherIds);
    Set<String> supplierIds =
        financialVouchers.stream().map(FinancialVoucher::getSupplierId).collect(Collectors.toSet());
    Supplier supplier =
        supplierRepository.findAllById(supplierIds).stream().findFirst().orElse(null);
    if (supplier == null) {
      throw new CheckException("没有找到供应商信息");
    }
    List<String> codes =
        financialVouchers.stream().map(FinancialVoucher::getPurchaseOrderNo).distinct()
            .collect(Collectors.toList());
    codes.add("-1");
    List<SupplierOrder> supplierOrders =
        supplierOrderRepository.findAllByCodeInAndState(codes, Constants.STATE_OK);
    if (StrUtil.equals(paymentApplyRecord.getApplyType(),
        PaymentApplyTypeEnums.ADVANCE_REVERSAL.getKey())) {
      // 预付冲销
      return SapAdvanceApplyRequest.buildAdvanceApplyParamByAdvanceReversal(paymentApplyRecord,
          paymentApplyDetails, user, supplier, financialVouchers, supplierOrders);
    } else if (StrUtil.equals(paymentApplyRecord.getApplyType(),
        PaymentApplyTypeEnums.ADVANCE.getKey())) {
      // 预付
      BigDecimal allApplyAdvancePrice =
          paymentApplyDetails.stream().map(PaymentApplyDetail::getApplyAdvancePrice)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      return buildMM034AdvanceParam(paymentApplyRecord, paymentApplyDetails, allApplyAdvancePrice,
          supplierOrders.get(0), user);
    } else if (StrUtil.equalsAny(paymentApplyRecord.getApplyType(),
        PaymentApplyTypeEnums.DRAW.getKey(), PaymentApplyTypeEnums.RETURN.getKey())) {
      // 提款 or 退款
      return buildMM034DrawOrReturnParam(paymentApplyRecord, user, financialVouchers, supplier,
          paymentApplyDetails);
    } else {
      throw new CheckException("付款申请类型异常");
    }
  }

  private AdvanceApplyParam buildMM034DrawOrReturnParam(PaymentApplyRecord applyRecord, User user,
      List<FinancialVoucher> financialVouchers,Supplier supplier,
      List<PaymentApplyDetail> paymentApplyDetails) {
    List<AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    BigDecimal sumItemPrice = BigDecimal.ZERO;
    FinancialVoucher lastOne = CollUtil.get(financialVouchers, -1);
    String currentDate =
        com.xhgj.srm.common.utils.DateUtil.getCurrentDate(DatePattern.PURE_DATE_PATTERN);
    int i = 1;
    for (FinancialVoucher financialVoucher : financialVouchers) {
      PaymentApplyDetail paymentApplyDetail = getByFinancialVouchersId(paymentApplyDetails, financialVoucher.getId());
      if (paymentApplyDetail == null) {
        continue;
      }
      String invoiceOrderNo = financialVoucher.getInvoiceOrderNo();
      //一次供应商提款备注为：【发票链接：{付款申请此行对应进项票的下载地址}】+{填写的备注内容}
      String remark = buildProvisionalRemark(invoiceOrderNo, financialVoucher.getSupplierId(),
          paymentApplyDetail.getRemark(), paymentApplyDetail.getVoucherType());
      DATADTO.HEADDTO.ITEMDTO itemdto = buildAdvanceApplyParam(financialVoucher, currentDate,
          paymentApplyDetail,remark);
      itemdto.setZzmfksqhxmh(String.valueOf(i++));
      itemdtoList.add(itemdto);
      String zzmsfje = itemdto.getZzmsfje();
      if (StrUtil.isNotBlank(zzmsfje)) {
        sumItemPrice = NumberUtil.add(sumItemPrice,new BigDecimal(zzmsfje));
      }
    }
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    AdvanceApplyParam.DATADTO.HEADDTO headdto = new AdvanceApplyParam.DATADTO.HEADDTO();
    headdto.setSrmid(applyRecord.getPaymentApplyNo().substring(4,14));
    headdto.setBukrs(lastOne.getGroupCode());
    if (StrUtil.equals(applyRecord.getApplyType(),
        PaymentApplyTypeEnums.DRAW.getKey())) {
      headdto.setZzmfklx("2");
    } else if (StrUtil.equals(applyRecord.getApplyType(),
        PaymentApplyTypeEnums.RETURN.getKey())) {
      headdto.setZzmfklx("3");
    }
    headdto.setLifnr(StrUtil.emptyIfNull(supplier.getMdmCode()));
    headdto.setZzmsqrq(currentDate);
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(applyRecord.getApplyMan());
    headdto.setItem(itemdtoList);
    headdto.setZzmfksqzje(sumItemPrice.toPlainString());
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  private PaymentApplyDetail getByFinancialVouchersId(List<PaymentApplyDetail> paymentApplyDetails,
      String id) {
    for (PaymentApplyDetail paymentApplyDetail : paymentApplyDetails) {
      if (CollUtil.contains(paymentApplyDetail.getFinancialVouchersIdList(), id)) {
        return paymentApplyDetail;
      }
    }
    return null;
  }

  private DATADTO.HEADDTO.ITEMDTO buildAdvanceApplyParam(FinancialVoucher financialVoucher,
      String currentDate, PaymentApplyDetail paymentApplyDetail, String remark) {
    //构建sap参数
    AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO itemdto =
        new AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO();
    itemdto.setEbeln(financialVoucher.getPurchaseOrderNo());
    itemdto.setZzmsfje(financialVoucher.getRelatedAmount().stripTrailingZeros().toPlainString());
    if (Boolean.TRUE.equals(financialVoucher.getInitialOrder())) {
      itemdto.setBrtwr(financialVoucher.getOrderAmount().toPlainString());
    } else {
      SupplierOrder supplierOrder =
          supplierOrderRepository.findFirstByCodeAndStateIn(financialVoucher.getPurchaseOrderNo(),
              CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED));
      itemdto.setBrtwr(supplierOrder.getPrice().toPlainString());
      itemdto.setBednr(supplierOrder.getPurchaseMan());
      itemdto.setEknam(supplierOrder.getPurchaseDept());
    }
    itemdto.setZfbdt(currentDate);
    itemdto.setZbd1t("0");
    itemdto.setZzmqwfkrq(currentDate);
    PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromKey(paymentApplyDetail.getPayType());
    if (payTypeSAPEnums == null) {
      payTypeSAPEnums = PayTypeSAPEnums.fromKey(financialVoucher.getPaymentType());
    }
    boolean otherPayment = payTypeSAPEnums == PayTypeSAPEnums.OHTER;
    itemdto.setZlsch(payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : "");
    itemdto.setText2(payTypeSAPEnums != null ? otherPayment ? paymentApplyDetail.getPaymentDesc() :
        payTypeSAPEnums.getName() : "");
    itemdto.setZzmmxbz(remark);
    itemdto.setBankl(paymentApplyDetail.getBankCode());
    itemdto.setBankn(paymentApplyDetail.getBankAccount());
    itemdto.setKoinh(paymentApplyDetail.getAccountName());
    itemdto.setPurchaseAccount(paymentApplyDetail.getPurchaseAccount());
    itemdto.setOrderNo(paymentApplyDetail.getOrderNo());
    itemdto.setOrderLink(paymentApplyDetail.getOrderLink());
    itemdto.setProductName(paymentApplyDetail.getProductName());
    return itemdto;
  }

  private AdvanceApplyParam buildMM034AdvanceParam(PaymentApplyRecord applyRecord,
      List<PaymentApplyDetail> paymentApplyDetails, BigDecimal allApplyAdvancePrice,
      SupplierOrder purchaseOrder, User user) {
    //调用MDM的SAP接口 预付款申请
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    AdvanceApplyParam.DATADTO.HEADDTO headdto = new AdvanceApplyParam.DATADTO.HEADDTO();
    headdto.setSrmid(applyRecord.getPaymentApplyNo().substring(4, 14));
    headdto.setBukrs(purchaseOrder.getGroupCode());
    //预付款默认传2
    headdto.setZzmfklx("1");
    Supplier supplier = supplierRepository.findById(purchaseOrder.getSupplierId()).orElseThrow(
        () -> new CheckException(
            "供应商信息有误,订单：【" + purchaseOrder.getCode() + "】，请核实！"));
    headdto.setLifnr(supplier != null ? supplier.getMdmCode() : "");
    //当前时间 格式 20240101
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(applyRecord.getApplyMan());
    // 预付款支付币种由明细调整放在抬头
    headdto.setWaers(purchaseOrder.getMoneyCode());
    List<AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    int i = 1;
    for (PaymentApplyDetail detail : paymentApplyDetails) {
      String purchaseOrderNuber = detail.getSupplierOrderNo();
      SupplierOrder supplierOrder =
          supplierOrderRepository.findFirstByCodeAndState(purchaseOrderNuber, Constants.STATE_OK);
      AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO itemdto =
          new AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO();
      itemdto.setZzmfksqhxmh(String.valueOf(i));
      //        itemdto.setWaers(purchaseOrder.getMoneyCode());
      itemdto.setEbeln(supplierOrder.getCode());
      itemdto.setZzmsfje(detail.getApplyAdvancePrice().stripTrailingZeros().toPlainString());
      itemdto.setBrtwr(supplierOrder.getPrice().toPlainString());
      itemdto.setZfbdt(nowDate);
      itemdto.setZbd1t(VoucherAccountPeriodEnum.getByDesc(detail.getAdvancePeriod()) != null
          ? VoucherAccountPeriodEnum.getByDesc(detail.getAdvancePeriod()).getDays().toString()
          : "");
      itemdto.setZzmqwfkrq(nowDate);
      PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(detail.getPayType());
      if (payTypeSAPEnums == null) {
        payTypeSAPEnums = PayTypeSAPEnums.fromKey(detail.getPayType());
      }
      boolean otherPayment = payTypeSAPEnums == PayTypeSAPEnums.OHTER;
      itemdto.setZlsch(payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : "");
      itemdto.setText2(payTypeSAPEnums != null ? otherPayment ? detail.getPaymentDesc():
          payTypeSAPEnums.getName() : "");
      //付款状态 默认传4
      itemdto.setZzmfkzt("4");
      itemdto.setBednr(supplierOrder.getPurchaseMan());
      itemdto.setEknam(supplierOrder.getPurchaseDept());
      itemdto.setZzmmxbz(detail.getRemark());
      itemdto.setBankl(detail.getBankCode());
      itemdto.setBankn(detail.getBankAccount());
      itemdto.setKoinh(detail.getAccountName());
      itemdto.setPurchaseAccount(detail.getPurchaseAccount());
      itemdto.setOrderLink(detail.getOrderLink());
      itemdto.setOrderNo(detail.getOrderNo());
      itemdto.setProductName(detail.getProductName());
      itemdtoList.add(itemdto);
      i++;
    }
    headdto.setItem(itemdtoList);
    headdto.setZzmfksqzje(allApplyAdvancePrice.stripTrailingZeros().toPlainString());
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }


  private String buildProvisionalRemark(String invoiceOrderNo, String supplierId,
      String remark,String financialType) {
    // 如果financialType不是指定类型，直接返回备注
    if (!StrUtil.equals(Constants.STATE_NO, financialType) || StrUtil.isBlank(supplierId)) {
      return remark;
    }
    // 如果供应商不存在或类型不符合，直接返回备注
    Supplier supplier = supplierRepository.findById(supplierId).orElse(null);
    if (supplier == null || !StrUtil.equals(Constants.SUPPLIER_TYPE_PROVISIONAL, supplier.getSupType())) {
      return remark;
    }
    InputInvoiceOrder inputInvoiceOrder = orderInvoiceRelationRepository.findFirstByInvoiceNumsAndState(invoiceOrderNo, Constants.STATE_OK);
    if (inputInvoiceOrder == null) {
      return remark;
    }
    List<String> fileUrlList = new ArrayList<>();
    List<OrderSupplierInvoice> orderSupplierInvoiceList =
        shareInputInvoiceService.getOrderSupplierInvoiceDistinct(inputInvoiceOrder.getId());
    for (OrderSupplierInvoice orderSupplierInvoice : orderSupplierInvoiceList) {
      File file =
          fileRepository.findFirstByRelationIdAndRelationTypeAndState(orderSupplierInvoice.getId(),
              Constants.FILE_TYPE_INVOICE, Constants.STATE_OK).orElse(null);
      if (file != null && StrUtil.isNotBlank(file.getUrl())) {
        String fileUrl = ossUtil.buildOssFileUrl(file.getUrl());
        fileUrlList.add(fileUrl);
      }
    }
    StringBuilder resultMark = new StringBuilder();
    fileUrlList.forEach(fileUrl -> resultMark.append("【发票链接：").append(fileUrl).append("】"));
    return StrUtil.isBlank(resultMark.toString()) ? remark : resultMark.append("、").append(remark).toString();
  }

}
