package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.dto.purchase.order.SupplierOrder2DetailTemp;
import com.xhgj.srm.jpa.dto.purchase.order.SupplierOrder2Details;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository.SupplierOrder2DetailProjection;
import com.xhgj.srm.service.ShareSupplierOrderDetailService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/5 19:53
 */
@Service
public class ShareSupplierOrderDetailServiceImpl implements ShareSupplierOrderDetailService {

  @Autowired private SupplierOrderDetailRepository repository;
  @Autowired private SupplierOrderDetailDao dao;

  @Override
  public BootBaseRepository<SupplierOrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId) {
    return repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
        orderToFormId, Constants.STATE_OK);
  }

  @Override
  public List<String> getAllIds() {
    return dao.findAllIds();
  }

  @Override
  public List<SupplierOrder2Details> getDetailsByOrderIds(List<String> orderIds) {
    if (CollUtil.isEmpty(orderIds)) {
      return new ArrayList<>();
    }
    List<SupplierOrder2DetailProjection> projections =
        repository.getDetailsByOrderIds2(orderIds, SupplierOrderFormType.DETAILED.getType());
    List<SupplierOrder2DetailTemp> supplierOrder2DetailTemps = projections.stream().map(
        proj -> new SupplierOrder2DetailTemp(proj.getSupplierOrder(),
            proj.getSupplierOrderDetail())).collect(Collectors.toList());
    // supplierOrder2DetailTemps根据相同的supplierOrder分组
    Map<SupplierOrder, List<SupplierOrderDetail>> supplierOrderListMap = supplierOrder2DetailTemps.stream()
        .collect(Collectors.groupingBy(SupplierOrder2DetailTemp::getSupplierOrder,
            Collectors.mapping(SupplierOrder2DetailTemp::getSupplierOrderDetail,
                Collectors.toList())));
    // 转换为List<SupplierOrder2Details>
    return supplierOrderListMap.entrySet().stream()
        .map(entry -> new SupplierOrder2Details(entry.getKey(), entry.getValue()))
        .collect(Collectors.toList());
  }
}
