package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/5 20:51
 */
public interface ShareUserToGroupService extends BootBaseService<UserToGroup, String> {

  /**
   * 根据用户 id 和组织 id 的获得用户和组织关系对象
   *
   * @param userId 用户 id
   * @param groupId 组织 id
   */
  UserToGroup getUserToGroupByUserIdAndGroupId(String userId, String groupId);

  /**
   * 根据用户id获取组织部门列表 @Author: liuyq @Date: 2022/7/8 15:23
   *
   * @param userId 用户id 必传
   * @return java.util.List<java.lang.String>
   */
  List<UserToGroup> getUserToGroupList(String userId);

  List<String> getDeptErpCodeListByUser(String userId);
  /**
   * 获取部门id列表内的用户id集合 @Author: liuyq @Date: 2022/7/8 16:57
   *
   * @param groupIds 组织或部门id集合 必传且不能为空
   * @param param 群组类型 必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserIdListByDepartIds(List<String> groupIds, String param);

}
