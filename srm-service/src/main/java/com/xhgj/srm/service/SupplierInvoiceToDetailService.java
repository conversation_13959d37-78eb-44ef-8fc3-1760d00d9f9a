package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Map;

public interface SupplierInvoiceToDetailService
    extends BootBaseService<SupplierInvoiceToDetail, String> {

  @Deprecated
  Map<SupplierOrder, List<SupplierInvoiceToDetail>> getInvoiceToDetailsGroupedByOrder(String inputInvoiceOrderId);

  /**
   * 根据发票号获取与明细的关联数据
   *
   * @param inputInvoiceOrderId 发票号
   */
  List<SupplierInvoiceToDetail> getAllByInputInvoiceOrderId(String inputInvoiceOrderId);

  /**
   * 根据进项票id删除与明细关联的数据
   *
   * @param inputInvoiceOrderId 发票号
   */
  void deleteByInputOrderId(String inputInvoiceOrderId);
}
