package com.xhgj.srm.service.impl;/**
 * @since 2025/3/4 10:09
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderStatus;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.TimeStampUtil;
import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderBatchNoCleanerAndGenerator;
import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderCleanerAndGenerator;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderListVO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderApplyLinkDTO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderExportForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderFillDTO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderImportForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSaveForm;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSearchForm;
import com.xhgj.srm.factory.AsmDisOrderFactory;
import com.xhgj.srm.factory.SapFactory;
import com.xhgj.srm.factory.WmsFactory;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.AsmDisOrderDao;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.jpa.entity.AsmDisOrderItem;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.AsmDisOrderItemRepository;
import com.xhgj.srm.jpa.repository.AsmDisOrderRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.ConstantWms;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Result.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturn;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturn.Data;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturnResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSAsmDisOrderReturnResult.ReturnData;
import com.xhgj.srm.request.service.third.erp.sap.dto.WMSSyncAsmDisOrderParam;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Param;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.service.third.wms.WMSService;
import com.xhgj.srm.service.AsmDisOrderService;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * 组装拆卸单服务实现
 */
@Service
@Slf4j
public class AsmDisOrderServiceImpl implements AsmDisOrderService {

  @Resource
  private AsmDisOrderFactory asmDisOrderFactory;
  @Resource
  private AsmDisOrderRepository asmDisOrderRepository;
  @Resource
  private AsmDisOrderItemRepository asmDisOrderItemRepository;
  @Resource
  private SharePermissionTypeService sharePermissionTypeService;
  @Resource
  private AsmDisOrderDao asmDisOrderDao;
  @Resource
  private UserRepository userRepository;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;
  private static final String LOCK_FOR_PURCHASE_APPLY_FOR_ORDER = "srm:lock_for_purchase_apply_for_order:{}";
  private static final int LOCK_WAIT_TIME = 30; // 等待锁的最长时间（秒）
  private static final int LOCK_LEASE_TIME = 120; // 锁自动释放时间（秒）
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  private HZeroService hZeroService;
  @Resource
  private ExportUtil exportUtil;
  @Resource
  private SAPService sapService;
  @Resource
  private MissionDispatcher missionDispatcher;
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private MissionUtil missionUtil;
  @Resource
  private SapFactory sapFactory;
  @Resource
  private WmsFactory wmsFactory;
  @Resource
  private WMSService wmsService;

  @Override
  public String save(AsmDisOrderSaveForm form) {
    AsmDisOrderServiceImpl proxy = applicationContext.getBean(this.getClass());
    Set<String> lockIds = new HashSet<>();
    if (form.getFp().getPurchaseApplyForOrderId() != null) {
      lockIds.add(form.getFp().getPurchaseApplyForOrderId());
    }
    if (CollUtil.isNotEmpty(form.getSub())) {
      form.getSub().forEach(item -> {
        if (item.getPurchaseApplyForOrderId() != null) {
          lockIds.add(item.getPurchaseApplyForOrderId());
        }
      });
    }
    if (StrUtil.isNotBlank(form.getId())) {
      asmDisOrderItemRepository.findByAsmDisIdAndState(form.getId(), Constants.STATE_OK)
          .forEach(item -> {
            if (item.getPurchaseApplyForOrderId() != null) {
              lockIds.add(item.getPurchaseApplyForOrderId());
            }
          });
      return proxy.saveTransactional(form);
    }
    List<RLock> locks = lockPurchaseApplyForOrder(lockIds);
    try {
      return proxy.saveTransactional(form);
    } finally {
      unlockAllLocks(locks);
    }
  }

  private List<RLock> lockPurchaseApplyForOrder(Set<String> lockIds) {
    if (CollUtil.isEmpty(lockIds)) {
      return Collections.emptyList();
    }
    // 将 Set 转换为 List，并按自然顺序排序，确保所有线程加锁顺序一致
    List<String> sortedLockIds = new ArrayList<>(lockIds);
    Collections.sort(sortedLockIds);

    List<RLock> locks = new ArrayList<>();
    try {
      for (String lockId : sortedLockIds) {
        RLock lock = redissonClient.getLock(StrUtil.format(LOCK_FOR_PURCHASE_APPLY_FOR_ORDER, lockId));
        boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
        if (!locked) {
          throw new RuntimeException("获取锁失败，lockId: " + lockId);
        }
        locks.add(lock);
      }
      return locks;
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      unlockAllLocks(locks); // 释放已获得的锁
      throw new RuntimeException("加锁过程被中断", e);
    } catch (Exception e) {
      unlockAllLocks(locks); // 异常时释放锁
      throw e;
    }
  }

  private void unlockAllLocks(List<RLock> locks) {
    if (CollUtil.isNotEmpty(locks)) {
      locks.forEach(lock -> {
        try {
          if (lock.isHeldByCurrentThread()) {
            lock.unlock();
          }
        } catch (Exception e) {
          // 记录日志，避免解锁异常影响主流程
          log.error("解锁失败", e);
        }
      });
    }
  }

  /**
   * 保存更新采购申请单
   */
  private void savePurchaseApplyForOrder(Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap) {
    if (CollUtil.isNotEmpty(purchaseApplyForOrderMap.values())) {
      Collection<PurchaseApplyForOrder> values = purchaseApplyForOrderMap.values();
      purchaseApplyForOrderRepository.saveAll(values);
    }
  }

  public static void main(String[] args) {
    AtomicReference<String> b = new AtomicReference<>("aaa");
    test(b);
    System.out.println(b);
  }

  public static void test(AtomicReference<String> a) {
    a.set("bbb");
  }


  @Transactional
  public String saveTransactional(AsmDisOrderSaveForm form) {
    AsmDisOrder asmDisOrder;
    try {
      // ------------------ 校验 ------------------
      // #check 校验子件仓库和成品仓库是否一致
      asmDisOrderFactory.checkWarehouse(form);
      // #check 校验直销库时 必填项
      asmDisOrderFactory.checkDirectSale(form);
      // #check 校验成品与子件不能出现相同的物料编码，注释
//      asmDisOrderFactory.checkProductCode(form);
      // ------------------ 保存 ------------------
      Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap = new HashMap<>();
      // 主表保存
      asmDisOrder = asmDisOrderFactory.createAsmDisOrder(form);
      asmDisOrderRepository.saveAndFlush(asmDisOrder);
      // 释放原有采购申请
      asmDisOrderItemRepository.findByAsmDisIdAndState(asmDisOrder.getId(), Constants.STATE_OK)
          .forEach(item -> asmDisOrderFactory.releasePurchaseApplyForOrder(
              item.getAsmDisType(),
              item.getNum(),
              item.getPurchaseApplyForOrderId(),
              item.getType(),
              purchaseApplyForOrderMap));
      // 保存成品明细
      List<AsmDisOrderItem> fpList = asmDisOrderFactory.createAsmDisOrderItemFp(form, asmDisOrder, purchaseApplyForOrderMap);
      asmDisOrderItemRepository.saveAll(fpList);
      asmDisOrderItemRepository.flush();
      AsmDisOrderItem fp = fpList.stream().filter(item -> !Constants.STATE_DELETE.equals(item.getState()))
          .findFirst().orElse(null);
      // 保存子件明细
      List<AsmDisOrderItem> sub = asmDisOrderFactory.createAsmDisOrderItemSub(form, asmDisOrder, purchaseApplyForOrderMap);
      // 过滤删除
      sub =
          sub.stream().filter(item -> !Constants.STATE_DELETE.equals(item.getState())).collect(Collectors.toList());
      asmDisOrderItemRepository.saveAll(sub);
      asmDisOrderItemRepository.flush();
      // 关联采购申请数量更新
      this.savePurchaseApplyForOrder(purchaseApplyForOrderMap);
      // ------------------ 调用三方 ------------------
      if (AsmDisOrderStatus.AUDITING.getCode().equals(asmDisOrder.getStatus())) {
        // 调用sap相关同步接口
        AsmDisOrderVO detail = getDetail(asmDisOrder.getId());
        sapService.sapAsmDisOrderUpdate(sapFactory.buildMM_077Param(detail));
        // sap成功后调用飞搭
        StartProcessVo startProcessVo = hZeroService.startAsmDisOrderProcess(asmDisOrder, fp, sub);
        asmDisOrder.setReviewId(startProcessVo.getInstanceId());
        asmDisOrderRepository.saveAndFlush(asmDisOrder);
      }
    } catch (Exception e) {
      AsmDisOrderCleanerAndGenerator.INSTANCE.rollbackOrderNumber(form.getType());
      throw e;
    }finally {
      AsmDisOrderCleanerAndGenerator.clear();
    }
    return asmDisOrder.getId();
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_ASSEMBLY_DISASSEMBLY_ORDER)
  public PageResult<AsmDisOrderListVO> getPage(@Valid AsmDisOrderSearchForm form) {
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(new SearchPermission(),
            new OperatorPermission());
    Map<String, Object> searchMap = form.toQueryMap(mergeUserPermission);
    Page<AsmDisOrder> page = asmDisOrderDao.getPage(searchMap);
    List<AsmDisOrder> content = page.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResult.empty((Integer) searchMap.get("pageNo"), (Integer) searchMap.get("pageSize"));
    }
    List<AsmDisOrderListVO> asmDisOrderListVOS = asmDisOrderFactory.buildListVos(content);
    return new PageResult<>(asmDisOrderListVOS, page.getTotalElements(), page.getTotalPages(),
        (Integer) searchMap.get("pageNo"), (Integer) searchMap.get("pageSize"));
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_ASSEMBLY_DISASSEMBLY_ORDER)
  public void export(AsmDisOrderExportForm form) {
    String userId = form.getUserId();
    User user =
        userRepository.findById(userId).orElseThrow(() -> new CheckException("用户不存在"));
    OperatorPermission operatorPermission =
        sharePermissionTypeService.getOperatorPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_EXPORT_ASSEMBLY_DISASSEMBLY);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(new SearchPermission(), operatorPermission);
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        MissionTypeEnum.BATCH_TASK_EXPORT_ASSEMBLY_DISASSEMBLY.getTypeName(),
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_ASSEMBLY_DISASSEMBLY);
  }

  @Override
  public AsmDisOrderVO getDetail(String id) {
    AsmDisOrder asmDisOrder = asmDisOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
    if (Constants.STATE_DELETE.equals(asmDisOrder.getState())) {
      throw new CheckException("组装拆卸单已删除");
    }
    List<AsmDisOrderItem> asmDisOrderItems =
        asmDisOrderItemRepository.findByAsmDisIdAndState(id, Constants.STATE_OK);
    return asmDisOrderFactory.buildVO(asmDisOrder, asmDisOrderItems);
  }

  @Override
  public void delete(String id, Boolean sap) {
    if (StrUtil.isBlank(id)) {
      return;
    }
    AsmDisOrderServiceImpl proxy = applicationContext.getBean(this.getClass());
    Set<String> lockIds = new HashSet<>();
    asmDisOrderItemRepository.findByAsmDisIdAndState(id, Constants.STATE_OK)
        .forEach(item -> {
          if (item.getPurchaseApplyForOrderId() != null) {
            lockIds.add(item.getPurchaseApplyForOrderId());
          }
        });
    List<RLock> locks = lockPurchaseApplyForOrder(lockIds);
    try {
      proxy.deleteTransactional(id, sap);
    } finally {
      unlockAllLocks(locks);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void deleteTransactional(String id, Boolean sap) {
    AsmDisOrder asmDisOrder = asmDisOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
    // 判断只有暂存和驳回状态的才能删除
    if (!AsmDisOrderStatus.TEMPORARY.getCode().equals(asmDisOrder.getStatus())
        && !AsmDisOrderStatus.REJECT.getCode().equals(asmDisOrder.getStatus())) {
      throw new CheckException("只有暂存和驳回状态的才能删除");
    }
    asmDisOrder.setState(Constants.STATE_DELETE);
    asmDisOrder.setUpdateTime(System.currentTimeMillis());
    asmDisOrderRepository.saveAndFlush(asmDisOrder);
    // 释放采购申请 + 删除明细
    Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap = new HashMap<>();
    List<AsmDisOrderItem> asmDisOrderItems = asmDisOrderItemRepository.findByAsmDisIdAndState(id, Constants.STATE_OK);
    asmDisOrderItems.forEach(item -> {
      item.setState(Constants.STATE_DELETE);
      item.setUpdateTime(System.currentTimeMillis());
      asmDisOrderFactory.releasePurchaseApplyForOrder(asmDisOrder.getType(), item.getNum(),
          item.getPurchaseApplyForOrderId(), item.getType(), purchaseApplyForOrderMap);
    });
    asmDisOrderItemRepository.saveAll(asmDisOrderItems);
    // 关联采购申请数量更新
    this.savePurchaseApplyForOrder(purchaseApplyForOrderMap);
    // 如何存在审核id 则调用sap
    if (StrUtil.isNotBlank(asmDisOrder.getReviewId()) || Boolean.TRUE.equals(sap)) {
      // 调用sap相关删除同步接口
      MM_078Param mm078Param = sapFactory.buildMM_078Param(asmDisOrder, true);
      sapService.sapAsmDisOrderChangeState(mm078Param);
    }
  }

  @Override
  public void invalid(String id) {
    if (StrUtil.isBlank(id)) {
      return;
    }
    AsmDisOrderServiceImpl proxy = applicationContext.getBean(this.getClass());
    Set<String> lockIds = new HashSet<>();
    asmDisOrderItemRepository.findByAsmDisIdAndState(id, Constants.STATE_OK)
        .forEach(item -> {
          if (item.getPurchaseApplyForOrderId() != null) {
            lockIds.add(item.getPurchaseApplyForOrderId());
          }
        });
    List<RLock> locks = lockPurchaseApplyForOrder(lockIds);
    try {
      proxy.invalidTransactional(id);
    } finally {
      unlockAllLocks(locks);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void invalidTransactional(String id) {
    AsmDisOrder asmDisOrder = asmDisOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
    // 判断只有待仓库状态的才能作废
    if (!AsmDisOrderStatus.WAITING_WAREHOUSE.getCode().equals(asmDisOrder.getStatus())) {
      throw new CheckException("只有待仓库执行的才能作废");
    }
    // 订单有仓库执行人和仓库执行时间的数据不允许作废
    if (StrUtil.isNotBlank(asmDisOrder.getWarehouseOperatorFp())
        || asmDisOrder.getWarehouseTimeFp() != null
        || StrUtil.isNotBlank(asmDisOrder.getWarehouseOperatorSub())
        || asmDisOrder.getWarehouseTimeSub() != null
    ) {
      throw new CheckException("订单有仓库执行人和仓库执行时间的数据不允许作废");
    }
    asmDisOrder.setState(Constants.STATE_DELETE);
    asmDisOrder.setUpdateTime(System.currentTimeMillis());
    asmDisOrderRepository.saveAndFlush(asmDisOrder);
    List<AsmDisOrderItem> asmDisOrderItems = asmDisOrderItemRepository.findByAsmDisIdAndState(id,
        Constants.STATE_OK);
    // 释放采购申请 + 删除明细
    Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap = new HashMap<>();
    asmDisOrderItems.forEach(item -> {
      item.setState(Constants.STATE_DELETE);
      item.setUpdateTime(System.currentTimeMillis());
      asmDisOrderFactory.releasePurchaseApplyForOrder(asmDisOrder.getType(), item.getNum(),
          item.getPurchaseApplyForOrderId(), item.getType(), purchaseApplyForOrderMap);
    });
    asmDisOrderItemRepository.saveAll(asmDisOrderItems);
    // 关联采购申请数量更新
    this.savePurchaseApplyForOrder(purchaseApplyForOrderMap);
    // 调用sap相关废弃同步接口
    MM_078Param mm078Param = sapFactory.buildMM_078Param(asmDisOrder, true);
    sapService.sapAsmDisOrderChangeState(mm078Param);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void auditCallBack(ApprovalResult approvalResult, byte status) {
    try {
      String instanceId = approvalResult.getProcessInstanceId();
      String jobNumber = approvalResult.getStaffId();
      String remark = approvalResult.getRemark();
      long finishTime = approvalResult.getFinishTime();
      String reviewerName = userRepository.findFirstByCodeAndState(jobNumber, Constants.STATE_OK)
          .map(User::getRealName)
          .orElse(StrUtil.EMPTY);
      AsmDisOrder asmDisOrder =
          asmDisOrderRepository.findByReviewIdAndState(instanceId, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
      if (Boolean.TRUE.equals(asmDisOrder.hasMovement()) && AsmDisOrderStatus.FINISHED.getCode() == status) {
        // 如果设计货物移动且审核通过，则状态修改为待仓库执行
        status = AsmDisOrderStatus.WAITING_WAREHOUSE.getCode();
      }
      asmDisOrder.setReviewerCode(jobNumber);
      asmDisOrder.setReviewer(reviewerName);
      asmDisOrder.setReviewReason(remark);
      asmDisOrder.setReviewTime(finishTime);
      asmDisOrder.setStatus(status);
      asmDisOrder.setUpdateTime(System.currentTimeMillis());
      asmDisOrderRepository.saveAndFlush(asmDisOrder);
      // 调用078接口同步审核信息
      MM_078Param mm078Param = sapFactory.buildMM_078Param(asmDisOrder, false);
      // todo 078可能的异常 + 状态处理
      MM_078Result mm078Result = sapService.sapAsmDisOrderChangeState(mm078Param);
      if (AsmDisOrderStatus.WAITING_WAREHOUSE.getCode() == status) {
        //  调用wms MM_055接口传递单据信息
        WMSSyncAsmDisOrderParam wmsParam =
            wmsFactory.buildWMSSyncAsmDisOrderParam(getDetail(asmDisOrder.getId()));
        wmsService.wmsSyncAsmDisOrderParam(wmsParam);
        // todo 可能的异常 + 状态处理
      }
      if (AsmDisOrderStatus.FINISHED.getCode() == status) {
        // 批次信息生成
        List<AsmDisOrderItem> asmDisOrderItems = asmDisOrderFactory.batchGenerateBatchNoAndUpdate(asmDisOrder);
        asmDisOrderItemRepository.saveAll(asmDisOrderItems);
        asmDisOrderItemRepository.flush();
        //不执行wms，直接去调sap079
        MM_079Param mm079Param =
            sapFactory.buildMM_079Param(getDetail(asmDisOrder.getId()), false, "", null);
        MM_079Result mm079Result = sapService.sapAsmDisOrderInfoPosting(mm079Param);
        List<ITEMDTO> mm079ResultItem = mm079Result.getItem();
        boolean isAssemblyOrder = asmDisOrder.getType().equals(AsmDisOrderType.ASSEMBLY_ORDER.getCode());
        //A:组装单的子件，拆卸单的成品 B:拆卸单的子件，组装单的成品
        for (ITEMDTO itemdto : mm079ResultItem) {
          process079ResultItem(itemdto, isAssemblyOrder, asmDisOrder);
        }
        asmDisOrder.setUpdateTime(System.currentTimeMillis());
        asmDisOrder.setStatus(AsmDisOrderStatus.FINISHED.getCode());
        asmDisOrderRepository.saveAndFlush(asmDisOrder);
      }
    } catch (Exception e) {
      log.error("审核回调异常", e);
      AsmDisOrderBatchNoCleanerAndGenerator.INSTANCE.rollbackOrderNumber();
      AsmDisOrderBatchNoCleanerAndGenerator.clear();
      throw e;
    }
  }

  public void process079ResultItem(ITEMDTO itemdto, boolean isAssemblyOrder, AsmDisOrder asmDisOrder) {
    //A:组装单的子件，拆卸单的成品 B:拆卸单的子件，组装单的成品
    if (StrUtil.equals(itemdto.getZczlx(), ConstantWms.MM_056_OPERATE_TYPE_SEND)) {
      if (isAssemblyOrder) {//组装单的子件
        asmDisOrder.setProductVoucherSub(itemdto.getBelnr());
        asmDisOrder.setProductVoucherYearSub(itemdto.getGjahr());
      }else {//拆卸单的成品
        asmDisOrder.setProductVoucherFp(itemdto.getBelnr());
        asmDisOrder.setProductVoucherYearFp(itemdto.getGjahr());
      }
    } else if (StrUtil.equals(itemdto.getZczlx(), ConstantWms.MM_056_OPERATE_TYPE_RECEIVE)) {
      if (isAssemblyOrder) {//组装单的成品
        asmDisOrder.setProductVoucherFp(itemdto.getBelnr());
        asmDisOrder.setProductVoucherYearFp(itemdto.getGjahr());
      }else {//拆卸单的子件
        asmDisOrder.setProductVoucherSub(itemdto.getBelnr());
        asmDisOrder.setProductVoucherYearSub(itemdto.getGjahr());
      }
    }
  }

  @SneakyThrows
  @Override
  public byte[] downloadDetail(List<AsmDisOrderFillDTO> fillDTOList) {
    XSSFWorkbook workbook = new XSSFWorkbook();
    List<Integer> widths = new ArrayList<>();
    int size = Constants_Excel.DOWNLOAD_ASSEMBLY_DISASSEMBLY_ORDER_INFO.size();
    for (int i = 0; i < size; i++) {
      widths.add(30);
    }
    Sheet sheet = exportUtil.createSheet(workbook, "product", widths);
    CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
    CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
    Row titleRow = sheet.createRow(0);
    exportUtil.createTitle(Constants_Excel.DOWNLOAD_ASSEMBLY_DISASSEMBLY_ORDER_INFO, titleStyle,titleRow);
    fillDTOList = CollUtil.emptyIfNull(fillDTOList);
    for (int i = 0; i < fillDTOList.size(); i++) {
      Row row = sheet.createRow(i + 1);
      AsmDisOrderFillDTO dto = fillDTOList.get(i);
      int index = 0;
      exportUtil.createCell(row, index++, dto.getRowId(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductCode(), baseStyle);
      exportUtil.createCell(row, index++, dto.getBrand(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductName(), baseStyle);
      exportUtil.createCell(row, index++, dto.getDesc(), baseStyle);
      exportUtil.createCell(row, index++, dto.getModel(), baseStyle);
      exportUtil.createCell(row, index++, dto.getUnit(), baseStyle);
      // 税率
      exportUtil.createCell(row, index++, dto.getTax(), baseStyle);
      // 未税单价
      exportUtil.createCell(row, index++, dto.getOriginNetPrice(), baseStyle);
      // 数量 保留三位小数
      BigDecimal num = dto.getNum();
      String numStr = num == null ? "" : num.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, numStr, baseStyle);
      // 含税结算单价 保留2位小数
      BigDecimal price = dto.getPrice();
      String priceStr = price == null ? "" : price.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, priceStr, baseStyle);
      // 仓库编码
      exportUtil.createCell(row, index++, dto.getWarehouseCode(), baseStyle);
      exportUtil.createCell(row, index++, dto.getBatchNo(), baseStyle);
      exportUtil.createCell(row, index++, dto.getRemark(), baseStyle);
    }
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    workbook.write(byteArrayOutputStream);
    return byteArrayOutputStream.toByteArray();
  }

  @Override
  public List<AsmDisOrderFillDTO> fill(AsmDisOrderImportForm form) throws IOException {
    MultipartFile file = form.getFile();
    if (file == null || file.isEmpty()) {
      throw new CheckException("文件不能为空");
    }
    List<AsmDisOrderFillDTO> res = new ArrayList<>();
    try (Workbook workbook = exportUtil.buildByFile(StrUtil.emptyIfNull(file.getOriginalFilename()),
        file.getInputStream());) {
      Sheet sheet = workbook.getSheetAt(0);
      if (!exportUtil.validateExcel(sheet, 0, Constants_Excel.DOWNLOAD_ASSEMBLY_DISASSEMBLY_ORDER_INFO)) {
        throw new CheckException("文件异常，请导入指定模板。");
      }
      int rowNumCount = sheet.getPhysicalNumberOfRows();
      if (rowNumCount > 1) {
        for (int i = 0; i < rowNumCount; i++) {
          Row row = sheet.getRow(i + 1);
          if (exportUtil.isEmptyRow(row)) {
            continue;
          }
          boolean fail = false;
          AsmDisOrderFillDTO dto = new AsmDisOrderFillDTO();
          int index = 0;
          try {
            String rowId = exportUtil.getCellStringValue(row.getCell(index++));
            String productCode = exportUtil.getCellStringValue(row.getCell(index++));
            String brand = exportUtil.getCellStringValue(row.getCell(index++));
            String productName = exportUtil.getCellStringValue(row.getCell(index++));
            String desc = exportUtil.getCellStringValue(row.getCell(index++));
            String model = exportUtil.getCellStringValue(row.getCell(index++));
            String unit = exportUtil.getCellStringValue(row.getCell(index++));
            String tax = exportUtil.getCellStringValue(row.getCell(index++));
            // 未税单价
            String originNetPriceStr = exportUtil.getCellStringValue(row.getCell(index++));
            // 数量
            String numStr = exportUtil.getCellStringValue(row.getCell(index++));
            // 含税单价
            String priceStr = exportUtil.getCellStringValue(row.getCell(index++));
            // 仓库编码
            String warehouseCode = exportUtil.getCellStringValue(row.getCell(index++));
            String batchNo = exportUtil.getCellStringValue(row.getCell(index++));
            String remark = exportUtil.getCellStringValue(row.getCell(index++));
            // 报错文案：导入完成！【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("【").append(rowId).append("】")
                .append("【").append(productCode).append("】");
            // 数量最大填写三位小数
            BigDecimal num = Convert.toBigDecimal(numStr);
            if (num != null && num.scale() > 3) {
              errorMsg.append(", 数量最大填写三位小数");
              fail = true;
            }
            // 含税结算单价最大填写两位小数
            BigDecimal price = Convert.toBigDecimal(priceStr);
            if (price != null && price.scale() > 2) {
              errorMsg.append(", 含税结算单价最大填写两位小数");
              fail = true;
            }
            // 备注最大只支持50字
            if (StrUtil.isNotBlank(remark) && remark.length() > 50) {
              errorMsg.append(", 备注最大只支持50字");
              fail = true;
            }
            // 库位查询
            InventoryLocation warehouse =
                inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                    form.getUserGroup(), warehouseCode, Constants.STATE_OK).orElse(null);
            if (warehouse == null) {
              errorMsg.append(", 库房不存在");
              fail = true;
            }
            if (fail) {
              dto.setErrorMsg(errorMsg.toString());
            }
            dto.setRowId(rowId);
            dto.setProductCode(productCode);
            dto.setBrand(brand);
            dto.setProductName(productName);
            dto.setDesc(desc);
            dto.setModel(model);
            dto.setUnit(unit);
            dto.setTax(tax);
            dto.setOriginNetPrice(Convert.toBigDecimal(originNetPriceStr));
            dto.setNum(num);
            dto.setPrice(price);
            dto.setWarehouseCode(warehouseCode);
            dto.setWarehouseId(warehouse == null ? null : warehouse.getId());
            dto.setBatchNo(batchNo);
            dto.setRemark(remark);
            res.add(dto);
          } catch (Exception e) {
            log.error("第" + (i + 1) + "行数据异常", e);
            throw new CheckException("第" + (i + 1) + "行数据异常：" + e.getMessage());
          }
        }
      }
      return res;
    }
  }

  @Override
  public WMSAsmDisOrderReturnResult wmsCallback(WMSAsmDisOrderReturn param) {
    WMSAsmDisOrderReturnResult res = new WMSAsmDisOrderReturnResult();
    List<WMSAsmDisOrderReturnResult.ReturnData> returnDataList = new ArrayList<>();
    List<Data> dataList = param.getDataList();
    for (Data data : dataList) {
      // 部分失败不影响其他
      try {
        ReturnData returnData = wmsFactory.handlerWmsData(data);
        returnDataList.add(returnData);
        String traceId = MDC.get("TRACE_ID");
        //sap异步调用
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
          MDC.put("TRACE_ID", traceId);
          // 异步调用调用079更新入库 出库凭证信息
          AsmDisOrder asmDisOrder =
              asmDisOrderRepository.findByCodeAndState(data.getHead().getOrderNumber(),
                  Constants.STATE_OK).orElseThrow(
                  () -> CheckException.noFindException(AsmDisOrder.class,
                      data.getHead().getOrderNumber()));
          sapProcess(asmDisOrder.getId(), data.getHead().getOperationType(), null);
        });
      } catch (Exception e) {
        log.error(data.getHead().getOrderNumber()+"wmsCallback error", e);
        // 回滚批次号
        AsmDisOrderBatchNoCleanerAndGenerator.INSTANCE.rollbackOrderNumber();
        ReturnData returnData = new ReturnData();
        returnData.setOrderNumber(data.getHead().getOrderNumber());
        returnData.setInfoType(Constants_Sap.ERROR_TYPE);
        returnData.setMessageText(StrUtil.maxLength(e.getMessage(), 220));
        returnDataList.add(returnData);
      } finally {
        AsmDisOrderBatchNoCleanerAndGenerator.clear();
      }
    }
    res.setReturnData(returnDataList);
    return res;
  }

  @Override
  public void syncSap079(String id, Long time) {
    AsmDisOrderVO detail = this.getDetail(id);
    AsmDisOrder asmDisOrder = asmDisOrderRepository.findById(detail.getId()).get();
    // 0.判断成品与子件是否sap物料号和物料凭证已经有数据
    // 成品是否有sap物料号和物料凭证数据
    boolean fpDone =
        StrUtil.isAllNotBlank(detail.getProductVoucherYearFp(), detail.getProductVoucherFp());
    boolean subDone =
        StrUtil.isAllNotBlank(detail.getProductVoucherYearSub(), detail.getProductVoucherSub());
    boolean allDone = fpDone && subDone;
    if (Boolean.TRUE.equals(allDone)) {
      throw new CheckException("成品与子件已经有sap物料号和物料凭证数据");
    }
    // 判断是否涉及wms
    Boolean movement = detail.getHasMovement();
    if (Boolean.TRUE.equals(movement)) {
      // 1.判断是否有仓库执行人和仓库执行时间的数据  都未完成
      boolean fpWmsDone = StrUtil.isNotBlank(detail.getWarehouseOperatorFp()) && detail.getWarehouseTimeFp() != null;
      boolean subWmsDone = StrUtil.isNotBlank(detail.getWarehouseOperatorSub()) && detail.getWarehouseTimeSub() != null;
      if (!fpWmsDone && !subWmsDone) {
        throw new CheckException("请先等仓库执行完成");
      }
      if (Boolean.FALSE.equals(fpDone) && Boolean.FALSE.equals(subDone)) {
        // 如果两条都无
        // 先调用操作类型为A的后调用操作类型为B的
        this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_SEND,time);
        this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_RECEIVE, time);
      } else if (Boolean.FALSE.equals(fpDone)) {
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == detail.getType()) {
          // 组装单 + 成品传B类型
          this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_RECEIVE, time);
        }
        if (AsmDisOrderType.DISASSEMBLY_ORDER.getCode() == detail.getType()) {
          // 拆卸单 + 成品传A类型
          this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_SEND, time);
        }
      } else if (Boolean.FALSE.equals(subDone)) {
        if (AsmDisOrderType.ASSEMBLY_ORDER.getCode() == detail.getType()) {
          // 组装单 + 子件传A类型
          this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_SEND, time);
        }
        if (AsmDisOrderType.DISASSEMBLY_ORDER.getCode() == detail.getType()) {
          // 拆卸单 + 子件传B类型
          this.sapProcess(id, ConstantWms.MM_056_OPERATE_TYPE_RECEIVE, time);
        }
      }
    } else {
      // 不涉及则直接调用
      // 调用079更新入库 出库凭证信息
      if (ObjectUtil.isNull(time)) {
        throw new CheckException("过账日期必填");
      }
      MM_079Param mm079Param =
          sapFactory.buildMM_079Param(detail, false, "",time);
      MM_079Result mm079Result = sapService.sapAsmDisOrderInfoPosting(mm079Param);
      List<ITEMDTO> mm079ResultItem = mm079Result.getItem();
      boolean isAssemblyOrder = asmDisOrder.getType().equals(AsmDisOrderType.ASSEMBLY_ORDER.getCode());
      for (ITEMDTO itemdto : mm079ResultItem) {
        process079ResultItem(itemdto, isAssemblyOrder, asmDisOrder);
      }
    }
    asmDisOrder.setUpdateTime(System.currentTimeMillis());
    asmDisOrder.setStatus(AsmDisOrderStatus.FINISHED.getCode());
    asmDisOrderRepository.saveAndFlush(asmDisOrder);
  }

  @Override
  public void sapProcess(String id, String wmsOperationType, Long time) {
    //  079更新入库 出库凭证信息
    AsmDisOrderVO detail = this.getDetail(id);
    MM_079Param mm079Param =
        sapFactory.buildMM_079Param(detail, true, wmsOperationType, time);
    MM_079Result mm079Result = sapService.sapAsmDisOrderInfoPosting(mm079Param);
    List<ITEMDTO> mm079ResultItem = mm079Result.getItem();
    AsmDisOrder asmDisOrder = asmDisOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("组装拆卸单不存在"));
    boolean isAssemblyOrder = asmDisOrder.getType().equals(AsmDisOrderType.ASSEMBLY_ORDER.getCode());
    //A:组装单的子件，拆卸单的成品 B:拆卸单的子件，组装单的成品
    for (ITEMDTO itemdto : mm079ResultItem) {
      process079ResultItem(itemdto, isAssemblyOrder, asmDisOrder);
    }
    if (StrUtil.isAllNotBlank(asmDisOrder.getProductVoucherFp(),
        asmDisOrder.getProductVoucherYearFp(), asmDisOrder.getProductVoucherSub(),
        asmDisOrder.getProductVoucherYearSub())) {
      asmDisOrder.setStatus(AsmDisOrderStatus.FINISHED.getCode());
    }
    asmDisOrder.setUpdateTime(System.currentTimeMillis());
    asmDisOrderRepository.saveAndFlush(asmDisOrder);
  }

  @Override
  public List<AsmDisOrderApplyLinkDTO> getPurchaseApplyLink(List<String> purchaseApplyForOrderIds) {
    List<AsmDisOrderApplyLinkDTO> res = new ArrayList<>();
    if (CollUtil.isEmpty(purchaseApplyForOrderIds)) {
      return res;
    }
    // 根据purchaseApplyForOrderIds查询组装拆卸单明细
    List<AsmDisOrderItem> asmDisOrderItems = asmDisOrderItemRepository.findByPurchaseApplyForOrderIdInAndState(purchaseApplyForOrderIds, Constants.STATE_OK);
    Map<String, List<AsmDisOrderItem>> asmDisOrderItemMap = asmDisOrderItems.stream().collect(Collectors.groupingBy(AsmDisOrderItem::getPurchaseApplyForOrderId));
    if (CollUtil.isEmpty(asmDisOrderItems)) {
      return res;
    }
    List<String> asmOrderIds =
        asmDisOrderItems.stream().map(AsmDisOrderItem::getAsmDisId).distinct()
        .collect(Collectors.toList());
    // 根据asmOrderIds查询组装拆卸单
    List<AsmDisOrder> asmDisOrders = asmDisOrderRepository.findAllById(asmOrderIds);
    Map<String, AsmDisOrder> asmDisOrderMap =
        asmDisOrders.stream().collect(Collectors.toMap(AsmDisOrder::getId, Function.identity(), (k1, k2) -> k1));
    // 组装结果
    for (String purchaseApplyForOrderId : purchaseApplyForOrderIds) {
      List<AsmDisOrderItem> findItems = asmDisOrderItemMap.get(purchaseApplyForOrderId);
      if (CollUtil.isEmpty(findItems)) {
        continue;
      }
      for (AsmDisOrderItem findItem : findItems) {
        AsmDisOrder asmDisOrder = asmDisOrderMap.get(findItem.getAsmDisId());
        AsmDisOrderApplyLinkDTO dto = new AsmDisOrderApplyLinkDTO();
        dto.setPurchaseApplyForOrderId(purchaseApplyForOrderId);
        dto.setId(asmDisOrder.getId());
        dto.setCode(asmDisOrder.getCode());
        dto.setType(asmDisOrder.getType());
        dto.setStatus(asmDisOrder.getStatus());
        dto.setCreateMan(asmDisOrder.getCreateMan());
        dto.setCreateManCode(asmDisOrder.getCreateManCode());
        dto.setCreateManName(asmDisOrder.getCreateManName());
        res.add(dto);
      }
    }
    return res;
  }
}

