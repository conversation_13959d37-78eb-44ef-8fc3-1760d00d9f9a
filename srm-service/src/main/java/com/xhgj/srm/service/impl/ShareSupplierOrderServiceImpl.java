package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.OutBoundDeliveryPrams;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.domain.PurchaseOrderInvoiceRelation;
import com.xhgj.srm.dto.PurchaseOrderReturnDTO;
import com.xhgj.srm.dto.WarehouseWarrantDTO;
import com.xhgj.srm.dto.WarehouseWarrantParams;
import com.xhgj.srm.jpa.dao.OrderInvoiceRelationDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDao;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.service.ShareSupplierOrderService;
import com.xhgj.srm.service.ShareSupplierOrderToFormService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ShareSupplierOrderServiceImpl implements ShareSupplierOrderService {

  @Autowired private SupplierOrderRepository repository;
  @Autowired private SupplierOrderDao dao;
  @Autowired private SharePermissionTypeService permissionTypeService;
  @Autowired private SearchSchemeDao searchSchemeDao;
  @Autowired private ShareSupplierOrderToFormService supplierOrderToFormService;
  @Autowired private OrderInvoiceRelationDao orderInvoiceRelationDao;

  @Override
  public BootBaseRepository<SupplierOrder, String> getRepository() {
    return repository;
  }

  @Override
  public List<String> getAllIds(String purchaseId, String supplierId) {
    return dao.findIdList(
        purchaseId,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        supplierId);
  }

  @Override
  public SupplierOrder getByCode(String code) {
    Assert.notBlank(code);
    return repository.findFirstByCodeAndState(code, Constants.STATE_OK);
  }
}
