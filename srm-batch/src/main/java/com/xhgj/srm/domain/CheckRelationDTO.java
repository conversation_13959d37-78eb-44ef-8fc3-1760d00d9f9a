package com.xhgj.srm.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class CheckRelationDTO {

    @ApiModelProperty(value = "供应商等级",required = true)
    @NotEmpty(message = "审核关系供应商等级不能为空")
    private String level;
    @ApiModelProperty(value = "审核类型(1-新增,2修改,3拉黑)",required = true)
    @NotEmpty(message = "审核关系类型不能为空")
    private String type;
    @ApiModelProperty(value = "审核erp编码",required = true)
    @NotEmpty(message = "审核关系erp编码不能为空")
    private String erpCode;
}
