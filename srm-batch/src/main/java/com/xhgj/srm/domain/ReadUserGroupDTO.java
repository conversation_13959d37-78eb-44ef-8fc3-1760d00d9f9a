package com.xhgj.srm.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName ReadUserGroupDTO */
@ApiModel("导入用户-分配组织部门对象")
@Data
@NoArgsConstructor
public class ReadUserGroupDTO {

  @ApiModelProperty("SRM组织id")
  private String groupId;

  @ApiModelProperty("SRM部门id")
  private String deptId;

  public ReadUserGroupDTO(String groupId, String deptId) {
    this.groupId = groupId;
    this.deptId = deptId;
  }
}
