package com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn;

import com.xhgj.srm.api.dto.scheme.BaseSchemeSearchDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> @ClassName SupplierOrderSchemeDTO */
@Data
public class SupplierOrderReturnSchemeDTO extends BaseSchemeSearchDTO {

  @ApiModelProperty("单据类型")
  private String supplierOrderReturnType;

  @ApiModelProperty("单号")
  private String supplierOrderReturnNumber;

  @ApiModelProperty("采购订单号")
  private String supplierOrderReturnCode;

  @ApiModelProperty("采购员")
  private String supplierOrderReturnReceiveMan;

  @ApiModelProperty("采购组织")
  private String supplierOrderReturnGroupName;

  @ApiModelProperty("是否厂直发")
  private Boolean supplierOrderDirectShipment;

  @ApiModelProperty("开始-生成时间")
  private Long supplierOrderReturnStartTime;

  @ApiModelProperty("结束-生成时间")
  private Long supplierOrderReturnEndTime;

  @ApiModelProperty("订单状态")
  private String supplierOrderReturnOrderState;

  @ApiModelProperty("状态")
  private String status;

  @ApiModelProperty("件数")
  private String supplierOrderReturnNum;

  @ApiModelProperty("金额")
  private String supplierOrderReturnPrice;
}
