package com.xhgj.srm.api.dto.supplier.invoice;

import com.xhgj.srm.jpa.entity.Order;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Geng Shy on 2023/8/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderAmount {
  @ApiModelProperty("合计金额")
  private BigDecimal totalAmount;
  @ApiModelProperty("合计税额")
  private BigDecimal totalTaxAmount;
  @ApiModelProperty("价税合计(订单的价税合计应该减去退货金额)")
  private BigDecimal totalAmountIncludingTax;
  private Order order;
  /**
   * 签收凭证状态
   */
  private String orderAcceptState;
}
