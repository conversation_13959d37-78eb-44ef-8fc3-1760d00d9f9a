package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.api.dto.OrderInvoiceFileDTO;
import com.xhgj.srm.common.enums.OrderInvoiceEnums;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR> <PERSON>
 * @date 2023/4/7
 */

@Data
public class BatchOrderInvoiceParams {
  @ApiModelProperty("供应商id")
  @NotBlank
  private String supplierId;
  @ApiModelProperty("发票附件集合")
  private List<OrderInvoiceFileDTO> orderInvoiceFileDTOS;

  @ApiModelProperty("落地商订单id集合")
  @NotEmpty(message = "落地商订单id集合 必传")
  private List<String> orderIds;

  @ApiModelProperty("发票类型")
  @NotNull(message = "发票类型 必传")
  private OrderInvoiceEnums orderInvoiceEnums;

  @ApiModelProperty("发票抬头")
  @NotBlank(message = "发票抬头 必传")
  @Length(max = 140, message = "发票抬头 超长")
  private String title;

  @ApiModelProperty("税号")
  @Pattern(regexp = "^[A-NP-Z0-9]{1,20}", message = "税号格式错误")
  private String taxNumber;

  @ApiModelProperty("开户银行")
  @Length(max = 140, message = "开户银行 超长")
  @NotBlank(message = "开户银行必传")
  private String bankName;

  @ApiModelProperty("银行账号")
  @NotBlank(message = "银行账号 必传")
  @Pattern(regexp = "^\\d{1,20}$", message = "银行账号格式错误")
  private String bankAccount;

  @ApiModelProperty("电话")
  @NotBlank(message = "电话 必传")
  private String mobile;

  @ApiModelProperty("地址")
  @Length(max = 140, message = "地址 超长")
  @NotBlank(message = "地址必传")
  private String address;

  @ApiModelProperty("票面描述（发票备注信息）")
  @Length(max = 500, message = "票面描述 超长")
  @NotBlank(message = "发票备注信息必传")
  private String content;

  @ApiModelProperty("收件人")
  @Length(max = 20, message = "收件人 超长")
//  @NotBlank(message = "收件人 必传")
  private String receiveMan;

  @ApiModelProperty("收件人联系电话")
//  @NotBlank(message = "收件人联系 必传")
  private String receiveMobile;

  @ApiModelProperty("收件人地址")
//  @NotBlank(message = "收件人地址 必传")
  @Length(max = 50, message = "收件人地址 超长")
  private String receiveAddress;

  @ApiModelProperty("其它备注")
  @Length(max = 500, message = "其它备注 超长")
  private String remark;

  @ApiModelProperty("邮箱")
  @NotBlank(message = "邮箱不能为空")
  @Length(max = 254, message = "邮箱 超长")
  private String mail;

}
