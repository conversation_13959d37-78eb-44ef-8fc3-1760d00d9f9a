package com.xhgj.srm.api.dto.account;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> @date 2023/6/28
 */
@Component
@Slf4j
public class OrderAccountDingTalkMessage {

  private final String DING_TALK_CARD_ID = "e3f28fd2-2998-41d4-b156-db9a69593ae4.schema";

  @Resource
  private SrmConfig srmConfig;
  @Resource
  private SupplierPerformanceService supplierPerformanceService;
  @Resource
  private DingUtils dingUtils;
  @Resource
  private BootConfig bootConfig;
  @Resource
  private SharePlatformService platformService;
  /**
   * 发送钉钉消息 -- 新增
   * @param messageParam
   */
  public void sendMessageForAdd(OrderAccountDingTalkMessageParams messageParam) {
    String title = "【{}】新增的对账单";
    sendMessage(messageParam, title);
  }
  /**
   * 发送钉钉消息 -- 驳回的对账单修改提交
   * @param messageParam
   */
  public void sendMessageForUpdate(OrderAccountDingTalkMessageParams messageParam) {
    String title = "【{}】重新提交的对账单";
    sendMessage(messageParam, title);
  }

  /**
   * 发送钉钉消息
   * @param messageParam
   * @param title
   */
  private void sendMessage(OrderAccountDingTalkMessageParams messageParam, String title) {
    String env = bootConfig.getEnv();
    OrderPlatformDTO platformDTO = platformService.findByCode(messageParam.getPlatformCode());
    if (platformDTO == null) {
      throw new CheckException("不存在的平台编码");
    }
    String code = platformDTO.getPlatformCode();
    ArrayList<String> phoneNumbers = new ArrayList<String>(){{
      addAll(srmConfig.getAccountNoticeList());
    }};
    String phoneNumber =
        supplierPerformanceService.getPurchasePhoneNumber(messageParam.getSupplierId(), code);
    if (StrUtil.isNotBlank(phoneNumber)) {
      log.info("新增或修改对账单采购人手机号：{}", phoneNumber);
      if (Objects.equals(env, "prod")) {
        phoneNumbers.add(phoneNumber);
      }
    }
    HashMap<String, Object> map = new HashMap<>();
    map.put("title", StrUtil.format(title, messageParam.getSupplierName()));
    map.put("accountNo", messageParam.getOrderAccountNumber());
    map.put("accountPrice", messageParam.getAmount());
    map.put("orderType", platformDTO.getPlatformName());
    String orderNo = StrUtil.join("、", messageParam.getOrderNumber());
    map.put("orderNo", orderNo);
    if (CollUtil.isEmpty(messageParam.getOrderNumber())) {
      map.put("orderNo", "");
    }
    map.put("detailUrl", srmConfig.getSrmAdminLogin());
    dingUtils.sendDingTaskRobotCar(DING_TALK_CARD_ID, phoneNumbers,
        String.valueOf(System.currentTimeMillis()), map);
  }
}
