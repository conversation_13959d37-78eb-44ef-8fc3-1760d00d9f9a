package com.xhgj.srm.api.service;

import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:52
 */
public interface ExtraFileService extends BootBaseService<ExtraFile, String> {

  /**
   * @return void @Title: addZDYFile @Description 新增自定义附件
   * <AUTHOR> @Date 2019年5月20日 上午11:45:45
   */
  void addZDYFile(String relationid, String filesall);

  /**
   * 将供应商的自定义附件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierZDYFileToSupplierFb(String supplierId, String supplierFbId);

  /**
   * 删除供应商的自定义附件
   *
   * @param supplierId 供应商 id
   * @since 16:51 2019/8/28
   */
  void deleteAllZDYFile(String supplierId);

  void updateSupplierExtraFilesByFb(String supplierId, String fbid);

  void updateSupplierFbExtraFilesByFb(String oldFbId, String fbid);


}
