package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.dto.invoice.InvoiceVerificationInfo;
import com.xhgj.srm.common.enums.InvoiceVerificationResultTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;

/**
 * Created by Geng Shy on 2023/8/16
 */
@Getter
@Builder
public class InvoiceVerificationResult {

  @ApiModelProperty(value = "结果类型")
  private InvoiceVerificationResultTypeEnum resultType;
  @ApiModelProperty(value = "发票信息")
  private InvoiceVerificationInfo invoiceVerificationInfo;

}
