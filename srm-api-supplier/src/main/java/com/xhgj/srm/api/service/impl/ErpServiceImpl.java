package com.xhgj.srm.api.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.api.service.ErpService;
import com.xhgj.srm.request.dto.erp.ReceivableBillDTO;
import com.xhgj.srm.request.dto.erp.ReceivableQueryDTO;
import com.xhgj.srm.request.dto.erp.UpdateLogisitcaDTO;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** <AUTHOR> @ClassName ErpServiceImpl */
@Service
public class ErpServiceImpl implements ErpService {
  @Autowired private ERPRequest request;

  @Override
  public void updateLogistics(UpdateLogisitcaDTO updateLogisitcaDTO) {
    Assert.notNull(updateLogisitcaDTO);
    request.updateLogistics(updateLogisitcaDTO);
  }

  @Override
  public List<ReceivableBillDTO> receivableBillQuery(ReceivableQueryDTO queryDTO) {
    return  request.receivableBillQuery(queryDTO);
  }
}
