package com.xhgj.srm.api.dto.supplierOrder;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/9/19
 */
@Data
public class LogisticsInformationParam {
  @ApiModelProperty("供应商订单单据id")
  @NotBlank
  private String id;
  @ApiModelProperty("物流单号")
  @NotBlank
  private String expressNo;
  /**
   * 物流公司编码，srm 小程序会新增无物流公司的信息，此处去除必填检验
   */
  @ApiModelProperty("物流公司编码")
  private String expressCode;
  @ApiModelProperty("收件人或者发件人手机号，顺丰快递必须要有手机号")
  private String phoneNumber;


}
