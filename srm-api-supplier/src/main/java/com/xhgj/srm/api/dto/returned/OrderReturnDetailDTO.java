package com.xhgj.srm.api.dto.returned;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OrderReturnDetailDTO {

    @ApiModelProperty("退货单id")
    private String id;
    @ApiModelProperty("客户订单号")
    private String orderNo;
    @ApiModelProperty("下单时间")
    private String createTime;
    @ApiModelProperty("下单平台")
    private String platform;
    @ApiModelProperty("下单金额")
    private BigDecimal price;
    @ApiModelProperty("退货金额")
    private BigDecimal refundPrice;
    @ApiModelProperty("结算金额")
    private BigDecimal settlePrice;
    @ApiModelProperty("发货进度")
    private String progress;
    @ApiModelProperty("客户名称")
    private String customer;
    @ApiModelProperty("收件人")
    private String consignee;
    @ApiModelProperty("联系方式")
    private String mobile;
    @ApiModelProperty("收件地址")
    private String address;
    @ApiModelProperty("开票状态")
    private String invoicingState;
    @ApiModelProperty("订单状态")
    private String orderState;
    @ApiModelProperty("报备单号")
    private String filingNo;
    @ApiModelProperty("验收单")
    private List<FileDTO> acceptList;
    @ApiModelProperty("取消/退货单号")
    private String orderReturnNo;
    @ApiModelProperty("申请时间")
    private String applyTime;
    @ApiModelProperty("申请人")
    private String applyMan;
    @ApiModelProperty("申请人联系方式")
    private String returnMobile;
    @ApiModelProperty("本次退货金额")
    private BigDecimal thisReturnPrice;
    @ApiModelProperty("退货/取消原因")
    private String returnReason;
    @ApiModelProperty("拒绝原因")
    private String rejectReason;
    @ApiModelProperty("退货/取消单状态")
    private String orderReturnState;
    @ApiModelProperty("退货/取消商品明细")
    private List<OrderReturnProductDetailDTO> productList;

    @ApiModelProperty("退货时间")
    private String returnTime;
    @ApiModelProperty("订单 id")
    private String orderId;



    public OrderReturnDetailDTO(OrderReturn orderReturn, String platformName){
        Order order = orderReturn.getOrder();
        this.id = orderReturn.getId();
        this.orderNo = order!=null?order.getOrderNo():"";
        this.createTime = order!=null&&order.getCreateTime() > 0 ? DateUtils.formatTimeStampToNormalDateTime(order.getCreateTime()) : "";
        this.price = order!=null&&order.getPrice()!=null?order.getPrice():BigDecimal.ZERO;
        this.refundPrice = order!=null&&order.getRefundPrice()!=null?order.getRefundPrice():BigDecimal.ZERO;
        this.platform = platformName;
        this.settlePrice = order != null ? order.getPrice().subtract(order.getRefundPrice()) : BigDecimal.ZERO;
        this.customer = StringUtils.emptyIfNull(order.getCustomer());
        this.consignee = StringUtils.emptyIfNull(order.getConsignee());
        this.mobile = StringUtils.emptyIfNull(order.getMobile());
        this.address =  StringUtils.emptyIfNull(order.getAddress());
        this.invoicingState = !StringUtils.isNullOrEmpty(order.getInvoicingState())? Constants_order.INVOICE_STATE_MAP.get(order.getInvoicingState()):"未开票";
        this.orderState = !StringUtils.isNullOrEmpty(order.getOrderState())? Constants_order.ORDER_STATE_MAP.get(order.getOrderState()):"待履约";
        this.filingNo = StringUtils.emptyIfNull(order.getFilingNo());
        //退货详情
        this.orderReturnNo = StringUtils.emptyIfNull(orderReturn.getReturnNo());
        this.applyMan = StringUtils.emptyIfNull(orderReturn.getApplyMan());
        this.applyTime = orderReturn.getApplyTime()!=null&&orderReturn.getApplyTime() > 0 ?
            DateUtils.formatTimeStampToNormalDateTime(orderReturn.getApplyTime()) : "";
        this.returnTime = orderReturn.getCreateTime()!=null&&orderReturn.getCreateTime() > 0 ?
            DateUtils.formatTimeStampToNormalDateTime(orderReturn.getCreateTime()) : "";
        this.returnMobile = StringUtils.emptyIfNull(orderReturn.getMobile());
        this.thisReturnPrice = orderReturn.getPrice()!=null?orderReturn.getPrice():BigDecimal.ZERO;
        this.returnReason =  StringUtils.emptyIfNull(orderReturn.getReason());
        this.rejectReason =  StringUtils.emptyIfNull(orderReturn.getRejectReason());
        if(Constants_order.ORDER_RETURN_TYPE.equals(orderReturn.getType())){
            this.orderReturnState = !StringUtils.isNullOrEmpty(orderReturn.getReturnState())? Constants_order.ORDER_RETURN_STATE_MAP.get(orderReturn.getReturnState()):"-";
        } else {
            this.orderReturnState = !StringUtils.isNullOrEmpty(orderReturn.getReturnState())? Constants_order.ORDER_CANCEL_STATE_MAP.get(orderReturn.getReturnState()):"-";
        }
      this.orderId = order!=null?order.getId(): StrUtil.EMPTY;
    }

}
