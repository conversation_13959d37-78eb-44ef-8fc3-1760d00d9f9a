package com.xhgj.srm.api.dto.account;

import com.xhgj.srm.api.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-28 9:27
 */
@Data
public class AccountOpenInvoiceParams {


  @ApiModelProperty("对账单 id ")
  @NotBlank(message = "对账单 id 必传")
  private String orderAccountId;
  @ApiModelProperty("供应商 id ")
  @NotBlank(message = "供应商 id 必传")
  private String supplierId;
  private List<InvoiceParams> invoiceParams;

  @ApiModelProperty("被删除的发票id")
  private List<String> deletedIds;
  @ApiModelProperty("被删除的发票附件id")
  private List<String> deletedFileIds;
  @Data
  public static class InvoiceParams {
    @ApiModelProperty("发票id")
    private String invoiceId;
    @ApiModelProperty("发票号")
    @NotBlank(message = "发票号 必传")
    private String invoiceNum;
    @ApiModelProperty("发票代码")
    private String invoiceCode;
    @ApiModelProperty("开票时间")
    private Long invoiceTime;
    @ApiModelProperty("含税金额")
    private BigDecimal price;
    @ApiModelProperty("物流公司")
    @NotBlank(message = "物流公司不能为空")
    private String logisticsCompany;
    @ApiModelProperty("物流单号")
    @NotBlank(message = "物流单号不能为空")
    private String logisticsNum;
    @ApiModelProperty("发票附件")
    @NotEmpty(message = "发票附件 必传")
    @NotNull(message = "发票附件 必传")
    private List<String> fileIdList;
    @ApiModelProperty("发票附件")
    private List<FileDTO> fileList;
  }
}
