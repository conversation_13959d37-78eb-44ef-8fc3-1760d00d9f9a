package com.xhgj.srm.api.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class OrderInvoicePageQuery {

  @ApiModelProperty("方案 id")
  private String schemeId;

  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("订单状态")
  private String orderState;
  @ApiModelProperty("下单平台")
  private String platform;
  @ApiModelProperty("下单金额")
  private String price;
  @ApiModelProperty("客户名称")
  private String customer;
  @ApiModelProperty("下单时间 - 范围开始时间")
  private String orderStartTime;
  @ApiModelProperty("下单时间 - 范围结束时间")
  private String orderEndTime;
  @ApiModelProperty("开始时间")
  private String createTimeBegin;
  @ApiModelProperty("结束时间")
  private String createTimeEnd;
  @ApiModelProperty("开票状态")
  private List<String> invoicingState;
  @ApiModelProperty("发票类型")
  private String invoiceType;
  @ApiModelProperty("发票抬头")
  private String title;
  @ApiModelProperty("申请人")
  private String enterPriseName;
  @ApiModelProperty("发票申请单号")
  private String invoiceApplicationNum;

  @ApiModelProperty("派单时间 - 范围开始时间")
  private String dispatchStartTime;
  @ApiModelProperty("派单时间 - 范围结束时间")
  private String dispatchEndTime;

  @ApiModelProperty("发票号码")
  private String invoiceNum;

  @ApiModelProperty(value = "当前页", example = "1")
  private Integer pageNo;
  @ApiModelProperty(value = "每页展示数量", example = "10")
  private Integer pageSize;

}
