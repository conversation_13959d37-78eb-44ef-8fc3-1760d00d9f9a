package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.BusinessDetailDto;
import com.xhgj.srm.api.dto.BusinessUpdateParam;
import com.xhgj.srm.api.dto.CooperationAgreementAddParam;
import com.xhgj.srm.api.dto.CooperationAgreementAddParams;
import com.xhgj.srm.api.dto.CooperationAgreementListData;
import com.xhgj.srm.api.dto.SupplierChangeData;
import com.xhgj.srm.api.dto.SupplierDetailFile;
import com.xhgj.srm.api.dto.SupplierLicenseAddParam;
import com.xhgj.srm.api.dto.SupplierLicenseData;
import com.xhgj.srm.api.dto.order.OrderPlatformAndItemFieldVo;
import com.xhgj.srm.api.dto.supplier.CooperationAgreementDataDTO;
import com.xhgj.srm.dto.supplier.OrderSupplierDTO;
import com.xhgj.srm.dto.supplier.SupplierStatusDTO;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName SupplierController
 * Create by Liuyq on 2021/6/1 9:51
 **/
@RestController
@RequestMapping("supplier")
@Api(tags = {"供应商接口"})
@Slf4j
public class SupplierController {


    @Autowired
    SupplierService supplierService;


    @ApiOperation(value = "获取工商信息", notes = "获取工商信息")
    @ApiImplicitParams({
    })
    @GetMapping(value = "/getMenuInfo")
    public ResultBean<BusinessDetailDto> getMenuInfo(
            @RequestParam("supplierId") String supplierId
    ) {
        return new ResultBean<>(supplierService.getBusinessInfo(supplierId));
    }

    @ApiOperation(value = "修改工商信息", notes = "修改工商信息")
    @ApiImplicitParams({
    })
    @PostMapping(value = "/updateBusinessInfo")
    public ResultBean<Boolean> updateBusinessInfo(
            @RequestBody @Valid BusinessUpdateParam updateParam
    ) {
        supplierService.updateBusinessInfo(updateParam);
        return new ResultBean<>();
    }


    @ApiOperation(value = "获取资质证照信息", notes = "获取资质证照信息")
    @ApiImplicitParams({
    })
    @GetMapping(value = "/getLicenceBySupplier")
    @Deprecated
    public ResultBean<SupplierLicenseData> getLicenceBySupplier(
            @RequestParam("supplierId") String supplierId
    ) {
        return new ResultBean<>(supplierService.getLicenceBySupplier(supplierId));
    }

    @ApiOperation(value = "获取资质证照信息 v4.0.2", notes = "获取资质证照信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "supplierId",value = "供应商 id ")
    })
    @GetMapping(value = "/getSupplierDetailFileList")
    public ResultBean<List<SupplierDetailFile>> getSupplierDetailFileList(
        @RequestParam("supplierId") String supplierId
    ){
        return new ResultBean<>(supplierService.getSupplierDetailFileList(supplierId));
    }

    @ApiOperation(value = "获取合作协议信息", notes = "获取合作协议信息")
    @ApiImplicitParams({
    })
    @GetMapping(value = "/getCooperationAgreementBySupplier")
    @Deprecated
    public ResultBean<CooperationAgreementListData> getCooperationAgreementBySupplier(
            @RequestParam("supplierId") String supplierId
    ) {
        return new ResultBean<>(supplierService.getCooperationAgreementBySupplier(supplierId));
    }

    @ApiOperation(value = "获取合作协议（v4.0.2）", notes = "获取合作协议信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "supplierId",value = "供应商 id")
    })
    @GetMapping(value = "/getCooperationAgreementDataDTOList")
    public ResultBean<List<CooperationAgreementDataDTO>> getCooperationAgreementDataDTOList(
        @NotBlank(message = "供应商 id 必传") @RequestParam String supplierId){
        return new ResultBean<>(supplierService.getCooperationAgreementDataDTOList(supplierId));
    }

    @ApiOperation(value = "新增修改资质证照/合作协议信息", notes = "新增修改资质证照/合作协议信息")
    @ApiImplicitParams({
    })
    @PostMapping(value = "/addOrUpdateLicenceBySupplier")
    public ResultBean<Boolean> addOrUpdateLicenceBySupplier(
            @RequestBody @Valid SupplierLicenseAddParam param
    ) {
        supplierService.addOrUpdateLicenceBySupplier(param);
        return new ResultBean<>();
    }


    @ApiOperation(value = "新增合作协议信息", notes = "新增合作协议信息")
    @ApiImplicitParams({
    })
    @PostMapping(value = "/addcooperationAgreementBySupplier")
    @Deprecated
    public ResultBean<Boolean> addcooperationAgreementBySupplier(
            @RequestBody @Valid CooperationAgreementAddParam param
    ) {
        supplierService.addcooperationAgreementBySupplier(param);
        return new ResultBean<>();
    }


    @ApiOperation(value = "新增合作协议信息 v4.0.2", notes = "新增合作协议信息")
    @PostMapping(value = "/addCooperationAgreementBySupplierInGroupId",consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResultBean<Boolean> addCooperationAgreementBySupplierInGroupId(@RequestBody @Valid CooperationAgreementAddParams param){
        supplierService.addCooperationAgreementBySupplierInGroupId(param);
        return new ResultBean<>(true);
    }


    @ApiOperation(value = "分页获取工商信息变更列表", notes = "分页获取工商信息变更列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "supplierId ", value = "供应商id"),
            @ApiImplicitParam(name = "pageNo", value = "当前页", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页展示数量", defaultValue = "10")
    })
    @GetMapping(value = "/getSupplierChangeInfoPage")
    public ResultBean<PageResult<SupplierChangeData>> getSupplierChangeInfoPage(
            String supplierId ,
            @RequestParam(defaultValue = "1") String pageNo,
            @RequestParam(defaultValue = "10") String pageSize
    ) {
        return new ResultBean<>(supplierService.getSupplierChangeInfoPage(supplierId ,pageNo, pageSize));
    }


    @ApiOperation(value = "获取派单供应商列表", notes = "获取派单供应商列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
    })
    @GetMapping("/getOrderSupplierList")
    public ResultBean<List<OrderSupplierDTO>> getOrderSupplierList(
            String enterpriseName
    ) {
      log.error("接口已迁移至portal");
      throw new CheckException("接口已迁移至portal");
    }

    @ApiOperation(value = "分页获取派单供应商", notes = "分页获取派单供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
            @ApiImplicitParam(name = "platform", value = "平台"),
            @ApiImplicitParam(name = "status", value = "履约生效状态"),
            @ApiImplicitParam(name = "pageNo", value = "页数"),
            @ApiImplicitParam(name = "pageSize", value = "每页展示数量"),
    })
    @GetMapping("/getOrderSupplierPage")
    public ResultBean<PageResult<SupplierStatusDTO>> getOrderSupplierPage(
        String enterpriseName,
        String platform,
        String status,
        Integer pageNo,
        Integer pageSize
    ) {
      log.error("接口已迁移至portal");
      throw new CheckException("接口已迁移至portal");
    }

  @ApiOperation(value = "获取派单供应商", notes = "获取派单供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
      @ApiImplicitParam(name = "platform", value = "平台"),
  })
  @GetMapping("/getOrderSuppliers")
  public ResultBean<List<SupplierStatusDTO>> getOrderSuppliers(
      String enterpriseName,
      String platform
  ) {
    log.error("接口已迁移至portal");
    throw new CheckException("接口已迁移至portal");
  }
  @ApiOperation(value = "分页获取履约信息已生效派单供应商", notes = "分页获取履约信息已生效派单供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
      @ApiImplicitParam(name = "platform", value = "平台"),
      @ApiImplicitParam(name = "pageNo", value = "页数"),
      @ApiImplicitParam(name = "pageSize", value = "每页展示数量"),
  })
  @GetMapping("/getTakeEffectOrderSupplierPage")
  public ResultBean<PageResult<OrderSupplierDTO>> getTakeEffectOrderSupplierPage(
      String enterpriseName,
      String platform,
      Integer pageNo,
      Integer pageSize
  ) {
    return new ResultBean<>(supplierService.getTakeEffectOrderSupplierPage(enterpriseName,platform,pageNo,pageSize));
  }


    @ApiOperation(value = "获取供应商履约金额", notes = "获取供应商履约金额")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "supplierId", value = "供应商id"),
    })
    @GetMapping("/getSupplierOrderPrice")
    public ResultBean<BigDecimal> getSupplierOrderPrice(
        String supplierId,
        String platform
    ) {
      log.error("接口已迁移至portal");
      throw new CheckException("接口已迁移至portal");
    }

  @ApiOperation(value = "根据供应商id获取供应商的可接单平台 - 仅为后台供应商配置生效的平台")
  @GetMapping(value = "getExclusivePlatformListBySupplierId")
  public ResultBean<List<OrderPlatformDTO>> getExclusivePlatformListBySupplierId(String supplierId) {
    return new ResultBean<>(supplierService.getPlatformListBySupplierId(supplierId));
  }

  @ApiOperation(value = "新增落地商履约信息")
  @GetMapping("addLandingMerchantPerformanceInfo")
  public ResultBean<Boolean> addLandingMerchantPerformanceInfo() {
    supplierService.addLandingMerchantPerformanceInfo();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "根据供应商id获取供应商的可接单平台 - 包含项目字段信息")
  @GetMapping(value = "platformAndItemField")
  public ResultBean<List<OrderPlatformAndItemFieldVo>> getOrderPlatformAndItemField(String supplierId) {
    return new ResultBean<>(supplierService.getOrderPlatformAndItemField(supplierId));
  }
}
