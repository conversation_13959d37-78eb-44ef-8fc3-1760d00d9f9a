package com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.supplierOrder.ReturnProductDTO;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName SupplierOrderDTO */
@Data
@NoArgsConstructor
public class SupplierOrderReturnDetailDTO extends BaseSupplierOrderDetailDTO {
  @ApiModelProperty("退货单id")
  private String id;

  @ApiModelProperty("订单单id")
  private String supplierOrderId;

  @ApiModelProperty("退货单编号")
  private String returnNumber;

  @ApiModelProperty("退货时间")
  private String returnTime;

  @ApiModelProperty("退款金额")
  private BigDecimal returnPrice;

  @ApiModelProperty("状态 （3--ERP 审核 3--ERP 驳回 5--退货中 6- 已退货 7 --已撤销）")
  private String state;

  @ApiModelProperty("状态名称")
  private String stateToName;

  @ApiModelProperty("物料明细")
  private List<ReturnProductDTO> returnProductDTOList;

  public SupplierOrderReturnDetailDTO(
      SupplierOrderToForm supplierOrderToForm,
      SupplierOrder supplierOrder,
      List<ReturnProductDTO> returnProductDTOList) {
    super(supplierOrder);
    this.id = supplierOrderToForm.getId();
    this.supplierOrderId = supplierOrderToForm.getSupplierOrderId();
    this.returnNumber = StrUtil.emptyIfNull(supplierOrderToForm.getNumbers());
    this.returnTime =
        ObjectUtil.isNotEmpty(supplierOrderToForm.getTime()) && supplierOrderToForm.getTime() > 0
            ? DateUtil.format(
                new Date(supplierOrderToForm.getTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.returnPrice =
        supplierOrderToForm.getReturnPrice();
    this.state = StrUtil.emptyIfNull(supplierOrderToForm.getStatus());
    this.stateToName = SupplierOrderFormStatus.findValueByStatus(supplierOrderToForm.getStatus()).getDesc();
    this.returnProductDTOList = returnProductDTOList;
  }
}
