package com.xhgj.srm.api.service;

import com.xhgj.srm.jpa.entity.CustomerReceivable;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.math.BigDecimal;

/**
 * Created by Geng Shy on 2023/9/25
 */
public interface CustomerReceivableService extends BootBaseService<CustomerReceivable, String> {
  /**
   * @param orderId 订单id
   * @return 订单大票回款金额总计
   */
  BigDecimal getAmountCount(String orderId);
}
