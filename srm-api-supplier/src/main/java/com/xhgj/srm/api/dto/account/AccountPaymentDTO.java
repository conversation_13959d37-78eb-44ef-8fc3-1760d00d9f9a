package com.xhgj.srm.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @date 2023/6/27
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AccountPaymentDTO {
  @ApiModelProperty("可录入发票")
  private Long openInvoiceCount;
  @ApiModelProperty(value = "可付款订单总计")
  private Long  payableOrdersTotal;
  @ApiModelProperty(value = "被驳发票单总计")
  private Long  rejectInvoiceCount;
  @ApiModelProperty(value = "供应商订单可录入发票")
  private Long supplierOrderOpenInvoiceCount;


}
