package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierUser.SupplierUserSource;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @ClassName SupplierLicenseAddParam
 * Create by Liuyq on 2021/6/3 17:32
 **/
@Data
public class SupplierUserAddParam {
    @NotBlank(message = "供应商id不能为空")
    @ApiModelProperty("供应商id")
    private String supplierId;
    @NotBlank(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String name;
    @NotBlank(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String realName;
    @Email(message = "邮箱格式有误")
    @NotBlank(message = "邮箱不能为空")
    @ApiModelProperty("邮箱")
    private String mail;
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^[\\d\\*]{7,12}$", message = "联系电话输入有误")
    @ApiModelProperty("手机号码")
    private String mobile;

    public SupplierUser buildSupplierUser(Supplier supplier,String password, String createMan) {
      SupplierUser supplierUser = new SupplierUser();
      supplierUser.setSupplier(supplier);
      supplierUser.setSupplierId(supplier == null ? null : supplier.getId());
      supplierUser.setName(name);
      supplierUser.setRealName(realName);
      supplierUser.setPassword(password);
      supplierUser.setMail(mail);
      supplierUser.setMobile(mobile);
      supplierUser.setCreateTime(System.currentTimeMillis());
      supplierUser.setUpdateTime(System.currentTimeMillis());
      supplierUser.setSource(SupplierUserSource.FRONTEND.getCode());
      supplierUser.setCreateMan(createMan);
      supplierUser.setRole(Constants.FRONT_USER_ROLE_ORDINARY);
      supplierUser.setState(Constants.STATE_OK);
      String permission = supplierUser.makePermission(supplier);
      supplierUser.setPermission(permission);
      return supplierUser;
    }
}
