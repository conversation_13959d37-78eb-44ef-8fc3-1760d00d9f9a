package com.xhgj.srm.api.dto.product;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.Product;
import com.xhgj.srm.jpa.entity.ProductExternalLink;
import com.xhgj.srm.vo.product.ExternalLinkVO;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.ObjectUtils;

@Data
public class ProductDetailDTO {

  @ApiModelProperty("物料类型")
  private String srmProductType;
  @ApiModelProperty("统一路径")
  private String path;
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("临时编码")
  //字段名与mdm保持一致
  private String code;
//  private String tempCode;
  @ApiModelProperty("物料名称")
  private String name;
  @ApiModelProperty("市场价")
  private Double marketPrice;

  @ApiModelProperty("供货价")
  private Double supplyPrice;

  @ApiModelProperty("型号")
  private String model;
  @ApiModelProperty("基本单位")
  private String basicUnit;
  @ApiModelProperty("单位名称")
  private String basicUnitName;
  @ApiModelProperty("条形码")
  private String barCode;
  @ApiModelProperty("毛重")
  private String grossWeight;
  @ApiModelProperty("净重")
  private String netWeight;
  @ApiModelProperty("长")
  private String length;
  @ApiModelProperty("宽")
  private String width;
  @ApiModelProperty("高")
  private String height;
  @ApiModelProperty("体积")
  private String volume;
  @ApiModelProperty("描述")
  //名称与mdm保持一致
  private String remark;
  //  private String des;

  @ApiModelProperty("图文信息")
  private String info;
  @ApiModelProperty("供应商id")
  private String supplierId;
  @ApiModelProperty("品牌mdmId")
  private String brandMdmId;
  @ApiModelProperty("品牌英文名")
  private String brandNameEn;
  @ApiModelProperty("品牌中文名")
  private String brandNameCn;
  @ApiModelProperty("四级类目id")
  private String fourthCateMdmId;
  @ApiModelProperty("类目名称")
  private String cateName;
  @ApiModelProperty("货主")
  private String owner;
  @ApiModelProperty("货主名称")
  private String ownerName;
  @ApiModelProperty("发货期")
  private String deliveryDay;
  @ApiModelProperty("是否包邮")
  private String isTransferPriceFreightIncluded;

  @ApiModelProperty("是否停市")
  private String isDelisting;

  @ApiModelProperty("起订量")
  //名称保持一致
  private String moq;
//  private String orderQuantity;
  @ApiModelProperty("商品mdmId")
  private String productMdmId;
  @ApiModelProperty("上架项目/下单平台")
  private List<PlatformAndFieldDTO> projectValueInfoList;
//  private List<PlatformAndFieldDTO> platformAndFields;
  @ApiModelProperty("是否含检测费")
  private String testingFee;
  @ApiModelProperty("是否含包装费")
  private String packingExpense;
  @ApiModelProperty("图片关系")
  private String pictureRelationship;
  @ApiModelProperty("外部链接")
  private List<ExternalLinkVO> externalLinks;
  @ApiModelProperty("质量证明")
  private List<MDMFileDTO> certificateOfQuality;
  @ApiModelProperty("详情图")
  private List<String> detailPicList;
//  private List<ProductFile> detailedPictures;
  /**
   * 扩展属性
   */
  @ApiModelProperty("扩展属性")
  private List<ExpandData> fieldList;
  /**
   * 宣传资料
   */
  @ApiModelProperty("宣传资料")
  //名称一致
  private List<MDMFileDTO> fileList;
//  private List<ProductFile> brochure;
  /**
   * 检测报告
   */
  @ApiModelProperty("检测报告")
    private List<MDMFileDTO> testReportFileList;
    /** 主图 */
    @ApiModelProperty("主图")
    private List<ProductFile> mainUrl;

  /**
   * 税收分类编码
   */
  @ApiModelProperty("税收分类编码")
  private String taxCategoryCode;

  /**
   * 税收分类名称
   */
  @ApiModelProperty("税收分类名称")
  private String taxCategoryName;

  /**
   * 税率字段
   */
  @ApiModelProperty("税率")
  private String taxCategoryRate;

  /**
   * 税收分类简称
   */
  @ApiModelProperty("税收分类简称")
  private String taxCategoryAbbr;

  public ProductDetailDTO(Product pro, String baseUrl, List<ProductExternalLink> links) {
    List<ExternalLinkVO> externalLinks =
        links.stream().map(MapStructFactory.INSTANCE::toExternalLinkVO)
            .collect(Collectors.toList());
    this.externalLinks = externalLinks;
        //外部链接
        this.testingFee = pro.getIsDetected();
    this.packingExpense = pro.getIsPack();
    this.pictureRelationship =
        ObjectUtils.isEmpty(pro.getPictureType()) ? "" : pro.getPictureType().toString();
    this.srmProductType = pro.getProductType();
    this.path = baseUrl;
    this.id = pro.getId();
    this.name = pro.getName();
    this.code = pro.getTempCode();
    this.model = StringUtils.emptyIfNull(pro.getModel());
    this.basicUnit = pro.getBasicUnit();
    this.basicUnitName = pro.getUnitName();
    this.barCode = StrUtil.emptyIfNull(pro.getBarCode());
    this.brandNameCn = StrUtil.emptyIfNull(pro.getBrandnameCn());
    this.brandNameEn = StrUtil.emptyIfNull(pro.getBrandnameEn());
    this.marketPrice = pro.getMarketPrice();
    this.supplyPrice = pro.getPurchasePrice();
    this.deliveryDay = StrUtil.emptyIfNull(pro.getDeliveryDate());
    this.remark = StrUtil.emptyIfNull(pro.getDes());
    this.brandMdmId = StringUtils.emptyIfNull(pro.getBrandMdmId());
    this.fourthCateMdmId = StringUtils.emptyIfNull(pro.getFourthCateMdmId());
    this.grossWeight = StringUtils.emptyIfNull(pro.getGrossWeight());
    this.height = StringUtils.emptyIfNull(pro.getHeight());
    this.width = StringUtils.emptyIfNull(pro.getWidth());
    this.length = StringUtils.emptyIfNull(pro.getLength());
    this.volume = StringUtils.emptyIfNull(pro.getVolume());
    this.info = StringUtils.emptyIfNull(pro.getInfo());
    this.isTransferPriceFreightIncluded = StringUtils.emptyIfNull(pro.getIsFreeShip());
    this.netWeight = StringUtils.emptyIfNull(pro.getNetWeight());
    this.owner = StringUtils.emptyIfNull(pro.getOwner());
    this.ownerName = StringUtils.emptyIfNull(pro.getOwnerName());
    this.supplierId = StringUtils.emptyIfNull(pro.getSupplier().getId());
    this.isDelisting = StringUtils.emptyIfNull(pro.getSaleState());
    this.cateName = StrUtil.emptyIfNull(pro.getFourthCateName());
    this.productMdmId = StrUtil.emptyIfNull(pro.getProductMdmId());
    this.moq = StrUtil.emptyIfNull(pro.getOrderQuantity());
    this.taxCategoryCode = StrUtil.emptyIfNull(pro.getTaxCategoryCode());
    this.taxCategoryName = StrUtil.emptyIfNull(pro.getTaxCategoryName());
    this.taxCategoryRate = StrUtil.EMPTY;
    if (pro.getTaxCategoryRate() != null) {
      this.taxCategoryRate = pro.getTaxCategoryRate().stripTrailingZeros().toPlainString();
    }
    this.taxCategoryAbbr = StrUtil.emptyIfNull(pro.getTaxCategoryAbbr());
  }




}
