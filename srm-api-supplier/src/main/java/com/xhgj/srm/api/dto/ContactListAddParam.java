package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * @ClassName ContactListAddParam
 * Create by Liuyq on 2021/6/9 16:53
 **/
@Data
public class ContactListAddParam {
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private String sex;
    @ApiModelProperty("职务")
    private String duty;
    @Email(message = "邮箱格式有误")
    @ApiModelProperty("邮箱")
    private String mail;
    @NotBlank(message = "联系方式不能为空")
    @Pattern(regexp = "^[\\d\\*]{7,12}$", message = "联系电话输入有误")
    @ApiModelProperty("联系方式")
    private String phone;
    @ApiModelProperty("负责区域")
    private String area;

    public Contact bulidContact(Supplier supplier, SupplierUser supplierUser) {
        Contact contact = new Contact();
        contact.setSupplier(supplier);
        contact.setSupplierId(supplier.getId());
        contact.setName(name);
        contact.setSex(sex);
        contact.setDuty(duty);
        contact.setMail(mail);
        contact.setPhone(phone);
        contact.setArea(area);
        contact.setCreateMan(supplierUser.getId());
        contact.setState(Constants.STATE_OK);
        return contact;
    }
}
