package com.xhgj.srm.api.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.OrderOpenInvoice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-02-03 11:11
 */
@Data
@NoArgsConstructor
public class BaseOpenInvoiceDTO {
  @ApiModelProperty("开票信息 id")
  private String openInvoiceId;

  @ApiModelProperty("发票号")
  @NotBlank(message = "发票号 必传")
  private String invoiceNum;
  @ApiModelProperty("发票代码")
  private String invoiceCode;

  @ApiModelProperty("开票日期")
  @NotNull(message = "开票日期 必传")
  private Long openTime;

  @ApiModelProperty("含税金额")
  @NotNull(message = "含税金额 必传")
  private BigDecimal taxPrice;

  @ApiModelProperty("物流公司")
  @NotBlank(message = "物流公司 必传")
  private String logisticsCompany;

  @ApiModelProperty("物流单号")
  @NotBlank(message = "物流单号 必传")
  private String logisticsNum;

  public BaseOpenInvoiceDTO(OrderOpenInvoice orderOpenInvoice) {
    this.openInvoiceId = StrUtil.emptyIfNull(orderOpenInvoice.getId());
    this.invoiceNum = StrUtil.emptyIfNull(orderOpenInvoice.getInvoiceNum());
    this.invoiceCode = StrUtil.emptyIfNull(orderOpenInvoice.getInvoiceCode());
    this.openTime = orderOpenInvoice.getInvoiceTime();
    this.taxPrice = orderOpenInvoice.getPrice();
    this.logisticsCompany = StrUtil.emptyIfNull(orderOpenInvoice.getLogisticsCompany());
    this.logisticsNum = StrUtil.emptyIfNull(orderOpenInvoice.getLogisticsNum());
  }
}
