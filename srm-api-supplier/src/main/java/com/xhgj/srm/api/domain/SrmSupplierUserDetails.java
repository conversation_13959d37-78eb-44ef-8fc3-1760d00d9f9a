package com.xhgj.srm.api.domain;

import com.xhgj.srm.jpa.entity.SupplierUser;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;


/**
 * <AUTHOR>
 * @since 2020/9/15 16:51
 */

public class SrmSupplierUserDetails implements UserDetails {
    private SupplierUser supplierUser;

    public SrmSupplierUserDetails(SupplierUser supplierUser) {
        this.supplierUser = supplierUser;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getPassword() {
        return supplierUser.getPassword();
    }

    @Override
    public String getUsername() {
        return supplierUser.getName();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return false;
    }

    public SupplierUser supplierUser() {
        return supplierUser;
    }
}
