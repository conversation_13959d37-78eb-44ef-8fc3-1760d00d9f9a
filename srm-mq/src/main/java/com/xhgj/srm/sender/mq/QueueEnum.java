package com.xhgj.srm.sender.mq;

import lombok.Getter;

/**
 * <AUTHOR> tx = topic exchange fx = fanout exchange dx = direct exchange
 * @since 2020/3/9 9:31
 */
@Getter
public enum QueueEnum {

  /**
   * 处理批量任务
   *
   * @deprecated 逐步迁移至 QUEUE_MISSION_CREATED
   */
  QUEUE_BATCH_TASK_HANDLE(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_TASK_HANDLE,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_TASK_HANDLE),
  /**
   * 任务创建队列<br>
   * (因 srm-batch 中针对任务中心的消息正在<b>逐步迁移（目前优先导出类任务）</b>至 srm-mission-consumer
   * 项目：为了迁移不影响测试环境与生产环境其他任务的正常使用，这里声明新的队列，且在迁移完成后弃用 {@link QueueEnum#QUEUE_BATCH_TASK_HANDLE} 队列）
   */
  QUEUE_MISSION_CREATED(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_MISSION_CREATED,
      Constants_MQ.QUEUE_NAME_MQ_MISSION_CREATED),
  /** 任务完成通知 */
  QUEUE_BATCH_TASK_DONE(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_TASK_DONE,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_TASK_DONE),

  /** 任务完成通知(管理端) */
  QUEUE_BATCH_MANAGE_TASK_DONE(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_MANAGE_TASK_DONE,
      Constants_MQ.QUEUE_NAME_MQ_BATCH_MANAGE_TASK_DONE),

  /** 发送到crm系统，签收凭证信息 */
  QUEUE_SRM_TO_CRM_RECEIPT_VOUCHER(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_SRM_TO_CRM_RECEIPT_VOUCHER,
      Constants_MQ.QUEUE_NAME_MQ_SRM_TO_CRM_RECEIPT_VOUCHER),
  /** 创建采购价格库消息 */
  QUEUE_PURCHASE_INFO_RECORD_CREATE(
      Constants_MQ.EXCHANGE_NAME_PURCHASE_INFO_RECORD,
      Constants_MQ.QUEUE_NAME_MQ_PURCHASE_INFO_RECORD_CREATE,
      Constants_MQ.QUEUE_NAME_MQ_PURCHASE_INFO_RECORD_CREATE),

  /** 发送到OMS系统，签收凭证信息 */
  QUEUE_SRM_TO_OMS_RECEIPT_VOUCHER(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_MQ_SRM_TO_OMS_RECEIPT_VOUCHER,
      Constants_MQ.QUEUE_NAME_MQ_SRM_TO_OMS_RECEIPT_VOUCHER),

  /** 生成客户开票申请时发消息给OMS */
  QUEUE_SRM_TO_OMS_SUBMIT_ORDER_INVOICE(
      Constants_MQ.EXCHANGE_NAME_BATCH_TASK,
      Constants_MQ.QUEUE_NAME_SRM_TO_OMS_SUBMIT_ORDER_INVOICE,
      Constants_MQ.QUEUE_NAME_SRM_TO_OMS_SUBMIT_ORDER_INVOICE),
  ;

  /** 交换名称 */
  private final String exchange;

  /** 队列名称 */
  private final String name;

  /** 路由键 */
  private final String routeKey;

  QueueEnum(String exchange, String name, String routeKey) {
    this.exchange = exchange;
    this.name = name;
    this.routeKey = routeKey;
  }
}
