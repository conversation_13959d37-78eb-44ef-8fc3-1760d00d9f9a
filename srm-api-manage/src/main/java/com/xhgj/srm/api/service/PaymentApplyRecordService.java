package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.financial.voucher.UpdateAdvanceReversalParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordDetailVO;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageExportParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageParam;
import com.xhgj.srm.api.dto.payment.apply.record.PaymentApplyRecordPageVO;
import com.xhgj.srm.jpa.dto.DrawApplyAddParams;
import com.xhgj.srm.jpa.dto.DrawApplyDetailDTO;
import com.xhgj.srm.jpa.dto.PaymentAdvanceAddParams;
import com.xhgj.srm.jpa.dto.PaymentAdvanceDTO;
import com.xhgj.srm.jpa.dto.PaymentAdvanceDetailDTO;
import com.xhgj.srm.jpa.dto.payment.apply.record.PaymentApplyStatistics;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;

public interface PaymentApplyRecordService extends BootBaseService<PaymentApplyRecord, String> {

  /**
   * @param applyType 申请类型
   * @param applyMan 申请人
   * @param applyState 申请状态
   * @param orderNos 订单号
   * @param invoiceNos 发票号
   */
  PaymentApplyRecord create(String applyType, String applyMan, String applyState, List<String> orderNos,
      List<String> invoiceNos, String groupCode, Boolean initialOrder);

  /**
   * 分页查询
   */
  PageResult<PaymentApplyRecordPageVO> getPage(PaymentApplyRecordPageParam param);

  /**
   * 申请记录统计
   * @param form
   * @return
   */
  PaymentApplyStatistics getStatistics(PaymentApplyRecordPageParam form);

  /**
   * 申请记录统计
   * @param form
   * @return
   */
  PaymentApplyStatistics getStatisticsRef(PaymentApplyRecordPageParam form);

  /**
   * @param id 申请记录id
   * @param financialVouchers 财务凭证集合
   * @param userId 用户id
   */
  void update(String id, List<FinancialVoucher> financialVouchers, String userId);

  /**
   * @param id 申请记录id
   */
  PaymentApplyRecordDetailVO getDetails(String id);

  /**
   * 回显预付申请信息
   * @param orderId
   * @return
   */
  PaymentAdvanceDTO getPaymentAdvance(String orderId);

  /**
   * 新增修改预付申请
   * @param params
   */
  void addOrUpdatePaymentApplyRecord(PaymentAdvanceAddParams params);

  /**
   * 预付申请详情
   * @param id
   * @return
   */
  PaymentAdvanceDetailDTO getPaymentAdvanceDetail(String id);

  /**
   * 新增修改提款申请
   */
  void addOrUpdateDrawApply(DrawApplyAddParams params);

  /**
   * 提款详情
   * @param id
   * @return
   */
  DrawApplyDetailDTO getDrawApplyDetail(String id);

  /**
   * 放弃申请
   * @param id
   */
  void giveUpApply(String id);

  /**
   * 同步sap付款状态
   * @return
   */
  Boolean syncSapPayState();

  /**
   * @param applyManName 申请人姓名
   * @return 预付款凭证冲销申请
   */
  PaymentApplyRecord addAdvanceReversalApply(String applyManName, String purchaseOrderNo,
      String financialVoucherId, String reversalNotes);

  /**
   * 修改预付申请冲销申请
   */
  void updateAdvanceReversal(UpdateAdvanceReversalParam param);

  /**
   * @param financialVoucherId 财务凭证id
   * @return 预付款凭证冲销申请审批状态
   */
  String getAdvanceReversalState(String financialVoucherId);

  /**
   *  查询采购订单是否有审核中或驳回的预付申请
   * @param purchaseOrderCode 订单号
   */
  Boolean checkPurchaseOrderAdvancePayment(String purchaseOrderCode);
  /**
   * 根据凭证类型，返回订单关联金额
   * @param supplierOrderCode 采购订单
   * @param type 凭证类型
   * @return
   */
  BigDecimal getRelatedAmountByType(String supplierOrderCode,String type);


  /**
   * 付款申请导出
   */
  void exportApplyRecordExcel(PaymentApplyRecordPageExportParam form);

  /**
   * 付款申请导出数量
   * @param param
   * @return
   */
  Long exportApplyRecordExcelCount(PaymentApplyRecordPageExportParam param);
}
