package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/2 16:35
 */
@Data
public class SupplierDetailExtraFile {
    private String uid;
    private String name;
    private String url;
    private String realname;

    public SupplierDetailExtraFile(ExtraFile extraFile, String baseUrl) {
        this.uid = extraFile.getId();
        String fileName = StrUtil.emptyIfNull(extraFile.getName());
        String fileUrl = StrUtil.emptyIfNull(extraFile.getUrl());
        if (!StringUtils.isNullOrEmpty(fileUrl) && !fileUrl.contains("srm/")) {
            fileUrl = "srm" + fileUrl;
        }
        this.name = !StringUtils.isNullOrEmpty(fileName) && !StringUtils.isNullOrEmpty(fileUrl) ? fileName + ";" + fileUrl : "";
        this.url = !StringUtils.isNullOrEmpty(fileUrl) ? baseUrl + fileUrl : "";
        this.realname = extraFile.getRelationName();
    }
}
