package com.xhgj.srm.api.service.impl;

import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.api.dto.CheckFrontCheckedSupplierDTO;
import com.xhgj.srm.api.dto.CheckFrontEditSupplierDTO;
import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.CheckService;
import com.xhgj.srm.api.service.ContactService;
import com.xhgj.srm.api.service.SupplierFbService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.CheckDao;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.entity.Check;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.CheckRepository;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CheckServiceImpl implements CheckService {

  @Autowired private CheckRepository checkRepository;

  @Autowired private SupplierDao supplierDao;

  @Autowired private SupplierService supplierService;

  @Autowired private BrandService brandService;

  @Autowired private ContactService contactService;

  @Autowired private CheckDao checkDao;

  @Autowired private ContactDao contactDao;

  @Autowired private CheckService checkService;

  @Autowired private SupplierFbService supplierFbService;

  @Autowired private UserService userService;

  @Autowired private SearchSchemeDao searchSchemeDao;

  @Override
  public BootBaseRepository<Check, String> getRepository() {
    return checkRepository;
  }

  @Override
  public PageResult<CheckFrontEditSupplierDTO> getEditCheckSupplierPage(
      String useGroup,
      String enterpriseName,
      String uscc,
      String corporate,
      String brands,
      String type,
      String startTime,
      String endTime,
      String userId,
      String schemeId,
      int pageNo,
      int pageSize) {
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_FRONT);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          uscc = StrUtil.blankToDefault(uscc,searchJo.containsKey("uscc") ? searchJo.getString(
              "uscc") : "");
          enterpriseName =
              StrUtil.blankToDefault(enterpriseName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString("enterpriseName") : "");
          corporate = StrUtil.blankToDefault(corporate,searchJo.containsKey("corporate") ?
              searchJo.getString("corporate") : "");
          type =StrUtil.blankToDefault( type,searchJo.containsKey("type") ? searchJo.getString(
              "type") : "");
          brands = StrUtil.blankToDefault(brands,searchJo.containsKey("brands") ?
              searchJo.getString("brands") : "");
          startTime =
              StrUtil.blankToDefault( startTime,searchJo.containsKey("applyStartDate") ?
                  searchJo.getString("applyStartDate") : "");
          endTime =StrUtil.blankToDefault( endTime,searchJo.containsKey("applyEndDate") ?
              searchJo.getString("applyEndDate") : "");
        }
      }
    }
    Page page =
        supplierDao.getEditCheckSupplierPage(
            useGroup,
            enterpriseName,
            uscc,
            corporate,
            brands,
            type,
            startTime,
            endTime,
            userId,
            pageNo,
            pageSize);
    List<CheckFrontEditSupplierDTO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (!(pageNo > totalPages)) {
      List<String> supplierIdList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (String s : supplierIdList) {
        Supplier supplier = supplierService.get(s);
        CheckFrontEditSupplierDTO data = new CheckFrontEditSupplierDTO(supplier);
        data.setBrand(brandService.buildBrandNameStrBySupplier(supplier.getId(), "|"));
        Contact con =
            contactService.getCurContactBySid(
                supplier.getId(), StrUtil.emptyIfNull(supplier.getMobile()));
        if (con != null) {
          data.setContact(StrUtil.emptyIfNull(con.getName()));
        }
        pageDataList.add(data);
      }
    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  @Override
  public PageResult<CheckFrontCheckedSupplierDTO> getFrontCheckedSupplierPage(
      String useGroup,
      String applyStartTime,
      String applyEndTime,
      String enterpriseName,
      String uscc,
      String corporate,
      String brands,
      String type,
      String checkMan,
      String startTime,
      String endTime,
      String userId,
      String schemeId,
      int pageNo,
      int pageSize) {
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_FRONT);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          uscc = StrUtil.blankToDefault(uscc,searchJo.containsKey("uscc") ? searchJo.getString(
              "uscc") : "");
          enterpriseName =
              StrUtil.blankToDefault(enterpriseName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString("enterpriseName") : "");
          corporate = StrUtil.blankToDefault(corporate,searchJo.containsKey("corporate") ?
              searchJo.getString("corporate") : "");
          type = StrUtil.blankToDefault(type,searchJo.containsKey("type") ? searchJo.getString(
              "type") : "");
          brands = StrUtil.blankToDefault(brands,searchJo.containsKey("brands") ?
              searchJo.getString("brands") : "");
          checkMan = StrUtil.blankToDefault(checkMan,searchJo.containsKey("checkMan") ?
              searchJo.getString("checkMan") : "");
          startTime =
              StrUtil.blankToDefault(startTime,searchJo.containsKey("auditStartDate") ?
                  searchJo.getString("auditStartDate") : "");
          endTime = StrUtil.blankToDefault(endTime,searchJo.containsKey("auditEndDate") ?
              searchJo.getString("auditEndDate") : "");
          applyStartTime =
              StrUtil.blankToDefault(applyStartTime,searchJo.containsKey("applyStartDate") ?
                  searchJo.getString("applyStartDate") : "");
          applyEndTime =
              StrUtil.blankToDefault(applyEndTime,searchJo.containsKey("applyEndDate ") ?
                  searchJo.getString(
                  "applyEndDate ") : "");
        }
      }
    }
    Page<String> page =
        checkDao.getFrontCheckedPage(
            useGroup,
            applyStartTime,
            applyEndTime,
            enterpriseName,
            uscc,
            corporate,
            brands,
            type,
            checkMan,
            startTime,
            endTime,
            userId,
            pageNo,
            pageSize);
    List<CheckFrontCheckedSupplierDTO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (!(pageNo > totalPages)) {
      List<String> checkIdList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (String s : checkIdList) {
        Check ck = checkService.get(s);
        SupplierFb supplierfb = supplierFbService.get(ck.getRelationId());
        CheckFrontCheckedSupplierDTO data = new CheckFrontCheckedSupplierDTO(supplierfb);
        User user = userService.get(ck.getUserId());
        data.setCheckMan(
            user != null && !StringUtils.isNullOrEmpty(user.getRealName())
                ? user.getRealName()
                : "");
        data.setReason(!StringUtils.isNullOrEmpty(ck.getDescription()) ? ck.getDescription() : "");
        data.setCheckTime(
            ck.getCreateTime() > 0
                ? DateUtils.formatTimeStampToNormalDateTime(ck.getCreateTime())
                : "");
        data.setCkId(ck.getId());
        data.setBrand(brandService.buildBrandNameStrBySupplierFb(supplierfb.getId(), "|"));
        data.setCheckState(ck.getOperaType());
        Contact con =
            contactDao.getCurContactByFbid(
                supplierfb.getId(), StrUtil.emptyIfNull(supplierfb.getMobile()));
        if (con != null) {
          data.setContact(StrUtil.emptyIfNull(con.getName()));
        }
        pageDataList.add(data);
      }
    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }
}
