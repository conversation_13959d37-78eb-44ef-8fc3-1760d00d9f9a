package com.xhgj.srm.api.dto.inventorySafety;/**
 * @since 2025/2/20 10:11
 */

import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class InventorySafetyExportForm extends InventorySafetySearchForm {
  /**
   * 勾选的ids
   */
  private List<String> ids;

  @Override
  public Map<String, Object> toQueryMap(MergeUserPermission searchPermission) {
    Map<String, Object> queryMap = super.toQueryMap(searchPermission);
    queryMap.put("ids", ids);
    return queryMap;
  }
}
