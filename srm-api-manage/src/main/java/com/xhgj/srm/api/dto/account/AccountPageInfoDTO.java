package com.xhgj.srm.api.dto.account;

import com.xhgj.srm.jpa.entity.OrderAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-02-23 18:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AccountPageInfoDTO extends BaseAccountDTO{

  @ApiModelProperty("对账单 id ")
  private String id;

  @ApiModelProperty("对账金额")
  private BigDecimal price;

  @ApiModelProperty("提交时间")
  private Long commitTime;

  @ApiModelProperty("审核时间")
  private Long assessTime;

  @ApiModelProperty("订单数")
  private Integer orderCount;

  @ApiModelProperty("关联平台")
  private String platformNames;

  public AccountPageInfoDTO(OrderAccount orderAccount) {
    super(orderAccount);
    this.id = orderAccount.getId();
    this.price = orderAccount.getPrice();
    this.commitTime = orderAccount.getCommitTime();
    this.assessTime = orderAccount.getAssessTime();
    this.orderCount = orderAccount.getOrderCount();
  }
}
