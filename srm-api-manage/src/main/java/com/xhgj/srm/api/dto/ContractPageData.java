package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.entity.Contract;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ContractPageData Create by Liuyq on 2021/3/22 15:20
 */
@Data
public class ContractPageData {

  private String id;
  private String supplier;
  private String useGroup;
  private String code;
  private String contractNum;
  private String contractMoney;
  private String contractTime;
  private String supplierLevel;
  private String createTime;
  private String uploadTime;
  private String createman;

  @ApiModelProperty("负责采购")
  private String purchaserName;

  @ApiModelProperty("负责采购部门")
  private String deptName;

  private String isfile;
  private String isupload;
  private String isEdit;
  private List<SupplierDetailFile> filelist;
  private List<SupplierDetailFile> bcfilelist;
  private String state;



  public ContractPageData(Contract contract, User userByErpId) {
    this.id = contract.getId();
    this.supplier = contract.getSupplierName();
    this.useGroup =
        contract.getSupplier() != null
                && !StringUtils.isNullOrEmpty(contract.getSupplier().getUseGroup())
            ? contract.getSupplier().getUseGroup()
            : "";
    this.code = contract.getCode();
    this.contractNum = contract.getContractNum();
    this.contractMoney = contract.getContractMoney();
    this.contractTime =
        contract.getContractTime() > 0
            ? DateUtils.formatTimeStampToNormalDate(contract.getContractTime())
            : "";
    this.supplierLevel =
        contract.getSupplier() != null
                && !StringUtils.isNullOrEmpty(contract.getSupplier().getEnterpriseLevel())
            ? SupplierLevelEnum.getAbbrByCode(contract.getSupplier().getEnterpriseLevel())
            : "";
    this.createTime =
        contract.getCreateTime() > 0
            ? DateUtils.formatTimeStampToNormalDate(contract.getCreateTime())
            : "";
    this.uploadTime =
        contract.getUploadTime() != null && contract.getUploadTime() > 0
            ? DateUtils.formatTimeStampToNormalDate(contract.getUploadTime())
            : "";
    this.createman =
        !StringUtils.isNullOrEmpty(contract.getCreateUser()) ? contract.getCreateUser() : "";
    this.purchaserName =
        !StringUtils.isNullOrEmpty(contract.getPurchaserName()) ? contract.getPurchaserName() : "";
    this.state = "0".equals(contract.getIsFile()) ? "关闭" : "正常";
  }
}
