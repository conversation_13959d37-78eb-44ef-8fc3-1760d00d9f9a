package com.xhgj.srm.api.dto.financial.voucher.paymentApply;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * 添加付款申请表单
 * todo （延长申请、加急申请、冻结申请、解冻申请、预付申请冲销）整合业务处理
 */
@Data
public class AddPaymentApplyBaseForm {

  /**
   * 付款申请id(编辑时候传)
   */
  @ApiModelProperty("付款申请id,编辑时候传")
  private String id;

  /**
   * 需要提交的财务凭证集合
   */
  @NotEmpty(message = "请选择需要冲销的预付款凭证")
  private List<String> financialVoucherIds;

  /**
   * 用户id
   */
  @NotBlank(message = "用户id不能为空")
  private String userId;
}
