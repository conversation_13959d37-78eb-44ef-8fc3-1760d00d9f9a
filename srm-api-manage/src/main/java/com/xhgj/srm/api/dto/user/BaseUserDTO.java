package com.xhgj.srm.api.dto.user;

import com.xhgj.srm.api.dto.CheckRelationDTO;
import com.xhgj.srm.common.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName BaseUserDTO */
@Data
@NoArgsConstructor
@ApiModel("用户对象")
public abstract class BaseUserDTO {
  @ApiModelProperty(value = "用户id，修改时必传")
  private String id;

  @ApiModelProperty(value = "姓名", required = true)
  @NotEmpty(message = "姓名不能为空")
  private String name;

  @ApiModelProperty(value = "联系方式")
  private String mobile;

  @ApiModelProperty(value = "邮箱")
  private String mail;

  @ApiModelProperty(value = "erp编码", required = true)
  @NotEmpty(message = "erp编码不能为空")
  private String erpCode;

  @ApiModelProperty(value = "erpId", required = true)
  @NotEmpty(message = "erpId不能为空")
  private String erpId;

  @ApiModelProperty(value = "mdmId")
  @NotEmpty(message = "mdmId不能为空")
  private String mdmId;

  @ApiModelProperty(value = "角色-可多选使用,分割", required = true)
  @NotEmpty(message = "角色不能为空")
  private String role;

  @ApiModelProperty(value = "是否授权至当前系统")
  private String isAllowToSystem;

  @ApiModelProperty(value = "审核关系")
  private List<CheckRelationDTO> checkRelationDTOList;

  @ApiModelProperty("合同数据范围 1-为负责人，2-所在部门，3-所在部门及下级部门，4-所在组织")
  private String contractDataScope;

  @ApiModelProperty("询价数据范围 1-为报价人，2-所在部门，3-所在部门及下级部门，4-所在组织")
  private String inquiryDataScope;

  @ApiModelProperty("拉黑供应商数据范围 1-自己为负责人，2-自己所在部门，3-下级部门，4-所在组织")
  private String blockSupplier;

  @ApiModelProperty("修改供应商数据范围 1-自己为负责人，2-自己所在部门，3-下级部门，4-所在组织")
  private String updateSupplier;

  @ApiModelProperty("供应商订单数据范围 1-为采购员 4-所在组织")
  private String supplierOrderDataScope;

  @ApiModelProperty("采购申请数据范围 1-为采购员 4-所在组织")
  private String purchaseApplyDataScope;

  @ApiModelProperty("采购申请修改权限范围 1-为采购员，2-所在部门，3-所在部门及下级部门，4-所在组织, 5-不允许操作")
  /**
   * {@link com.xhgj.srm.common.enums.purchase.order.PurchaseApplyOperationPermissionsEnum}
   */
  private String purchaseApplyOperationPermissions;

  @ApiModelProperty("采购价格库数据范围 2-所在部门，3-所在部门及下级部门，4-所在组织")
  private String priceLibrary;

  @ApiModelProperty("进项票数据范围 1-为采购员，2-所在部门，3-所在部门及下级部门，4-所在组织")
  private String invoiceDataScope;

  @ApiModelProperty("退换货订单数据范围 1-为采购员 4-所在组织")
  private String returnExchangeDataScope;

  @ApiModelProperty("0:不允许，1：允许")
  private String importPriceLibrary;

  @ApiModelProperty("0:不允许，1：允许")
  private String exportApplyForOrder;

  @ApiModelProperty("0:不允许，1：允许")
  private String exportSupplierOrder;

  @ApiModelProperty("0:不允许，1：允许")
  private String updateLeader;

  @ApiModelProperty("0:不允许，1：允许")
  private String inventorySafety;


  @ApiModelProperty("0:不允许，1：允许")
  private String allOrgOrders;

  /**
   * 财务凭证列表导出权限
   * {@link Constants#USER_PERMISSION_EXPORT_FINANCIAL_VOUCHER
   * @link Constants#EXPORT_MAP}
   */
  @ApiModelProperty("0-不允许，1-允许,2-仅允许导出本部门")
  private String exportFinancialVoucher;
  /**
   * 付款申请导出权限
   * {@link Constants#USER_PERMISSION_EXPORT_PAYMENT_APPLY
   * @link Constants#EXPORT_MAP}
   */
  @ApiModelProperty("0-不允许，1-允许,2-仅允许导出本部门")
  private String exportPaymentApply;
  /**
   *  入库/退库单导出权限
   * {@link Constants#USER_PERMISSION_EXPORT_WAREHOUSE_RETURN
   * @link Constants#EXPORT_MAP}
   */
  @ApiModelProperty("0-不允许导出，1-导出全部，2-所在部门，3-为采购员")
  private String exportWarehouseAndReturnOrder;
  /**
   * 库存列表导出权限
   */
  @ApiModelProperty("0-不允许，1-允许,2-仅允许导出本部门")
  private String exportInventory;
  /**
   * 退换货订单列表导出权限
   */
  @ApiModelProperty("0-不允许导出，1-导出全部，2-所在部门，3-为采购员")
  private String exportReturnExchange;
  /**
   * 组装拆卸单导出权限
   */
  @ApiModelProperty("0-不允许导出，1-导出全部，2-所在部门，3-为创建人")
  private String exportAssembleDisassemble;

  /**
   * 进项票导出 6.7.0 注释
   */
//  @ApiModelProperty("0-不允许导出，1-导出全部，2-所在部门，3-为采购员")
//  private String exportInputInvoice;
}

