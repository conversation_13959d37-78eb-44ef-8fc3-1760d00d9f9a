package com.xhgj.srm.api.controller.v2Mix;/**
 * @since 2025/2/10 15:38
 */

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.api.constants.ConstantsLockByUserForType;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewCancelForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewReturnForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderCount;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderFillDTO;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderFillFile;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSaveForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSearchForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.UnCancelForm;
import com.xhgj.srm.api.service.ReturnExchangeOrderService;
import com.xhgj.srm.api.vo.returnExchange.NewReturnVO;
import com.xhgj.srm.api.vo.returnExchange.ReturnExchangeVO;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.utils.returnExchangeOrder.OrderNumberCleanerAndGenerator;
import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.jpa.dto.returnExchange.ReturnExchangeStatistics;
import com.xhgj.srm.request.dto.sap.SapInventoryQueryForm;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.mvc.lock.BootLockByUserFor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/2/10 15:38:48
 *@description
 */
@RestController
@RequestMapping("/returnExchangeOrder")
@Validated
@Api(tags = {"退换货单管理"})
@Slf4j
public class ReturnExchangeOrderController {
  @Resource
  private ReturnExchangeOrderService returnExchangeOrderService;
  @Resource
  private SAPService sapService;
  @Resource
  private RedissonClient redissonClient;

  /**
   * 保存退换货单
   * @param saveForm
   * @return
   */
  @PostMapping(value = "")
  @BootLockByUserFor(ConstantsLockByUserForType.SAVE_RETURN_EXCHANGE_ORDER)
  public ResultBean<String> addPurchaseOrder(@RequestBody @Valid ReturnExchangeSaveForm saveForm) {
    return new ResultBean<>(returnExchangeOrderService.saveRefundExchangeOrder(saveForm));
  }

  /**
   * 查询退换货单列表
   */
  @GetMapping(value = "")
  public ResultBean<PageResult<ReturnExchangeListVO>> getPageList(ReturnExchangeSearchForm form) {
    return new ResultBean<>(returnExchangeOrderService.getPage(form));
  }

  /**
   * 查询退换货单统计
   */
  @GetMapping(value = "/statistics")
  public ResultBean<ReturnExchangeStatistics> getStatistics(ReturnExchangeSearchForm form) {
    return new ResultBean<>(returnExchangeOrderService.getStatistics(form));
  }

  /**
   * 查询退换货单数量统计
   */
  @GetMapping(value = "/count")
  public ResultBean<ReturnExchangeOrderCount> getCount(ReturnExchangeSearchForm form) {
    return new ResultBean<>(returnExchangeOrderService.getCount(form));
  }


  /**
   * 退换货订单详情
   */
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "退换货单id", required = true, dataType = "String", paramType = "path")
  })
  @GetMapping(value = "/{id}")
  public ResultBean<ReturnExchangeVO> getDetail(@PathVariable("id") @NotBlank(message = "退换货单id必须传值") String id) {
    return new ResultBean<>(returnExchangeOrderService.getDetail(id));
  }

  /**
   * 退换货订单删除
   */
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "退换货单id", required = true, dataType = "String", paramType = "path")
  })
  @DeleteMapping(value = "/{id}")
  public ResultBean<Boolean> delete(@PathVariable("id") @NotBlank(message = "退换货单id必须传值") String id) {
    returnExchangeOrderService.delete(id);
    return new ResultBean<>(true);
  }

  /**
   * 下载需要导入退换货单的物料信息
   */
  @PostMapping(value = "/detail/download")
  public ResponseEntity<byte[]> download(@RequestBody List<ReturnExchangeOrderFillDTO> fillDTOList,
      HttpServletResponse response) throws IOException {
    response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
    response.setHeader("Content-disposition",
        "attachment;filename=" + System.currentTimeMillis() + ".xlsx");
    response.flushBuffer();
    return ResponseEntity.ok()
        .body(returnExchangeOrderService.downloadReturnExchangeOrderFill(fillDTOList));
  }

  /**
   * 批量导入退换货单的物料信息
   */
  @PostMapping(value = "/detail/fill")
  public ResultBean<List<ReturnExchangeOrderFillDTO>> fill(@ModelAttribute ReturnExchangeOrderFillFile fillFile) throws IOException {
    return new ResultBean<>(returnExchangeOrderService.fill(fillFile.getFile()));
  }

  /**
   * 查询退换货库存列表
   */
  @ApiImplicitParams({
      @ApiImplicitParam(name = "productCode", value = "物料编码", required = true, dataType = "String"),
      @ApiImplicitParam(name = "userGroup", value = "用户组织", required = true, dataType = "String"),
      @ApiImplicitParam(name = "batchNo", value = "批次号", required = false, dataType = "String"),
      @ApiImplicitParam(name = "isOnlyPriceZero", value = "是否只展示未税单价为0", required = true, dataType = "Boolean")
  })
  @GetMapping(value = "/stock")
  public ResultBean<List<SapInventoryVO>> stock(
      @RequestParam("productCode") @NotBlank(message = "物料编码必须传值") String productCode,
      @RequestParam(value = "userGroup")  String userGroup,
      @RequestParam(value = "batchNo", required = false)  String batchNo,
      @RequestParam(value = "isOnlyPriceZero")@NotNull(message = "是否只展示未税单价为0的必须传值")  Boolean isOnlyPriceZero
  ) {
    SapInventoryQueryForm form = new SapInventoryQueryForm();
    form.setProductCode(productCode);
    form.setBatchNo(batchNo);
    form.setUserGroup(userGroup);
    List<SapInventoryVO> sapInventoryVOList = sapService.sapInventoryQuery(form);
    if (BooleanUtil.isTrue(isOnlyPriceZero)) {
      //只展示未税单价为0的数据
      List<SapInventoryVO> resultList = sapInventoryVOList.stream().filter(
          item -> item.getUnitPrice() != null && NumberUtil.equals(item.getUnitPrice(), BigDecimal.ZERO)).collect(Collectors.toList());
      return new ResultBean<>(resultList);
    }
    return new ResultBean<>(sapInventoryVOList);
  }

  /**
   * 退库单列表查询
   */
  @GetMapping(value = "/return")
  public ResultBean<List<NewReturnVO>> getReturnList(
      @RequestParam("supplierOrderId") @NotBlank(message = "关联supplierOrderId不能为空") String supplierOrderId) {
    return new ResultBean<>(returnExchangeOrderService.getReturnList(supplierOrderId));
  }


  /**
   * 新增退库单
   */
  @PostMapping(value = "/return")
  public ResultBean<Boolean> addReturnOrder(@RequestBody @Valid AddNewReturnForm saveForm) {
    RLock lock = null;
    RLock lockGroup = null;
    try {
      lock = redissonClient.getLock(Constants_LockName.PURCHASE_ORDER_RETURN + saveForm.getSupplierOrderId());
      lockGroup = redissonClient.getLock(Constants_LockName.LOCK_GROUP_PURCHASE_ADD_RETURN_AND_REVERSAL + saveForm.getSupplierOrderId());
      lock.lock();
      lockGroup.lock();
      returnExchangeOrderService.addRefundOrder(saveForm);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error("新增退库单异常", e);
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
      if (lockGroup != null) {
        lockGroup.unlock();
      }
    }
    return new ResultBean<>(true);
  }


  @ApiOperation(value = "新增取消单")
  @PostMapping(value = "/cancel")
  public ResultBean<Boolean> addCancelOrder(@RequestBody @Valid AddNewCancelForm cancelForm) {
    returnExchangeOrderService.addCancelOrder(cancelForm);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "反取消")
  @PostMapping(value = "/unCancel")
  public ResultBean<Boolean> unCancelOrder(@RequestBody @Valid UnCancelForm unCancelForm) {
    returnExchangeOrderService.unCancelOrder(unCancelForm);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "退换货订单导出")
  @PostMapping(value = "/export")
  public ResultBean<Boolean> export(@RequestBody ReturnExchangeSearchForm form) {
    returnExchangeOrderService.export(form);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "查询退换货订单预计导出数量")
  @PostMapping(value = "/export/count")
  public ResultBean<Long> exportCount(@RequestBody ReturnExchangeSearchForm form) {
    return new ResultBean<>(returnExchangeOrderService.exportCount(form));
  }


  @PostMapping("/clear")
  public ResultBean<Boolean> clear() {
    OrderNumberCleanerAndGenerator.INSTANCE.clearYesterdayOrderNumbers(LocalDate.now());
    return new ResultBean<>(true);
  }

}
