package com.xhgj.srm.api.dto.account;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.OrderAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-02-23 19:26
 */
@Data
@NoArgsConstructor
public class BaseAccountDTO {

  @ApiModelProperty("对账单号")
  private String accountNo;

  @ApiModelProperty("对账单状态")
  private String accountStatus;

  @ApiModelProperty("提交人")
  private String createSupplier;

  @ApiModelProperty("对账开票状态")
  private String accountOpenInvoiceStatus;

  public BaseAccountDTO(OrderAccount orderAccount) {
    this.accountNo = orderAccount.getAccountNo();
    this.accountStatus = orderAccount.getAccountState();
    this.createSupplier = orderAccount.getCreateSupplier();
    String accountOpenInvoiceStatus1 = orderAccount.getAccountOpenInvoiceStatus();
    this.accountOpenInvoiceStatus =
        StrUtil.isNotBlank(StrUtil.emptyIfNull(orderAccount.getAccountOpenInvoiceStatus()))?
            accountOpenInvoiceStatus1: Constants.ORDER_INVOICE_STATE_NOT_DONE;
  }
}
