package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.dto.account.OrderInfoDTO;
import com.xhgj.srm.dto.account.ProductInfoDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderInfoDTO;
import com.xhgj.srm.dto.order.invoice.OrderSupplierInvoiceDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
  *@ClassName OrderInvoiceRelationDetailDTO
  *<AUTHOR>
  *@Date 2023/8/14 14:44
*/
@Data
public class OrderInvoiceRelationDetailDTO {
  /**
   * 供应商名称
   */
  @ApiModelProperty("供应商名称")
  private String supplierName;

  /**
   * 下单平台
   */
  @ApiModelProperty("下单平台")
  private String platform;

  /**
   * 1 审核中 2 暂存 3 通过 4 驳回
   */
  @ApiModelProperty("1 审核中 2 暂存 3 通过 4 驳回")
  private String invoiceState;

  /**
   * 应处理人
   */
  @ApiModelProperty("应处理人")
  private String operator;

  /**
   * 审核人
   */
  @ApiModelProperty("审核人")
  private String auditor;

  /**
   * 审核时间
   */
  @ApiModelProperty("审核时间")
  private Long examineTime;

  /**
   * 创建时间
   */
  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("增值税发票号")
  private String addInvoiceNum;

  @ApiModelProperty("erp应付单号")
  private String erpPayableNo;

  @ApiModelProperty("应付金额")
  private BigDecimal erpPayableMoney;

  @ApiModelProperty("驳回理由")
  private String rejection;

  @ApiModelProperty("业务负责人")
  private String businessLeader;

  @ApiModelProperty("对接助理")
  private String dockingAssistant;

  @ApiModelProperty("订单明细")
  private List<OrderInfoDTO> orderInfoList;

  @ApiModelProperty("供应商订单明细")
  private List<SupplierOrderInfoDTO> supplierOrderInfoList;

  @ApiModelProperty("物料明细")
  private List<ProductInfoDTO> productInfoList;

  @ApiModelProperty("发票列表")
  private List<OrderSupplierInvoiceDTO> invoiceList;

  @ApiModelProperty("订单发票核对")
  private List<OrderAndInvoiceDTO> orderAndInvoiceDTOS;

  @ApiModelProperty("发票价税合计")
  private BigDecimal invoiceTotalAmountIncludingTax;

  @ApiModelProperty("订单价税合计")
  private BigDecimal orderTotalAmountIncludingTax;

  @ApiModelProperty("是否是新订单")
  private Boolean isNew;
  @ApiModelProperty("物流公司")
  private String logisticsCompany;
  @ApiModelProperty("物流单号")
  private String logisticsNum;

  /** 源单类型 */
  @ApiModelProperty("源单类型")
  private String orderSource;

  /**
   * sap发票凭证号
   */
  @ApiModelProperty("sap发票凭证号")
  private String  invoiceVoucherNumber;

  /**
   * sap会计年度
   */
  @ApiModelProperty("sap会计年度")
  private String  accountingYear;

  /**
   * sap财务凭证号
   */
  @ApiModelProperty("sap财务凭证号")
  private String  financialVouchers;

  /**
   * 进项票来源
   */
  @ApiModelProperty("进项票来源")
  private String source;

  /**
   * 进项票来源描述
   */
  @ApiModelProperty("进项票来源描述")
  private String sourceValue;

  @ApiModelProperty("sap冲销会计年度")
  private String offsetAccountingYear;

  @ApiModelProperty("sap冲销日期")
  private Long reversalDate;

  @ApiModelProperty("sap财务冲销凭证号")
  private String reversalVoucherNo;

  @ApiModelProperty("sap发票冲销凭证号")
  private String invoiceOffsetVoucherNo;
  /**
   * 是否为一次性供应商
   */
  @ApiModelProperty("是否为一次性供应商")
  private Boolean oneTimeSupplier;

  @ApiModelProperty("采购组织")
  private String purchasingOrganization;

  @Data
  public static class OrderAndInvoiceDTO {
    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("id")
    private String productId;

    @ApiModelProperty("商品编码")
    private String productCode;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("商品名称")
    private String productName;

    @ApiModelProperty("型号")
    private String model;

    @ApiModelProperty("数量")
    private BigDecimal productCount;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("单价")
    private BigDecimal productPrice;

    @ApiModelProperty("去税单价")
    private BigDecimal  taxFreeCbPrice;

    @ApiModelProperty("客户订单号")
    private String orderNo;

    @ApiModelProperty("规格")
    private String specification;


    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNumber;

    /**
     * 商品名称
     */
    @ApiModelProperty("发票商品名称")
    private String goodsName;

    /**
     * 规格
     */
    @ApiModelProperty("发票规格")
    private String standard;


    /**
     * 单位
     */
    @ApiModelProperty("发票单位")
    private String invoiceUnit;

    /**
     * 数量
     */
    @ApiModelProperty("发票数量")
    private String num;


    /**
     * 去税单价
     */
    @ApiModelProperty("发票去税单价")
    private String netValue;

    /**
     * 物料匹配
     */
    @ApiModelProperty("物料匹配")
    private String productMate;

    /**
     * 单价匹配
     */
    @ApiModelProperty("单价匹配")
    private String priceMate;

  }


}
