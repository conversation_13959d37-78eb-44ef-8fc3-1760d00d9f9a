package com.xhgj.srm.api.dto.financial.voucher;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class FinancialVoucherPageParam implements BaseDefaultSearchSchemeForm {

  @ApiModelProperty("供应商名称")
  private String supplierName;
  @ApiModelProperty("开票方")
  private String invoicingParty;
  @ApiModelProperty("采购订单号")
  private String purchaseOrderNo;
  @ApiModelProperty("sap财务凭证号")
  private String financialVoucherNo;
  @ApiModelProperty("sap会计年度")
  private String accountingYear;
  @ApiModelProperty("客户回款状态")
  private String customerCollectionState;
  @ApiModelProperty("进项发票号")
  private String invoiceOrderNo;
  @ApiModelProperty("凭证类型")
  private List<String> voucherType;
  @ApiModelProperty("凭证付款状态")
  private List<String> voucherPaymentState;
  @ApiModelProperty("预计付款日期-开始")
  private Long expectedPaymentDateStart;
  @ApiModelProperty("预计付款日期-结束")
  private Long expectedPaymentDateEnd;
  @ApiModelProperty("基准日期-开始")
  private Long baseDateStart;
  @ApiModelProperty("基准日期-结束")
  private Long baseDateEnd;
  @ApiModelProperty("方案id")
  private String schemeId;
  @ApiModelProperty("用户id")
  @NotBlank
  private String userId;
  @ApiModelProperty("排序字段")
  private String sortField;
  @ApiModelProperty("排序类型")
  private String sortType;

  @ApiModelProperty("组织编码")
  private String userGroup;
  @ApiModelProperty("预付申请冲销状态")
  private String advanceReversalState;

  @ApiModelProperty("关联付款申请单号")
  private String relevancePaymentApplicationNo;
  @ApiModelProperty("凭证金额操作类型")
  private LogicalOperatorsEnums voucherPriceOperators;
  @ApiModelProperty("凭证金额")
  private BigDecimal voucherPrice;
  @ApiModelProperty("凭证金额操作类型")
  private LogicalOperatorsEnums paymentApplyAmountOperators;
  @ApiModelProperty("凭证金额")
  private BigDecimal paymentApplyAmount;
  @ApiModelProperty("付款冻结状态")
  private String paymentFreezeStatus;
  @ApiModelProperty("付款方式")
  private String paymentType;
  @ApiModelProperty("订货金额操作类型")
  private LogicalOperatorsEnums orderPriceOperators;
  @ApiModelProperty("订货金额")
  private BigDecimal orderPrice;
  @ApiModelProperty("订货金额操作类型")
  private LogicalOperatorsEnums relatedAmountOperators;
  @ApiModelProperty("关联金额")
  private BigDecimal relatedAmount;
  @ApiModelProperty("剩余可提款金额类型")
  private LogicalOperatorsEnums remainingWithdrawableAmountOperators;
  @ApiModelProperty("剩余可提款金额")
  private BigDecimal remainingWithdrawableAmount;
  @ApiModelProperty("账期枚举")
  private String accountPeriod;
  @ApiModelProperty("SAP凭证行项目")
  private String voucherLineItems;
  @ApiModelProperty("已退款金额操作类型")
  private LogicalOperatorsEnums refundAmountOperators;
  @ApiModelProperty("已退款金额")
  private BigDecimal refundAmount;
  @ApiModelProperty("抵消预付金额操作类型")
  private LogicalOperatorsEnums offsetPrepaidAmountOperators;
  @ApiModelProperty("抵消预付金额")
  private BigDecimal offsetPrepaidAmount;
  @ApiModelProperty("已提款金额操作类型")
  private LogicalOperatorsEnums withdrawnAmountOperators;
  @ApiModelProperty("已提款金额")
  private BigDecimal withdrawnAmount;

  @ApiModelProperty("导出数据勾选的id集合")
  List<String> ids;

  @ApiModelProperty("分页参数")
  private Integer pageNo;

  @ApiModelProperty("分页参数")
  private Integer pageSize;

  public Integer getPageNo() {
    if (pageNo == null) {
      return 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null) {
      return 10;
    }
    return pageSize;
  }

}
