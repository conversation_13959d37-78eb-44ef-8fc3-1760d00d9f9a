package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.purchase.order.ProductDetailParam;
import com.xhgj.srm.api.dto.supplierorder.SaveSupplierOrderParams.ProductDetailDTO;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderProductService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:20
 */
@Service
@Slf4j
public class SupplierOrderDetailServiceImpl implements SupplierOrderDetailService {

  @Autowired private SupplierOrderDetailRepository repository;
  @Autowired private SupplierOrderProductService supplierOrderProductService;
  @Autowired private SupplierOrderToFormService supplierOrderToFormService;
  @Autowired private SupplierOrderDetailDao dao;
  @Autowired private SupplierOrderService supplierOrderService;

  @Override
  public BootBaseRepository<SupplierOrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public SupplierOrderDetail getByErpIdAndOrderToFormId(String erpId, String orderToFormId) {
    return repository.getFirstByErpIdAndOrderToFormIdAndState(
        erpId, orderToFormId, Constants.STATE_OK);
  }

  @Override
  public SupplierOrderDetail createProductOrderDetail(
      String orderProductId,
      String detailedId,
      long createTime,
      int sortNum,
      String detailedErpId) {
    Assert.notEmpty(orderProductId);
    Assert.notEmpty(detailedId);
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    supplierOrderDetail.setOrderProductId(orderProductId);
    supplierOrderDetail.setDetailedId(detailedId);
    supplierOrderDetail.setCreateTime(createTime);
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setSortNum(sortNum);
    supplierOrderDetail.setDetailedErpId(detailedErpId);
    return supplierOrderDetail;
  }

  @Override
  public void saveProductDetail(
      List<ProductDetailDTO> productDetailDTOList, String supplierOrderId) {
    Assert.notNull(productDetailDTOList);
    Assert.notEmpty(supplierOrderId);
    SupplierOrderToForm detailed =
        supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrderId);
    if (detailed == null) {
      detailed =
          supplierOrderToFormService.createSupplierOrderToForm(
              supplierOrderId, SupplierOrderFormType.DETAILED, System.currentTimeMillis());
      supplierOrderToFormService.save(detailed);
    }
    String supplierOrderToFormId = detailed.getId();
    List<String> nowErpId = new ArrayList<>();
    List<SupplierOrderDetail> supplierOrderDetails = new ArrayList<>();
    productDetailDTOList.forEach(
        productDetailDTO -> {
          Integer unitDigit = productDetailDTO.getUnitDigit();
          String productCode = productDetailDTO.getProductCode();
          if (StrUtil.isBlank(productCode)) {
            throw new CheckException("物料编码必传");
          }
          // 保存物料
          String name = productDetailDTO.getName();
          String brand = productDetailDTO.getBrand();
          String brandCode = productDetailDTO.getBrandCode();
          String manuCode = productDetailDTO.getManuCode();
          BigDecimal price = productDetailDTO.getPrice();
          String unit = productDetailDTO.getUnit();
          String unitCode = productDetailDTO.getUnitCode();
          SupplierOrderProduct supplierOrderProduct =
              Optional.ofNullable(supplierOrderProductService.getByCode(productCode))
                  .orElseGet(SupplierOrderProduct::new);
          supplierOrderProduct.setCode(productCode);
          supplierOrderProduct.setBrand(brand);
          supplierOrderProduct.setBrandCode(brandCode);
          supplierOrderProduct.setName(name);
          supplierOrderProduct.setManuCode(manuCode);
          supplierOrderProduct.setUnit(unit);
          supplierOrderProduct.setUnitDigit(unitDigit);
          supplierOrderProduct.setUnitCode(unitCode);
          supplierOrderProductService.save(supplierOrderProduct);
          // 保存物料明细
          SupplierOrderDetail supplierOrderDetail =
              saveSupplierOrderDetail(supplierOrderToFormId, nowErpId, productDetailDTO, unitDigit,
                  price, supplierOrderProduct);
          supplierOrderDetails.add(supplierOrderDetail);
        });
    try {
      SupplierOrder supplierOrder = supplierOrderService.get(supplierOrderId);
      if (supplierOrder != null) {
        supplierOrderService.erpCancel(supplierOrder, supplierOrderDetails);
      }
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
    }
    // 需要删除的行
    List<String> dbErpId = dao.getErpIdListByOrderToFormId(supplierOrderToFormId);
    // 需要删除的行
    List<String> deleteErpId =
        new ArrayList<>(CollUtil.subtract(new ArrayList<>(dbErpId), nowErpId));
    for (String erpId : deleteErpId) {
      SupplierOrderDetail supplierOrderDetail =
          repository.getFirstByErpIdAndOrderToFormIdAndState(
              erpId, supplierOrderToFormId, Constants.STATE_OK);
      if (supplierOrderDetail != null) {
        supplierOrderDetail.setState(Constants.STATE_DELETE);
        save(supplierOrderDetail);
      }
    }
  }

  private SupplierOrderDetail saveSupplierOrderDetail(String supplierOrderToFormId,
      List<String> nowErpId, ProductDetailDTO productDetailDTO, Integer unitDigit, BigDecimal price,
      SupplierOrderProduct supplierOrderProduct) {
    String rowId = productDetailDTO.getRowId();
    if (StrUtil.isBlank(rowId)) {
      throw new CheckException("行 id 必传");
    }
    nowErpId.add(rowId);
    Integer rowNum = productDetailDTO.getRowNum();
    BigDecimal num = productDetailDTO.getNum();
    BigDecimal totalPrice = productDetailDTO.getTotalPrice();
    Long deliveryTime = productDetailDTO.getDeliveryTime();
    String mark = productDetailDTO.getMark();
    BigDecimal stockInputQty = productDetailDTO.getStockInputQty();
    BigDecimal remainQty = productDetailDTO.getRemainQty();
    String salesOrderNo = productDetailDTO.getSalesOrderNo();
    String description = productDetailDTO.getDescription();
    SupplierOrderDetail supplierOrderDetail =
        Optional.ofNullable(
                repository.getFirstByErpIdAndOrderToFormIdAndState(
                    rowId, supplierOrderToFormId, Constants.STATE_OK))
            .orElseGet(
                () -> {
                  SupplierOrderDetail supplierOrderDetail1 = new SupplierOrderDetail();
                  supplierOrderDetail1.setState(Constants.STATE_OK);
                  supplierOrderDetail1.setCreateTime(System.currentTimeMillis());
                  return supplierOrderDetail1;
                });
    supplierOrderDetail.setPrice(price);
    supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
    supplierOrderDetail.setOrderToFormId(supplierOrderToFormId);
    supplierOrderDetail.setErpId(rowId);
    supplierOrderDetail.setErpRowNum(rowNum);
    supplierOrderDetail.setSortNum(rowNum);
    supplierOrderDetail.setDeliverTime(deliveryTime);
    supplierOrderDetail.setUpdateTime(System.currentTimeMillis());
    BigDecimal zero = BigDecimal.ZERO;
    BigDecimal oldShipQty = ObjectUtil.defaultIfNull(supplierOrderDetail.getShipQty(), zero);
    BigDecimal oldCancelQty =
        ObjectUtil.defaultIfNull(supplierOrderDetail.getCancelQty(), zero);
    BigDecimal oldReturnQty =
        ObjectUtil.defaultIfNull(supplierOrderDetail.getReturnQty(), zero);
    // 总数量
    supplierOrderDetail.setNum(num);
    // 待发数量 (总数量 - 已发数量 - 取消数量)
    BigDecimal waitQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(
            NumberUtil.sub(num, oldShipQty, oldCancelQty), unitDigit);
    supplierOrderDetail.setWaitQty(NumberUtil.isLess(waitQty, zero) ? zero : waitQty);
    // 采购入库数量
    supplierOrderDetail.setStockInputQty(stockInputQty);
    // 剩余入库数量
    supplierOrderDetail.setRemainQty(remainQty);
    // 实际结算数量 = erp 过来的采购入库数量
    supplierOrderDetail.setSettleQty(stockInputQty);
    // 待入库数量（已经发货的数量 - 采购入库数量 - 退货数量）
    BigDecimal waitStockInputQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(
            NumberUtil.sub(oldShipQty, stockInputQty, oldReturnQty),
            supplierOrderProduct.getUnitDigit());
    supplierOrderDetail.setWaitStockInputQty(
        NumberUtil.isLess(waitStockInputQty, zero) ? zero : waitStockInputQty);
    supplierOrderDetail.setTotalPrice(totalPrice);
    supplierOrderDetail.setMark(mark);
    supplierOrderDetail.setSalesOrderNo(salesOrderNo);
    supplierOrderDetail.setDescription(description);
    save(supplierOrderDetail);
    return supplierOrderDetail;
  }

  @Override
  public List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId) {
    return repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
        orderToFormId, Constants.STATE_OK);
  }

  @Override
  public void deleteSupplierOrderDetail(List<String> orderToFormIds) {
    if (CollUtil.isEmpty(orderToFormIds)) {
      return;
    }
    orderToFormIds.stream().filter(StrUtil::isNotBlank).forEach(id -> {
      List<SupplierOrderDetail> supplierOrderDetails =
          repository.findByOrderToFormIdAndState(id, Constants.STATE_OK);
      supplierOrderDetails.forEach(supplierOrderDetail -> {
        supplierOrderDetail.setState(Constants.STATE_DELETE);
        save(supplierOrderDetail);
      });
    });
  }

  private SupplierOrderDetail saveSupplierOrderDetail(
      String orderFormId, ProductDetailParam detailParam, String purchaseOrderId) {
    SupplierOrderDetail orderDetail = get(detailParam.getId(),
        () -> CheckException.noFindException(SupplierOrderDetail.class, detailParam.getId()));
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    supplierOrderDetail.setId(null);
    supplierOrderDetail.setOrderToFormId(orderFormId);
    supplierOrderDetail.setShipQty(detailParam.getDeliveryQty());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    SupplierOrderProduct supplierOrderProduct = orderDetail.getSupplierOrderProduct();
    supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
    supplierOrderDetail.setSupplierOrderProduct(supplierOrderProduct);
    supplierOrderDetail.setSapRowId(orderDetail.getSapRowId());
    supplierOrderDetail.setErpRowNum(orderDetail.getErpRowNum());
    supplierOrderDetail.setWaitQty(orderDetail.getWaitQty());
    supplierOrderDetail.setBatchNo(orderDetail.getBatchNo());
    supplierOrderDetail.setSapReversalRowNo(orderDetail.getSapReversalRowNo());
    supplierOrderDetail.setDetailedId(detailParam.getDetailId());
    supplierOrderDetail.setPrice(detailParam.getProductPrice());
    supplierOrderDetail.setStockInputQty(orderDetail.getShipQty());
    supplierOrderDetail.setSortNum(orderDetail.getSortNum());
    supplierOrderDetail.setPurchaseOrderId(purchaseOrderId);
    supplierOrderDetail.setInvoicableNum(orderDetail.getShipQty());
    supplierOrderDetail.setTaxRate(orderDetail.getTaxRate());
    supplierOrderDetail.setProductRate(orderDetail.getProductRate());
    String detailedId = orderDetail.getDetailedId();
    if (StrUtil.isNotBlank(detailedId)) {
      SupplierOrderDetail detailed =
          get(detailedId, () -> CheckException.noFindException(SupplierOrderDetail.class, detailedId));
      supplierOrderDetail.setTotalPrice(NumberUtil.mul(detailed.getPrice(),
          detailed.getShipQty()));
      supplierOrderDetail.setWarehouse(detailed.getWarehouse());
      supplierOrderDetail.setWarehouseName(detailed.getWarehouseName());
    }
    return save(supplierOrderDetail);
  }
  @Override
  public List<SupplierOrderDetail> saveOrderDetail(
      String orderFormId,
      List<ProductDetailParam> productDetailList, String purchaseOrderId) {
    Assert.notEmpty(orderFormId);
    Assert.notNull(productDetailList);
    return productDetailList.stream().map(
        productDetailParam -> saveSupplierOrderDetail(orderFormId,
            productDetailParam,
            purchaseOrderId)).collect(Collectors.toList());
  }

  @Override
  public List<SupplierOrderDetail> findAllByPurchaseApplyForOrderIdAndState(
      String purchaseApplyForOrderId) {
    Assert.notBlank(purchaseApplyForOrderId);
    return repository.findAllByPurchaseApplyForOrderIdAndState(purchaseApplyForOrderId,
        Constants.STATE_OK);
  }

  @Override
  public boolean existByWarehousingFormId(String warehousingFormId) {
    return CollUtil.isNotEmpty(dao.getByWarehousingFormId(warehousingFormId));
  }
  @Override
  public BigDecimal getPurchaseOrderShipQty(String purchaseId) {
    return dao.getPurchaseOrderShipQty(purchaseId);
  }

  @Override
  public SupplierOrderDetail getFormIdAndDetailedId(String formId, String detailedId) {
    return repository.getFirstByOrderToFormIdAndDetailedIdAndState(formId,detailedId,Constants.STATE_OK);
  }

  @Override
  public BigDecimal getSupplierOrderReturnQty(String supplierOrderId) {
    return dao.getSupplierOrderReturnQty(supplierOrderId);
  }
}
